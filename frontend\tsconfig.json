{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["./*"], "~backend/*": ["../backend/*"], "~backend/client": ["./client.ts"]}}, "include": ["**/*.ts", "**/*.tsx", "components/maps/types/css-modules.d.ts"], "exclude": ["node_modules"]}