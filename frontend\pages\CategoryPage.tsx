import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ArrowLeft, Star, Moon, Heart, BookOpen, Globe, Archive } from 'lucide-react';
import backend from '~backend/client';
import { But<PERSON> } from '@/components/ui/button';
import MartyrCard from '../components/MartyrCard';
import { IslamicGeometricPattern, IslamicCalligraphyBorder, MosqueDecoration, IslamicFrameBorder } from '@/components/IslamicPatterns';

export default function CategoryPage() {
  const { category } = useParams<{ category: string }>();
  const decodedCategory = category ? decodeURIComponent(category) : '';

  const { data: categoryData, isLoading, error } = useQuery({
    queryKey: ['category', decodedCategory],
    queryFn: () => backend.martyrs.getCategory({ category: decodedCategory }),
    enabled: !!decodedCategory
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden">
        <IslamicGeometricPattern className="fixed inset-0 z-0" opacity={0.03} color="#059669" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="animate-pulse">
            <div className="h-12 bg-gradient-to-r from-emerald-200 to-teal-200 rounded-2xl w-1/3 mb-8"></div>
            <div className="h-16 bg-gradient-to-r from-emerald-300 to-teal-300 rounded-3xl w-2/3 mb-12"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg h-96 overflow-hidden">
                  <div className="h-56 bg-gradient-to-br from-emerald-200 to-teal-200"></div>
                  <div className="p-6 space-y-4">
                    <div className="h-6 bg-slate-200 rounded-lg w-3/4"></div>
                    <div className="h-4 bg-slate-200 rounded w-full"></div>
                    <div className="h-4 bg-slate-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !categoryData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden flex items-center justify-center">
        <IslamicGeometricPattern className="fixed inset-0 z-0" opacity={0.03} color="#059669" />
        <div className="relative text-center">
          <IslamicFrameBorder className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl" borderColor="#059669">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Archive className="w-10 h-10 text-white" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-slate-800 mb-6">Sacred Category Not Found</h1>
            <IslamicCalligraphyBorder className="mb-6" color="#059669" />
            <p className="text-slate-600 mb-8 text-lg">The requested sacred category could not be found in our archive.</p>
            <Link to="/">
              <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-4 rounded-2xl text-lg font-semibold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
                <ArrowLeft className="w-5 h-5 mr-3" />
                Return to Sacred Home
              </Button>
            </Link>
          </IslamicFrameBorder>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden">
      {/* Enhanced Islamic Background Pattern */}
      <IslamicGeometricPattern className="fixed inset-0 z-0" opacity={0.03} color="#059669" />
      
      {/* Floating decorative elements */}
      <div className="fixed top-20 left-10 w-8 h-8 bg-emerald-400/20 rounded-full animate-pulse"></div>
      <div className="fixed top-40 right-20 w-12 h-12 bg-amber-400/15 rounded-full animate-bounce"></div>
      <div className="fixed bottom-20 left-20 w-6 h-6 bg-teal-400/25 rounded-full animate-ping"></div>

      {/* Header Section */}
      <section className="relative bg-gradient-to-br from-emerald-900 via-emerald-800 to-teal-800 text-white py-20 overflow-hidden">
        <IslamicGeometricPattern opacity={0.1} color="#ffffff" />
        
        {/* Floating Islamic elements */}
        <div className="absolute top-10 right-10 opacity-20">
          <MosqueDecoration color="#ffffff" />
        </div>
        <div className="absolute bottom-10 left-10 opacity-15">
          <Star className="w-16 h-16 text-amber-300" fill="currentColor" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Back Button */}
          <Link 
            to="/" 
            className="inline-flex items-center text-emerald-200 hover:text-white mb-8 px-6 py-3 rounded-2xl bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 transform hover:scale-105 border border-white/20"
          >
            <ArrowLeft className="w-5 h-5 mr-3" />
            Back to Sacred Home
          </Link>

          <div className="text-center">
            <div className="flex justify-center mb-8">
              <div className="relative group">
                <div className="w-24 h-24 bg-gradient-to-br from-emerald-500 via-emerald-600 to-teal-600 rounded-2xl flex items-center justify-center shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:scale-105">
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
                  <div className="relative">
                    {decodedCategory === "Shi'a IMN Martyrs" && <Heart className="w-12 h-12 text-white drop-shadow-xl" />}
                    {decodedCategory === "Marhum" && <BookOpen className="w-12 h-12 text-white drop-shadow-xl" />}
                    {decodedCategory === "Global Martyrs" && <Globe className="w-12 h-12 text-white drop-shadow-xl" />}
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-amber-400 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-emerald-100 to-teal-100 bg-clip-text text-transparent leading-tight">
              {decodedCategory}
            </h1>
            
            <IslamicCalligraphyBorder className="max-w-md mx-auto mb-6" color="#10b981" />
            
            <div className="text-xl text-emerald-200 mb-6 font-medium tracking-wide" dir="rtl">
              {decodedCategory === "Shi'a IMN Martyrs" && "شهداء الحركة الإسلامية الشيعية في نيجيريا"}
              {decodedCategory === "Marhum" && "المرحوم"}
            </div>
            
            <p className="text-xl md:text-2xl mb-8 text-emerald-100 max-w-5xl mx-auto leading-relaxed">
              {categoryData.total} sacred {categoryData.total === 1 ? 'martyr' : 'martyrs'} preserved in this blessed category
            </p>
          </div>
        </div>
      </section>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Enhanced Category Description */}
        <div className="mb-16">
          <IslamicFrameBorder className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-emerald-100" borderColor="#059669">
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Archive className="w-8 h-8 text-white" />
                </div>
              </div>
              <h2 className="text-3xl font-bold text-slate-800 mb-6">Sacred Category Description</h2>
              <IslamicCalligraphyBorder className="max-w-md mx-auto mb-6" color="#059669" />
              
              <div className="text-lg text-slate-700 leading-relaxed max-w-4xl mx-auto">
                {decodedCategory === "Shi'a IMN Martyrs" && (
                  <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-8 border-2 border-emerald-200">
                    <p>
                      Sacred martyrs from the Islamic Movement of Nigeria (IMN), a blessed Shi'a Islamic organization 
                      that has faced persecution and violence. These noble souls sacrificed their lives 
                      for their religious beliefs and the divine pursuit of justice, following the path of Ahlul Bayt.
                    </p>
                  </div>
                )}
                {decodedCategory === "Marhum" && (
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 border-2 border-blue-200">
                    <p>
                      Blessed spiritual leaders, devoted clerics, and wise religious scholars who were martyred for their faith
                      and sacred religious convictions. Their sacrifice serves as an eternal inspiration for believers
                      and advocates of religious freedom, lighting the path of divine truth.
                    </p>
                  </div>
                )}

              </div>
            </div>
          </IslamicFrameBorder>
        </div>

        {/* Enhanced Martyrs Grid */}
        {categoryData.martyrs.length === 0 ? (
          <div className="text-center py-20">
            <IslamicFrameBorder className="max-w-2xl mx-auto bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl" borderColor="#059669">
              <div className="flex justify-center mb-8">
                <div className="w-20 h-20 bg-gradient-to-br from-slate-400 to-slate-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <Archive className="w-10 h-10 text-white" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-slate-800 mb-6">No Sacred Martyrs Found</h3>
              <IslamicCalligraphyBorder className="mb-6" color="#059669" />
              <p className="text-slate-600 mb-8 text-lg leading-relaxed">
                This blessed category doesn't have any martyrs yet. Check back later as we continue 
                to expand our sacred archive with more stories of courage and sacrifice.
              </p>
              <Link to="/search">
                <Button 
                  variant="outline" 
                  className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white border-0 hover:from-emerald-700 hover:to-teal-700 px-8 py-4 rounded-2xl text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  <Archive className="w-5 h-5 mr-3" />
                  Browse All Sacred Martyrs
                </Button>
              </Link>
            </IslamicFrameBorder>
          </div>
        ) : (
          <>
            {/* Category Stats */}
            <div className="mb-12 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border-2 border-emerald-200 text-center">
                <div className="w-12 h-12 bg-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-6 h-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-emerald-800">{categoryData.total}</div>
                <div className="text-emerald-700 font-medium">Sacred Martyrs</div>
              </div>
              <div className="bg-gradient-to-br from-amber-50 to-amber-100 rounded-2xl p-6 border-2 border-amber-200 text-center">
                <div className="w-12 h-12 bg-amber-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Star className="w-6 h-6 text-white" fill="currentColor" />
                </div>
                <div className="text-2xl font-bold text-amber-800">∞</div>
                <div className="text-amber-700 font-medium">Eternal Legacy</div>
              </div>
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border-2 border-blue-200 text-center">
                <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Moon className="w-6 h-6 text-white" fill="currentColor" />
                </div>
                <div className="text-2xl font-bold text-blue-800">Blessed</div>
                <div className="text-blue-700 font-medium">Sacred Memory</div>
              </div>
            </div>

            {/* Martyrs Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {categoryData.martyrs.map((martyr) => (
                <MartyrCard key={martyr.id} {...martyr} />
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
