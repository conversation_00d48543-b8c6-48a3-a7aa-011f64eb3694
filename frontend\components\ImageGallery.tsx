import React, { useState } from 'react';
import { Chevron<PERSON>eft, ChevronRight, X, Download, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface Image {
  id: number;
  url: string;
  caption?: string;
  credit?: string;
}

interface ImageGalleryProps {
  images: Image[];
  className?: string;
}

export default function ImageGallery({ images, className = '' }: ImageGalleryProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);

  if (images.length === 0) {
    return null;
  }

  const openLightbox = (index: number) => {
    setSelectedImageIndex(index);
  };

  const closeLightbox = () => {
    setSelectedImageIndex(null);
  };

  const goToPrevious = () => {
    if (selectedImageIndex !== null) {
      setSelectedImageIndex(
        selectedImageIndex === 0 ? images.length - 1 : selectedImageIndex - 1
      );
    }
  };

  const goToNext = () => {
    if (selectedImageIndex !== null) {
      setSelectedImageIndex(
        selectedImageIndex === images.length - 1 ? 0 : selectedImageIndex + 1
      );
    }
  };

  const handleDownload = (imageUrl: string, imageName: string) => {
    // In a real implementation, this would add a watermark
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `${imageName}-martyrs-archive.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className={className}>
      {/* Gallery Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {images.map((image, index) => (
          <div
            key={image.id}
            className="aspect-square bg-gradient-to-br from-slate-100 to-slate-200 rounded-xl overflow-hidden cursor-pointer group shadow-lg hover:shadow-2xl transition-all duration-500 relative"
            onClick={() => openLightbox(index)}
          >
            <img
              src={image.url}
              alt={image.caption || `Gallery image ${index + 1}`}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <div className="w-8 h-8 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center">
                <Star className="w-4 h-4 text-amber-300" />
              </div>
            </div>
            <div className="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              {image.caption && (
                <p className="text-white text-xs font-medium truncate">
                  {image.caption}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Lightbox */}
      <Dialog open={selectedImageIndex !== null} onOpenChange={closeLightbox}>
        <DialogContent className="max-w-6xl w-full h-full max-h-[95vh] p-0 bg-black/95 backdrop-blur-md border-0">
          {selectedImageIndex !== null && (
            <div className="relative w-full h-full flex items-center justify-center">
              {/* Close Button */}
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-6 right-6 z-10 text-white hover:bg-white/20 rounded-xl backdrop-blur-md"
                onClick={closeLightbox}
              >
                <X className="w-6 h-6" />
              </Button>

              {/* Navigation Buttons */}
              {images.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute left-6 top-1/2 transform -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-xl backdrop-blur-md"
                    onClick={goToPrevious}
                  >
                    <ChevronLeft className="w-8 h-8" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-6 top-1/2 transform -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-xl backdrop-blur-md"
                    onClick={goToNext}
                  >
                    <ChevronRight className="w-8 h-8" />
                  </Button>
                </>
              )}

              {/* Download Button */}
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-6 right-20 z-10 text-white hover:bg-white/20 rounded-xl backdrop-blur-md"
                onClick={() =>
                  handleDownload(
                    images[selectedImageIndex].url,
                    `image-${selectedImageIndex + 1}`
                  )
                }
              >
                <Download className="w-5 h-5" />
              </Button>

              {/* Image */}
              <img
                src={images[selectedImageIndex].url}
                alt={images[selectedImageIndex].caption || `Gallery image ${selectedImageIndex + 1}`}
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              />

              {/* Image Info */}
              {(images[selectedImageIndex].caption || images[selectedImageIndex].credit) && (
                <div className="absolute bottom-6 left-6 right-6 bg-black/70 backdrop-blur-md text-white p-6 rounded-xl border border-white/10">
                  {images[selectedImageIndex].caption && (
                    <p className="text-lg mb-2 font-medium">{images[selectedImageIndex].caption}</p>
                  )}
                  {images[selectedImageIndex].credit && (
                    <p className="text-sm text-slate-300">
                      Credit: {images[selectedImageIndex].credit}
                    </p>
                  )}
                </div>
              )}

              {/* Image Counter */}
              {images.length > 1 && (
                <div className="absolute top-6 left-6 bg-black/70 backdrop-blur-md text-white px-4 py-2 rounded-xl text-sm font-medium border border-white/10">
                  {selectedImageIndex + 1} / {images.length}
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
