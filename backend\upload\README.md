# Upload Module

This module handles file uploads and cloud storage for the Martyr Biography Website.

## Features

- **Cloud Storage Abstraction**: Supports S3 and other cloud providers
- **Direct Uploads**: Base64 file upload for smaller files
- **Signed URLs**: Secure direct-to-cloud uploads for larger files  
- **File Management**: List, delete, and organize uploaded files
- **Security**: Admin-only access with JWT authentication
- **Development Mode**: Local storage simulation for testing

## API Endpoints

### POST /admin/upload/signed-url
Generate signed upload URL for direct frontend uploads.

**Request**:
```json
{
  "filename": "image.jpg",
  "contentType": "image/jpeg", 
  "fileSize": 1024000
}
```

### POST /admin/upload/direct
Direct file upload for smaller files (base64 encoded).

**Request**:
```json
{
  "fileData": "data:image/jpeg;base64,/9j/4AAQ...",
  "filename": "image.jpg",
  "contentType": "image/jpeg",
  "martyrId": 123
}
```

### DELETE /admin/upload/file
Delete uploaded file from storage and database.

**Request**:
```json
{
  "fileUrl": "https://storage.example.com/files/filename.jpg"
}
```

### GET /admin/upload/files
List uploaded files with optional filtering.

**Query Parameters**:
- `martyrId` - Filter by martyr ID
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)

## Database Schema

The module uses the `file_uploads` table:

```sql
CREATE TABLE file_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    original_filename TEXT NOT NULL,
    stored_filename TEXT NOT NULL,
    file_url TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type TEXT NOT NULL,
    uploaded_by TEXT NOT NULL,
    martyr_id INTEGER REFERENCES martyrs(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Configuration

Set environment variables for cloud storage:

```bash
# AWS S3 Configuration (Production)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket-name

# Development (uses local simulation)
NODE_ENV=development
```

## Usage in Other Modules

```typescript
import { deleteFile, getSignedUploadUrl } from "../upload/upload";

// Delete file when cleaning up
await deleteFile({ fileUrl: "https://..." });

// Get upload URL for frontend
const uploadUrl = await getSignedUploadUrl({
  filename: "image.jpg",
  contentType: "image/jpeg", 
  fileSize: 1024000
});
```

## Testing

1. Start backend: `encore run`
2. Test with curl or Postman
3. Check file_uploads table for records
4. Verify storage integration (S3 or local)

## File Size Limits

- Direct upload: 10MB maximum
- Signed URL upload: Configurable (default: 50MB)
- Supported formats: JPEG, PNG, WebP, GIF