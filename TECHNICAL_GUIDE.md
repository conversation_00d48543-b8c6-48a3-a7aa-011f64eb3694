# Technical Implementation Guide for AI Assistants

## Quick Reference Commands & Patterns

### Development Environment Setup
```bash
# Start development servers
cd backend && encore run                 # Backend on port 4000
cd frontend && npm run dev              # Frontend on port 5173

# Generate API client after backend changes
cd backend && encore gen client --target frontend

# Set up admin secret for local development
./scripts/setup-dev-secret.sh          # Generates secure random secret
encore secret set AdminSecret <value>  # Set via Encore CLI
```

### Common Code Patterns

#### Backend API Endpoint Pattern
```typescript
import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";

// Public endpoint
export const getPublicData = api<RequestType, ResponseType>(
  { expose: true, method: "GET", path: "/public-endpoint" },
  async (params) => {
    // Implementation
  }
);

// Admin-only endpoint
export const adminAction = api<RequestType, ResponseType>(
  { auth: true, expose: true, method: "POST", path: "/admin/action" },
  async (params) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }
    // Implementation
  }
);
```

#### Frontend API Call Pattern
```typescript
import { useQuery, useMutation } from '@tanstack/react-query';
import backend from '~backend/client';

// Query pattern
const { data, isLoading, error } = useQuery({
  queryKey: ['resource', id],
  queryFn: () => backend.martyrs.getResource({ id }),
  staleTime: 5 * 60 * 1000, // 5 minutes
});

// Mutation pattern
const mutation = useMutation({
  mutationFn: (data) => backend.martyrs.createResource(data),
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['resources'] });
  },
});
```

#### Component Structure Pattern
```typescript
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface ComponentProps {
  className?: string;
  // Other props
}

export default function Component({ className, ...props }: ComponentProps) {
  return (
    <Card className={cn("default-classes", className)}>
      <CardHeader>
        <CardTitle>Title</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Content */}
      </CardContent>
    </Card>
  );
}
```

## Database Migration Patterns

### Creating New Migration
```sql
-- File: backend/martyrs/migrations/N_description.up.sql
-- Always increment N and use descriptive names

-- Add new column
ALTER TABLE martyrs ADD COLUMN new_field TEXT;

-- Create new table with proper relationships
CREATE TABLE new_table (
  id BIGSERIAL PRIMARY KEY,
  martyr_id BIGINT REFERENCES martyrs(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_new_table_martyr_id ON new_table(martyr_id);
```

### Database Query Patterns
```typescript
// Simple query
const martyr = await martyrsDB.queryRow<MartyrType>`
  SELECT * FROM martyrs WHERE slug = ${slug}
`;

// Query with joins
const martyrWithImages = await martyrsDB.queryRow<ComplexType>`
  SELECT m.*, mi.url as profile_image
  FROM martyrs m
  LEFT JOIN martyr_images mi ON m.id = mi.martyr_id 
    AND mi.is_profile_image = true
  WHERE m.id = ${id}
`;

// Insert with returning
const newMartyr = await martyrsDB.queryRow<MartyrType>`
  INSERT INTO martyrs (name, slug, bio) 
  VALUES (${name}, ${slug}, ${bio})
  RETURNING *
`;
```

## Admin System Implementation Patterns

### Admin Authentication Setup

#### Frontend Auth Context
```typescript
// AdminAuthContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import backend from '~backend/client';

interface AdminAuthContextType {
  token: string | null;
  user: AdminUser | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

interface AdminUser {
  email: string;
  role: string;
  userID: string;
}

const AdminAuthContext = createContext<AdminAuthContextType | null>(null);

export const AdminAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [token, setToken] = useState<string | null>(localStorage.getItem('admin_token'));
  const [user, setUser] = useState<AdminUser | null>(() => {
    const saved = localStorage.getItem('admin_user');
    return saved ? JSON.parse(saved) : null;
  });
  const navigate = useNavigate();

  const login = async (credentials: { email: string; password: string }) => {
    try {
      const response = await backend.auth.login(credentials);
      setToken(response.token);
      setUser(response.user);
      localStorage.setItem('admin_token', response.token);
      localStorage.setItem('admin_user', JSON.stringify(response.user));
    } catch (error) {
      throw new Error('Login failed');
    }
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
    navigate('/admin/login');
  };

  return (
    <AdminAuthContext.Provider value={{
      token,
      user,
      login,
      logout,
      isAuthenticated: !!token
    }}>
      {children}
    </AdminAuthContext.Provider>
  );
};

export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (!context) throw new Error('useAdminAuth must be used within AdminAuthProvider');
  return context;
};
```

#### Authenticated Backend Client
```typescript
// hooks/useAuthenticatedBackend.ts
import { useMemo } from 'react';
import backend from '~backend/client';
import { useAdminAuth } from '../context/AdminAuthContext';

export const useAuthenticatedBackend = () => {
  const { token } = useAdminAuth();
  
  return useMemo(() => {
    if (!token) throw new Error('No authentication token found');
    
    return backend.with({
      auth: async () => ({ authorization: `Bearer ${token}` })
    });
  }, [token]);
};
```

### Admin Dashboard Implementation

#### Dashboard with Analytics
```typescript
// AdminDashboard.tsx
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useAuthenticatedBackend } from '../hooks/useAuthenticatedBackend';

const AdminDashboard = () => {
  const backend = useAuthenticatedBackend();
  
  // Dashboard stats
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['admin-stats'],
    queryFn: () => backend.martyrs.listMartyrs(),
  });
  
  // Views analytics
  const { data: viewsData, isLoading: viewsLoading } = useQuery({
    queryKey: ['admin-views'],
    queryFn: async () => {
      const response = await backend.martyrs.getViewsOverTime();
      return response.data.map(d => ({
        ...d,
        name: new Date(d.date).toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        })
      }));
    },
  });
  
  // Content health calculation
  const contentHealth = useMemo(() => {
    if (!stats?.martyrs) return 0;
    const totalHealth = stats.martyrs.reduce((sum, martyr) => {
      let health = 0;
      if (martyr.bio && martyr.bio.length > 100) health += 0.5;
      if (martyr.profileImage) health += 0.5;
      return sum + health;
    }, 0);
    return totalHealth / stats.martyrs.length;
  }, [stats]);
  
  return (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard 
          title="Total Martyrs" 
          value={stats?.total || 0}
          trend="+2 this month"
          icon={<Users className="h-4 w-4" />}
        />
        <StatCard 
          title="Content Health" 
          value={`${Math.round(contentHealth * 100)}%`}
          trend={`${stats?.martyrs.filter(m => m.contentHealth < 1).length || 0} need attention`}
          icon={<AlertCircle className="h-4 w-4" />}
        />
      </div>
      
      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Views Over Time</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            {viewsLoading ? (
              <div className="flex items-center justify-center h-full">
                Loading...
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={viewsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="views" fill="#16a34a" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
```

### Admin CRUD Operations

#### Martyr Form with Rich Text
```typescript
// AdminMartyrForm.tsx
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import RichTextEditor from '../components/RichTextEditor';
import { useAuthenticatedBackend } from '../hooks/useAuthenticatedBackend';

interface MartyrFormData {
  name: string;
  slug: string;
  birthDate?: string;
  birthPlace?: string;
  deathDate?: string;
  deathPlace?: string;
  martyrdomCause?: string;
  martyrdomContext?: string;
  bio: string;
  subCategories: string[];
  region?: string;
  period?: string;
  quotes: string[];
  familyInfo?: string;
  latitude?: number;
  longitude?: number;
}

const AdminMartyrForm = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const backend = useAuthenticatedBackend();
  const queryClient = useQueryClient();
  const isEdit = slug && slug !== 'new';
  
  const [formData, setFormData] = useState<MartyrFormData>({
    name: '',
    slug: '',
    bio: '',
    subCategories: [],
    quotes: []
  });
  
  // Load existing martyr for editing
  const { data: martyrData, isLoading } = useQuery({
    queryKey: ['martyr-profile', slug],
    queryFn: () => backend.martyrs.getProfile({ slug: slug! }),
    enabled: isEdit,
  });
  
  // Populate form when editing
  useEffect(() => {
    if (martyrData && isEdit) {
      setFormData({
        name: martyrData.name,
        slug: martyrData.slug,
        birthDate: formatDateForInput(martyrData.birthDate),
        birthPlace: martyrData.birthPlace || '',
        deathDate: formatDateForInput(martyrData.deathDate),
        deathPlace: martyrData.deathPlace || '',
        martyrdomCause: martyrData.martyrdomCause || '',
        martyrdomContext: martyrData.martyrdomContext || '',
        bio: martyrData.bio || '',
        subCategories: martyrData.subCategories || [],
        region: martyrData.region || '',
        period: martyrData.period || '',
        quotes: martyrData.quotes || [],
        familyInfo: martyrData.familyInfo || '',
        latitude: martyrData.latitude,
        longitude: martyrData.longitude
      });
    }
  }, [martyrData, isEdit]);
  
  // Save mutation
  const saveMutation = useMutation({
    mutationFn: async (data: MartyrFormData) => {
      if (isEdit && martyrData) {
        return backend.martyrs.updateMartyr({ id: martyrData.id, ...data });
      } else {
        return backend.martyrs.createMartyr(data);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-martyrs'] });
      navigate('/admin');
    },
  });
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    saveMutation.mutate(formData);
  };
  
  const handleFormChange = (field: keyof MartyrFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  
  // Auto-generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };
  
  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: prev.slug || generateSlug(name)
    }));
  };
  
  if (isLoading) {
    return <LoadingSpinner />;
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleNameChange(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="slug">URL Slug *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => handleFormChange('slug', e.target.value)}
                    required
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="birthDate">Birth Date</Label>
                  <Input
                    id="birthDate"
                    type="date"
                    value={formData.birthDate}
                    onChange={(e) => handleFormChange('birthDate', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="birthPlace">Birth Place</Label>
                  <Input
                    id="birthPlace"
                    value={formData.birthPlace}
                    onChange={(e) => handleFormChange('birthPlace', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Biography & Martyrdom</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="bio">Biography *</Label>
                <RichTextEditor
                  value={formData.bio}
                  onChange={(value) => handleFormChange('bio', value)}
                />
              </div>
              
              <div>
                <Label htmlFor="martyrdomContext">Martyrdom Context</Label>
                <RichTextEditor
                  value={formData.martyrdomContext}
                  onChange={(value) => handleFormChange('martyrdomContext', value)}
                />
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Sidebar */}
        <div className="lg:col-span-1 space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Publish</CardTitle>
            </CardHeader>
            <CardContent>
              <Button 
                type="submit" 
                disabled={saveMutation.isPending}
                className="w-full"
              >
                {saveMutation.isPending ? 'Saving...' : 'Save Martyr'}
              </Button>
            </CardContent>
          </Card>
          
          <CategoriesCard 
            categories={formData.subCategories}
            onChange={(categories) => handleFormChange('subCategories', categories)}
          />
          
          <QuotesCard
            quotes={formData.quotes}
            onChange={(quotes) => handleFormChange('quotes', quotes)}
          />
        </div>
      </div>
    </form>
  );
};
```

## Admin File Upload Implementation

### Image Upload with Signed URLs
```typescript
// ImageUploadForm.tsx
import React, { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { FileUpload } from './FileUpload';

interface ImageUploadFormProps {
  martyrId: number;
  onImageAdded?: (image: MartyrImage) => void;
}

interface ImageMetadata {
  caption: string;
  credit: string;
  isProfileImage: boolean;
}

export const ImageUploadForm: React.FC<ImageUploadFormProps> = ({
  martyrId,
  onImageAdded
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<{ url: string; id: string }[]>([]);
  const [imageMetadata, setImageMetadata] = useState<Record<string, ImageMetadata>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleUploadSuccess = (fileUrl: string, fileId: string) => {
    setUploadedFiles(prev => [...prev, { url: fileUrl, id: fileId }]);
    setImageMetadata(prev => ({
      ...prev,
      [fileId]: { caption: '', credit: '', isProfileImage: false }
    }));
  };

  const submitImages = async () => {
    if (uploadedFiles.length === 0) {
      toast({
        title: "No Images",
        description: "Please upload at least one image.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const backend = getAuthenticatedBackend();
      
      for (const file of uploadedFiles) {
        const metadata = imageMetadata[file.id];
        
        const savedImage = await backend.martyrs.addImage({
          martyrId,
          url: file.url,
          caption: metadata.caption || undefined,
          credit: metadata.credit || undefined,
          isProfileImage: metadata.isProfileImage,
        });
        
        onImageAdded?.(savedImage);
      }

      toast({
        title: "Images Saved",
        description: `Successfully saved ${uploadedFiles.length} image(s).`,
      });

      // Reset form
      setUploadedFiles([]);
      setImageMetadata({});
    } catch (error) {
      toast({
        title: "Save Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <FileUpload
        martyrId={martyrId}
        onUploadSuccess={handleUploadSuccess}
        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
        maxSize={10 * 1024 * 1024} // 10MB
        multiple={true}
      />
      
      {uploadedFiles.map((file) => (
        <ImageMetadataForm
          key={file.id}
          fileId={file.id}
          fileUrl={file.url}
          metadata={imageMetadata[file.id]}
          onChange={(updates) => updateMetadata(file.id, updates)}
          onRemove={() => removeFile(file.id)}
        />
      ))}
      
      {uploadedFiles.length > 0 && (
        <Button 
          onClick={submitImages} 
          disabled={isSubmitting}
          className="w-full"
        >
          {isSubmitting ? 'Saving Images...' : 'Save All Images'}
        </Button>
      )}
    </div>
  );
};
```

### Advanced File Upload Component
```typescript
// FileUpload.tsx
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';

interface FileUploadProps {
  martyrId: number;
  onUploadSuccess: (fileUrl: string, fileId: string) => void;
  accept?: string;
  maxSize?: number;
  multiple?: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  martyrId,
  onUploadSuccess,
  accept = 'image/*',
  maxSize = 10 * 1024 * 1024, // 10MB
  multiple = false
}) => {
  const [uploading, setUploading] = useState<Record<string, number>>({});
  const { toast } = useToast();

  const uploadFile = async (file: File) => {
    const fileId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      setUploading(prev => ({ ...prev, [fileId]: 0 }));
      
      // Get signed upload URL
      const backend = getAuthenticatedBackend();
      const { uploadUrl, fileUrl, fields } = await backend.martyrs.getImageUploadURL({
        martyrId,
        filename: file.name,
        contentType: file.type,
        fileSize: file.size
      });
      
      // Upload file with progress tracking
      const formData = new FormData();
      Object.entries(fields).forEach(([key, value]) => {
        formData.append(key, value);
      });
      formData.append('file', file);
      
      const xhr = new XMLHttpRequest();
      
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100;
          setUploading(prev => ({ ...prev, [fileId]: progress }));
        }
      };
      
      xhr.onload = () => {
        if (xhr.status === 200 || xhr.status === 204) {
          setUploading(prev => {
            const { [fileId]: removed, ...rest } = prev;
            return rest;
          });
          onUploadSuccess(fileUrl, fileId);
          toast({
            title: "Upload Complete",
            description: `${file.name} uploaded successfully.`,
          });
        } else {
          throw new Error(`Upload failed: ${xhr.statusText}`);
        }
      };
      
      xhr.onerror = () => {
        throw new Error('Upload failed due to network error');
      };
      
      xhr.open('POST', uploadUrl);
      xhr.send(formData);
      
    } catch (error) {
      setUploading(prev => {
        const { [fileId]: removed, ...rest } = prev;
        return rest;
      });
      toast({
        title: "Upload Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(uploadFile);
  }, [martyrId]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { [accept.split(',')[0]]: accept.split(',') },
    maxSize,
    multiple
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-colors duration-200
          ${
            isDragActive
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          }
        `}
      >
        <input {...getInputProps()} />
        <div className="space-y-2">
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <div>
            {isDragActive ? (
              <p className="text-blue-600">Drop the files here...</p>
            ) : (
              <div>
                <p className="text-gray-600">
                  Drag & drop files here, or{' '}
                  <span className="text-blue-600 underline">browse</span>
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Max file size: {(maxSize / (1024 * 1024)).toFixed(0)}MB
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Upload Progress */}
      {Object.entries(uploading).map(([fileId, progress]) => (
        <div key={fileId} className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading...</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      ))}
    </div>
  );
};
```

### Image Management Component
```typescript
// ImageManager.tsx
import React, { useState } from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

interface ImageManagerProps {
  images: MartyrImage[];
  onImageDeleted?: (imageId: number) => void;
  onImageUpdated?: (image: MartyrImage) => void;
}

export const ImageManager: React.FC<ImageManagerProps> = ({
  images,
  onImageDeleted,
  onImageUpdated
}) => {
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const { toast } = useToast();

  const deleteImage = async (imageId: number) => {
    setDeletingId(imageId);
    
    try {
      const backend = getAuthenticatedBackend();
      await backend.martyrs.deleteImage({ id: imageId });
      
      onImageDeleted?.(imageId);
      toast({
        title: "Image Deleted",
        description: "The image has been successfully removed.",
      });
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
    }
  };

  const setAsProfileImage = async (imageId: number) => {
    try {
      const backend = getAuthenticatedBackend();
      const updatedImage = await backend.martyrs.setProfileImage({ id: imageId });
      
      onImageUpdated?.(updatedImage);
      toast({
        title: "Profile Image Updated",
        description: "The profile image has been successfully changed.",
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  if (images.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <ImageIcon className="h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">No Images</h3>
          <p className="text-sm text-gray-500">Upload images to get started</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {images.map((image) => (
        <Card key={image.id} className="overflow-hidden">
          <div className="relative">
            <img
              src={image.url}
              alt={image.caption || 'Martyr image'}
              className="w-full h-48 object-cover"
            />
            
            {image.isProfileImage && (
              <Badge className="absolute top-2 left-2 bg-green-100 text-green-800">
                Profile Image
              </Badge>
            )}

            <div className="absolute top-2 right-2 flex gap-1">
              {!image.isProfileImage && (
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setAsProfileImage(image.id)}
                >
                  Set as Profile
                </Button>
              )}
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    size="sm"
                    variant="destructive"
                    disabled={deletingId === image.id}
                  >
                    {deletingId === image.id ? 'Deleting...' : 'Delete'}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Image</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this image? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => deleteImage(image.id)}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>

          <CardContent className="p-4">
            {image.caption && (
              <p className="text-sm text-gray-700 mb-2">{image.caption}</p>
            )}
            {image.credit && (
              <p className="text-xs text-gray-500">Credit: {image.credit}</p>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
```

### Admin Backend Patterns

#### Protected Admin Endpoint Pattern
```typescript
// admin_*.ts pattern
import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";
import type { Martyr } from "./types";

interface AdminRequest {
  // Request parameters
}

interface AdminResponse {
  // Response data
}

// Admin endpoint with authentication and role checking
export const adminOperation = api<AdminRequest, AdminResponse>(
  { auth: true, expose: true, method: "POST", path: "/admin/operation" },
  async (req) => {
    // Get authenticated user
    const auth = getAuthData()!;
    
    // Role-based access control
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Input validation
    if (!req.requiredField) {
      throw APIError.invalidArgument("Required field missing");
    }

    try {
      // Database operation with error handling
      const result = await martyrsDB.queryRow<ResultType>`
        SELECT * FROM table WHERE condition = ${req.condition}
      `;
      
      if (!result) {
        throw APIError.notFound("Resource not found");
      }

      return {
        success: true,
        data: result
      };
    } catch (error) {
      // Log error for debugging
      console.error('Admin operation failed:', error);
      
      if (error instanceof APIError) {
        throw error;
      }
      
      throw APIError.internal("Operation failed");
    }
  }
);
```

#### Admin List with Search and Pagination
```typescript
// admin_list.ts pattern
import { api, Query, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";

interface AdminListRequest {
  page?: Query<number>;
  limit?: Query<number>;
  search?: Query<string>;
  sortBy?: Query<string>;
  sortOrder?: Query<'asc' | 'desc'>;
}

interface AdminListResponse {
  items: AdminListItem[];
  total: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export const adminList = api<AdminListRequest, AdminListResponse>(
  { auth: true, expose: true, method: "GET", path: "/admin/items" },
  async ({ page = 1, limit = 20, search = "", sortBy = "updated_at", sortOrder = "desc" }) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    const offset = (page - 1) * limit;
    const queryParams: any[] = [];
    let whereClause = "WHERE 1=1";
    let paramIndex = 1;

    // Search functionality
    if (search) {
      whereClause += ` AND (name ILIKE $${paramIndex} OR bio ILIKE $${paramIndex})`;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Get total count for pagination
    const countQuery = `SELECT COUNT(*) as total FROM items ${whereClause}`;
    const countResult = await martyrsDB.rawQueryRow<{ total: number }>(
      countQuery, 
      ...queryParams
    );
    const total = countResult?.total || 0;
    const totalPages = Math.ceil(total / limit);

    // Get items with sorting
    const allowedSortFields = ['name', 'created_at', 'updated_at', 'view_count'];
    const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'updated_at';
    const safeSortOrder = sortOrder === 'asc' ? 'ASC' : 'DESC';

    const itemsQuery = `
      SELECT 
        id, name, slug, bio, sub_categories, region, view_count,
        created_at, updated_at,
        (SELECT url FROM martyr_images WHERE martyr_id = items.id AND is_profile_image = true LIMIT 1) as profile_image
      FROM items 
      ${whereClause}
      ORDER BY ${safeSortBy} ${safeSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    queryParams.push(limit, offset);

    const items = await martyrsDB.rawQueryAll<AdminListItem>(itemsQuery, ...queryParams);

    return {
      items: items.map(item => ({
        ...item,
        subCategories: item.sub_categories || [],
        profileImage: item.profile_image
      })),
      total,
      page,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    };
  }
);
```

#### Admin CRUD with Validation
```typescript
// admin_create.ts pattern
import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";
import type { Martyr } from "./types";

interface CreateMartyrRequest {
  name: string;
  slug: string;
  birthDate?: string;
  birthPlace?: string;
  deathDate?: string;
  deathPlace?: string;
  martyrdomCause?: string;
  martyrdomContext?: string;
  bio: string;
  subCategories: string[];
  region?: string;
  period?: string;
  quotes: string[];
  familyInfo?: string;
  latitude?: number;
  longitude?: number;
}

export const createMartyr = api<CreateMartyrRequest, Martyr>(
  { auth: true, expose: true, method: "POST", path: "/admin/martyrs" },
  async (req) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Input validation
    if (!req.name || req.name.trim().length === 0) {
      throw APIError.invalidArgument("Name is required");
    }
    
    if (!req.slug || req.slug.trim().length === 0) {
      throw APIError.invalidArgument("Slug is required");
    }
    
    if (!req.bio || req.bio.trim().length === 0) {
      throw APIError.invalidArgument("Biography is required");
    }

    // Slug validation (URL-safe)
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    if (!slugRegex.test(req.slug)) {
      throw APIError.invalidArgument("Slug must be URL-safe (lowercase, hyphens only)");
    }

    // Check for duplicate slug
    const existing = await martyrsDB.queryRow`
      SELECT id FROM martyrs WHERE slug = ${req.slug}
    `;
    if (existing) {
      throw APIError.alreadyExists("A martyr with this slug already exists");
    }

    // Validate dates if provided
    if (req.birthDate && isNaN(Date.parse(req.birthDate))) {
      throw APIError.invalidArgument("Invalid birth date format");
    }
    
    if (req.deathDate && isNaN(Date.parse(req.deathDate))) {
      throw APIError.invalidArgument("Invalid death date format");
    }

    // Validate coordinates if provided
    if (req.latitude !== undefined && (req.latitude < -90 || req.latitude > 90)) {
      throw APIError.invalidArgument("Latitude must be between -90 and 90");
    }
    
    if (req.longitude !== undefined && (req.longitude < -180 || req.longitude > 180)) {
      throw APIError.invalidArgument("Longitude must be between -180 and 180");
    }

    try {
      const martyr = await martyrsDB.queryRow<{
        id: number;
        name: string;
        slug: string;
        birth_date?: string;
        birth_place?: string;
        death_date?: string;
        death_place?: string;
        martyrdom_cause?: string;
        martyrdom_context?: string;
        bio: string;
        sub_categories: string[];
        region?: string;
        period?: string;
        quotes: string[];
        family_info?: string;
        view_count: number;
        created_at: string;
        updated_at: string;
        latitude?: number;
        longitude?: number;
      }>`
        INSERT INTO martyrs (
          name, slug, birth_date, birth_place, death_date, death_place,
          martyrdom_cause, martyrdom_context, bio, sub_categories,
          region, period, quotes, family_info, latitude, longitude
        ) VALUES (
          ${req.name}, ${req.slug}, ${req.birthDate}, ${req.birthPlace},
          ${req.deathDate}, ${req.deathPlace}, ${req.martyrdomCause},
          ${req.martyrdomContext}, ${req.bio}, ${req.subCategories},
          ${req.region}, ${req.period}, ${req.quotes}, ${req.familyInfo},
          ${req.latitude}, ${req.longitude}
        )
        RETURNING *
      `;

      if (!martyr) {
        throw APIError.internal("Failed to create martyr");
      }

      // Transform database result to API response
      return {
        id: martyr.id,
        name: martyr.name,
        slug: martyr.slug,
        birthDate: martyr.birth_date,
        birthPlace: martyr.birth_place,
        deathDate: martyr.death_date,
        deathPlace: martyr.death_place,
        martyrdomCause: martyr.martyrdom_cause,
        martyrdomContext: martyr.martyrdom_context,
        bio: martyr.bio,
        subCategories: martyr.sub_categories || [],
        region: martyr.region,
        period: martyr.period,
        quotes: martyr.quotes || [],
        familyInfo: martyr.family_info,
        viewCount: martyr.view_count,
        createdAt: martyr.created_at,
        updatedAt: martyr.updated_at,
        latitude: martyr.latitude,
        longitude: martyr.longitude,
      };
    } catch (error) {
      console.error('Failed to create martyr:', error);
      
      if (error instanceof APIError) {
        throw error;
      }
      
      // Handle database-specific errors
      if (error.code === '23505') { // PostgreSQL unique violation
        throw APIError.alreadyExists("A martyr with this slug already exists");
      }
      
      throw APIError.internal("Failed to create martyr");
    }
  }
);
```

#### Admin Analytics Endpoint
```typescript
// admin_chart_views.ts pattern
import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";

interface ViewsOverTimeResponse {
  data: {
    date: string;
    views: number;
    uniqueViews?: number;
  }[];
  totalViews: number;
  periodStart: string;
  periodEnd: string;
}

export const getViewsOverTime = api<{}, ViewsOverTimeResponse>(
  { auth: true, expose: true, method: "GET", path: "/admin/charts/views" },
  async () => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    try {
      // Get views data for the last 30 days
      const data = await martyrsDB.queryAll<{
        date: string;
        views: number;
      }>`
        SELECT
          view_date::TEXT as date,
          SUM(view_count)::INT as views
        FROM martyr_daily_views
        WHERE view_date >= NOW() - INTERVAL '30 days'
        GROUP BY view_date
        ORDER BY view_date
      `;

      // Get total views for the period
      const totalResult = await martyrsDB.queryRow<{ total: number }>`
        SELECT SUM(view_count)::INT as total
        FROM martyr_daily_views
        WHERE view_date >= NOW() - INTERVAL '30 days'
      `;

      const totalViews = totalResult?.total || 0;
      const periodStart = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
      const periodEnd = new Date().toISOString();

      return {
        data,
        totalViews,
        periodStart,
        periodEnd
      };
    } catch (error) {
      console.error('Failed to fetch views data:', error);
      throw APIError.internal("Failed to fetch analytics data");
    }
  }
);
```

## File Upload Implementation

### Backend Upload Endpoint
```typescript
export const uploadFile = api<UploadRequest, UploadResponse>(
  { auth: true, expose: true, method: "POST", path: "/admin/upload" },
  async (req) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(req.contentType)) {
      throw APIError.invalidArgument("Invalid file type");
    }

    // Upload to storage
    const url = await storage.uploadFile(fileData, filename, contentType);
    
    // Save metadata to database
    await martyrsDB.exec`
      INSERT INTO file_uploads (id, file_url, martyr_id, uploaded_by)
      VALUES (${fileId}, ${url}, ${martyrId}, ${auth.userID})
    `;

    return { success: true, url };
  }
);
```

### Frontend Upload Component
```typescript
const FileUploadComponent = ({ onUpload }: { onUpload: (url: string) => void }) => {
  const uploadMutation = useMutation({
    mutationFn: (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      return backend.upload.uploadFile(formData);
    },
    onSuccess: (data) => onUpload(data.url),
  });

  return (
    <div className="upload-zone">
      <input 
        type="file" 
        onChange={(e) => e.target.files?.[0] && uploadMutation.mutate(e.target.files[0])}
        accept="image/*"
      />
      {uploadMutation.isPending && <div>Uploading...</div>}
    </div>
  );
};
```

## Authentication Integration

### Frontend Auth Context
```typescript
const AuthContext = createContext<{
  token: string | null;
  login: (token: string) => void;
  logout: () => void;
}>({});

// Set up client with auth
const authClient = new Client(Local, {
  auth: () => ({
    authorization: `Bearer ${token}`,
  }),
});
```

### Protected Route Pattern
```typescript
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { token } = useAuth();
  
  if (!token) {
    return <Navigate to="/admin/login" replace />;
  }
  
  return <>{children}</>;
};
```

## Search Implementation

### Backend Search with Filters
```typescript
export const search = api<SearchParams, SearchResult>(
  { expose: true, method: "GET", path: "/search" },
  async ({ query, filters, limit = 20, offset = 0 }) => {
    let whereClause = "WHERE 1=1";
    const params: any[] = [];
    let paramIndex = 1;

    // Text search
    if (query) {
      whereClause += ` AND (name ILIKE $${paramIndex} OR bio ILIKE $${paramIndex})`;
      params.push(`%${query}%`);
      paramIndex++;
    }

    // Category filter
    if (filters.categories?.length) {
      whereClause += ` AND sub_categories && $${paramIndex}`;
      params.push(filters.categories);
      paramIndex++;
    }

    const martyrs = await martyrsDB.rawQueryAll<MartyrCard>(
      `SELECT * FROM martyrs ${whereClause} LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
      ...params, limit, offset
    );

    return { martyrs, total: martyrs.length };
  }
);
```

### Frontend Search with Debouncing
```typescript
const SearchPage = () => {
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => setDebouncedQuery(query), 300);
    return () => clearTimeout(timer);
  }, [query]);

  const { data, isLoading } = useQuery({
    queryKey: ['search', debouncedQuery],
    queryFn: () => backend.martyrs.search({ query: debouncedQuery }),
    enabled: debouncedQuery.length >= 2,
  });

  return (
    <div>
      <input 
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search martyrs..."
      />
      {/* Results */}
    </div>
  );
};
```

## Performance Optimization Patterns

### Frontend Optimization
```typescript
// Lazy loading with React.lazy
const LazyComponent = React.lazy(() => import('./HeavyComponent'));

// Virtual scrolling for large lists
import { FixedSizeList as List } from 'react-window';

// Image optimization
<img 
  src={imageSrc}
  loading="lazy"
  alt={altText}
  className="w-full h-auto"
/>

// Code splitting by route
const HomePage = React.lazy(() => import('./pages/HomePage'));
```

### Backend Optimization
```typescript
// Database indexing
CREATE INDEX CONCURRENTLY idx_martyrs_search ON martyrs USING GIN(to_tsvector('english', name || ' ' || bio));

// Pagination
const martyrs = await martyrsDB.rawQueryAll<Martyr>`
  SELECT * FROM martyrs 
  ORDER BY created_at DESC 
  LIMIT ${limit} OFFSET ${offset}
`;

// Caching frequently accessed data
const cache = new Map<string, any>();
const getCachedData = async (key: string) => {
  if (cache.has(key)) return cache.get(key);
  const data = await fetchData(key);
  cache.set(key, data);
  return data;
};
```

## Error Handling Patterns

### Backend Error Handling
```typescript
try {
  const result = await someOperation();
  return result;
} catch (error) {
  if (error instanceof DatabaseError) {
    throw APIError.internal("Database operation failed");
  }
  if (error instanceof ValidationError) {
    throw APIError.invalidArgument(error.message);
  }
  throw APIError.internal("Unexpected error occurred");
}
```

### Frontend Error Handling
```typescript
// Error boundary
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}

// Query error handling
const { data, error, isError } = useQuery({
  queryKey: ['data'],
  queryFn: fetchData,
  retry: 3,
  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
});

if (isError) {
  return <ErrorMessage error={error} />;
}
```

## Testing Patterns

### Backend Testing
```typescript
// API endpoint test
import { beforeAll, describe, expect, test } from 'vitest';

describe('martyrs API', () => {
  test('should create martyr with valid data', async () => {
    const mockData = {
      name: 'Test Martyr',
      slug: 'test-martyr',
      bio: 'Test biography'
    };

    const result = await backend.martyrs.createMartyr(mockData);
    expect(result.name).toBe(mockData.name);
  });
});
```

### Frontend Testing
```typescript
// Component test
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

test('renders martyr card correctly', () => {
  const martyr = { name: 'Test Martyr', bio: 'Test bio' };
  render(<MartyrCard {...martyr} />, { wrapper: TestWrapper });
  expect(screen.getByText('Test Martyr')).toBeInTheDocument();
});
```

## Accessibility Implementation

### ARIA Labels and Semantic HTML
```typescript
// Proper heading hierarchy
<main role="main">
  <h1>Martyrs Archive</h1>
  <section aria-labelledby="featured-heading">
    <h2 id="featured-heading">Featured Martyrs</h2>
    <ul role="list">
      {martyrs.map(martyr => (
        <li key={martyr.id} role="listitem">
          <article aria-labelledby={`martyr-${martyr.id}`}>
            <h3 id={`martyr-${martyr.id}`}>{martyr.name}</h3>
            <p>{martyr.bio}</p>
          </article>
        </li>
      ))}
    </ul>
  </section>
</main>

// Focus management
const useAutoFocus = (shouldFocus: boolean) => {
  const ref = useRef<HTMLElement>(null);
  
  useEffect(() => {
    if (shouldFocus && ref.current) {
      ref.current.focus();
    }
  }, [shouldFocus]);
  
  return ref;
};
```

## Internationalization Setup

### Arabic Text Support
```css
/* RTL support */
.arabic-text {
  direction: rtl;
  text-align: right;
  font-family: 'Amiri', 'Times New Roman', serif;
}

/* Bilingual layout */
.bilingual-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

@media (max-width: 768px) {
  .bilingual-content {
    grid-template-columns: 1fr;
  }
}
```

### Date and Number Formatting
```typescript
// Locale-aware formatting
const formatDate = (date: Date, locale: string = 'en-US') => {
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};

const formatNumber = (num: number, locale: string = 'en-US') => {
  return new Intl.NumberFormat(locale).format(num);
};
```

## Deployment and DevOps

### Environment Configuration
```typescript
// Environment-specific configuration
const config = {
  development: {
    apiUrl: 'http://localhost:4000',
    storage: 'local',
    logLevel: 'debug'
  },
  production: {
    apiUrl: process.env.ENCORE_API_URL,
    storage: 's3',
    logLevel: 'info'
  }
};
```

### CI/CD Pipeline Considerations
```yaml
# Example GitHub Actions workflow
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          cd backend && npm test
          cd frontend && npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Encore
        run: encore deploy --env production
```

This technical guide provides the concrete implementation patterns and code examples that AI assistants need to work effectively with this project while following the cultural and ethical guidelines established in the main PROJECT_RULES.md file.