import React, { useEffect } from 'react';
import { runtimeOptimization, resourceOptimization } from '../lib/cssOptimization';

interface CriticalCSSProps {
  criticalCSS?: string;
  nonCriticalCSS?: string[];
  preloadResources?: boolean;
}

const CriticalCSS: React.FC<CriticalCSSProps> = ({
  criticalCSS,
  nonCriticalCSS = ['/styles/components.css', '/styles/utilities.css'],
  preloadResources = true
}) => {
  useEffect(() => {
    // Inject critical CSS if provided
    if (criticalCSS) {
      runtimeOptimization.injectCriticalCSS(criticalCSS);
    }

    // Lazy load non-critical CSS
    nonCriticalCSS.forEach(href => {
      runtimeOptimization.lazyLoadCSS(href);
    });

    // Preload critical resources
    if (preloadResources) {
      resourceOptimization.preload.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        if ('crossorigin' in resource && resource.crossorigin) {
          link.crossOrigin = resource.crossorigin;
        }
        document.head.appendChild(link);
      });

      // DNS prefetch
      resourceOptimization.dnsPrefetch.forEach(domain => {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = domain;
        document.head.appendChild(link);
      });

      // Preconnect
      resourceOptimization.preconnect.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = resource.href;
        if (resource.crossorigin) {
          link.crossOrigin = resource.crossorigin;
        }
        document.head.appendChild(link);
      });
    }
  }, [criticalCSS, nonCriticalCSS, preloadResources]);

  return null; // This component doesn't render anything visible
};

export default CriticalCSS;