import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import Header from './components/Header';
import Footer from './components/Footer';
import HomePage from './pages/HomePage';
import SearchPage from './pages/SearchPage';
import MartyrProfilePage from './pages/MartyrProfilePage';
import CategoryPage from './pages/CategoryPage';
import TimelinePage from './pages/TimelinePage';
import AboutPage from './pages/AboutPage';
import ContactPage from './pages/ContactPage';
import ResearchMethodologyPage from './pages/ResearchMethodologyPage';
import EducationalResourcesPage from './pages/EducationalResourcesPage';
import AdminLoginPage from './pages/AdminLoginPage';
import AdminDashboardPage from './pages/AdminDashboardPage';
import AdminMartyrFormPage from './pages/AdminMartyrFormPage';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
});

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 flex flex-col">
          <Routes>
            {/* Admin routes without header/footer */}
            <Route path="/admin/login" element={<AdminLoginPage />} />
            <Route path="/admin" element={<AdminDashboardPage />} />
            <Route path="/admin/martyrs/new" element={<AdminMartyrFormPage />} />
            <Route path="/admin/martyrs/:slug/edit" element={<AdminMartyrFormPage />} />
            
            {/* Public routes with header/footer */}
            <Route path="/*" element={
              <>
                <Header />
                <main className="flex-1">
                  <Routes>
                    <Route path="/" element={<HomePage />} />
                    <Route path="/search" element={<SearchPage />} />
                    <Route path="/martyrs/:slug" element={<MartyrProfilePage />} />
                    <Route path="/categories/:category" element={<CategoryPage />} />
                    <Route path="/timeline" element={<TimelinePage />} />
                    <Route path="/about" element={<AboutPage />} />
                    <Route path="/contact" element={<ContactPage />} />
                    <Route path="/methodology" element={<ResearchMethodologyPage />} />
                    <Route path="/educational" element={<EducationalResourcesPage />} />
                  </Routes>
                </main>
                <Footer />
              </>
            } />
          </Routes>
          <Toaster />
        </div>
      </Router>
    </QueryClientProvider>
  );
}
