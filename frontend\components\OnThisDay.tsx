
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import backend from '~backend/client';
import MartyrCard from './MartyrCard';
import MartyrCardSkeleton from './MartyrCardSkeleton';
import { Calendar } from 'lucide-react';

const OnThisDay = () => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['on-this-day'],
    queryFn: () => backend.martyrs.getOnThisDay(),
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {[...Array(3)].map((_, i) => <MartyrCardSkeleton key={i} />)}
      </div>
    );
  }

  if (error || (!data?.born.length && !data?.martyred.length)) {
    return null; // Don't render the section if there's an error or no data
  }

  return (
    <section className="py-20 bg-white" aria-labelledby="on-this-day-heading">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <header className="text-center mb-16">
          <h2 id="on-this-day-heading" className="text-4xl font-bold text-slate-800 mb-4 flex items-center justify-center">
            <Calendar className="w-8 h-8 mr-4" />
            On This Day in History
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">Remembering those born and martyred on this day.</p>
        </header>

        {data.born.length > 0 && (
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-slate-700 mb-8">Born on this day</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {data.born.map((martyr) => <MartyrCard key={martyr.id} {...martyr} />)}
            </div>
          </div>
        )}

        {data.martyred.length > 0 && (
          <div>
            <h3 className="text-2xl font-bold text-slate-700 mb-8">Martyred on this day</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {data.martyred.map((martyr) => <MartyrCard key={martyr.id} {...martyr} />)}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default OnThisDay;
