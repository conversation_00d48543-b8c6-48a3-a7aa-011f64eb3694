# Changelog

## [Unreleased] - 2025-08-21

### Added - File Upload & Cloud Storage System

#### New Backend Modules
- **Created `/backend/upload/` module** - Complete file upload system with cloud storage integration
  - `encore.service.ts` - Service registration for upload module
  - `types.ts` - TypeScript interfaces for file uploads, responses, and metadata
  - `storage.ts` - Storage service abstraction supporting S3/cloud storage with development simulation
  - `upload.ts` - RESTful API endpoints for file operations

#### New API Endpoints
- `POST /admin/upload/signed-url` - Generate signed upload URLs for direct frontend uploads
- `POST /admin/upload/direct` - Direct file upload endpoint for smaller files (base64)
- `DELETE /admin/upload/file` - Delete uploaded files from storage and database
- `GET /admin/upload/files` - List uploaded files with pagination and filtering
- `POST /admin/martyrs/images/upload-url` - Specialized endpoint for martyr image uploads

#### Frontend Integration
- **Created `/frontend/components/FileUpload.tsx`** - Drag-and-drop file upload component with progress tracking
  - Supports multiple file uploads with real-time progress indicators
  - File type validation and size limits
  - Integration with signed URL uploads for large files
  - Error handling and user feedback with toast notifications
  - Graceful handling of 404 errors when backend client needs regeneration

- **Created `/frontend/components/ImageUploadForm.tsx`** - Specialized form for martyr image uploads
  - Image preview before submission
  - Caption and credit metadata input
  - Profile image selection (ensures only one profile image per martyr)
  - Batch upload with individual metadata for each image
  - Status indicator for backend connectivity

- **Created `/frontend/components/ImageManager.tsx`** - Management interface for existing images
  - Grid layout with image previews
  - Delete functionality with confirmation dialogs
  - Set/unset profile image functionality
  - Display image metadata (caption, credit, upload date)

- **Created `/frontend/components/UploadTestComponent.tsx`** - Testing interface for upload functionality
  - Test console for all upload endpoints
  - Direct upload testing with sample data
  - File listing and signed URL generation tests
  - Real-time results display for debugging

- **Updated `/frontend/pages/AdminMartyrFormPage.tsx`** - Integrated new upload components
  - Replaced manual URL input with sophisticated upload interface
  - Seamless integration with existing image management workflow
  - Improved user experience with drag-and-drop uploads
  - **Fixed date format issues** - Added proper date formatting for HTML date inputs
  - Added upload testing tab for development and debugging
  - Enhanced error handling and null safety checks

#### Database Schema Changes
- **New Migration**: `2_add_file_uploads.up.sql`
  - Created `file_uploads` table with comprehensive metadata tracking
  - Fields: id, original_filename, stored_filename, file_url, file_size, mime_type, uploaded_by, martyr_id, created_at, updated_at
  - Indexes on uploaded_by, martyr_id, and created_at for performance
  - Foreign key constraints to martyrs and auth tables

#### Enhanced Features
- **Cloud Storage Integration**: Abstracted storage service supporting AWS S3 and other cloud providers
- **File Type Validation**: Restricts uploads to image files (JPEG, PNG, WebP, GIF)
- **Security**: Admin-only access with JWT authentication for all upload operations
- **File Management**: Automatic cleanup when deleting martyr images
- **Unique Naming**: Timestamp-based filename generation to prevent conflicts
- **Development Mode**: Local storage simulation for development environment
- **Progress Tracking**: Real-time upload progress with visual indicators
- **Error Handling**: Comprehensive error handling with user-friendly messages

#### Updated Existing Modules
- **Enhanced `martyrs/admin_images.ts`**:
  - Integrated with new upload system for file deletion
  - Added `getImageUploadURL` endpoint for martyr-specific image uploads
  - Automatic storage cleanup when images are deleted from martyr profiles
  - Improved error handling and type safety

- **Fixed `martyrs/get_related.ts`**:
  - Resolved SQL query issue with SELECT DISTINCT and ORDER BY
  - Improved query performance and reliability

#### Implementation Details
- **TypeScript Compatibility**: Resolved Encore.dev compatibility issues with Buffer, crypto, and Node.js APIs
- **Browser-Safe Patterns**: Implemented custom base64 decoding without Node.js dependencies
- **Database Integration**: Proper field mapping between database snake_case and TypeScript camelCase
- **Error Handling**: Comprehensive error handling with proper HTTP status codes
- **File Size Limits**: 10MB limit for direct uploads, configurable for signed URLs
- **Authentication**: All API calls use proper admin token authentication
- **Responsive Design**: Upload components work seamlessly on desktop and mobile

#### Testing & Debugging
- **Upload Test Console**: Interactive testing interface for all upload endpoints
- **Development Tools**: Comprehensive logging and error reporting
- **API Testing**: Built-in tools for testing signed URLs, direct uploads, and file listing
- **Error Diagnostics**: Detailed error messages and debugging information

#### Migration Status
- Migration SQL created and ready for application
- All TypeScript compilation errors resolved
- Frontend components ready for production use
- Backend endpoints tested and functional

### Technical Notes
- File upload system is designed to be cloud-agnostic with easy provider switching
- Development environment uses simulated storage for testing without cloud dependencies
- All uploads require admin authentication and include comprehensive audit logging
- Storage URLs are designed to be portable between different cloud providers
- Frontend components are fully responsive and accessible
- Upload progress tracking provides excellent user experience
- Error handling ensures graceful degradation in case of failures
