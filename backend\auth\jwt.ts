// JWT utility functions for secure authentication
// Implements best practices for token generation, validation, and refresh

import * as crypto from 'crypto';
import { APIError } from 'encore.dev/api';
import { secret } from 'encore.dev/config';

// JWT configuration
const JWT_CONFIG = {
  ACCESS_TOKEN_EXPIRES_IN: 15 * 60 * 1000, // 15 minutes
  REFRESH_TOKEN_EXPIRES_IN: 30 * 24 * 60 * 60 * 1000, // 30 days
  ALGORITHM: 'HS256'
};

// Get JWT secret from Encore secret management
const jwtSecret = secret("JWTSecret");

function getJWTSecret(): string {
  const secret = jwtSecret();
  if (secret) return secret;
  // Fallback for development only
  const fallback = (globalThis as any).process?.env?.DEV_JWT_SECRET || 'dev-jwt-secret-key-for-local-development-only';
  (globalThis as any).console?.warn('JWTSecret not defined; using DEV_JWT_SECRET or fallback for local development');
  return fallback;
}

// JWT Payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat: number; // Issued at
  exp: number; // Expires at
  jti?: string; // JWT ID (for refresh token tracking)
}

// Generate a secure random string
function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

// Create JWT token
export function createJWT(payload: Omit<JWTPayload, 'iat' | 'exp' | 'jti'>): { accessToken: string; refreshToken: string } {
  const secret = getJWTSecret();
  const issuedAt = Math.floor(Date.now() / 1000);
  
  // Create access token
  const accessTokenPayload: JWTPayload = {
    ...payload,
    iat: issuedAt,
    exp: issuedAt + (JWT_CONFIG.ACCESS_TOKEN_EXPIRES_IN / 1000),
  };
  
  const accessToken = signJWT(accessTokenPayload, secret);
  
  // Create refresh token with longer expiration
  const refreshTokenPayload: JWTPayload = {
    ...payload,
    iat: issuedAt,
    exp: issuedAt + (JWT_CONFIG.REFRESH_TOKEN_EXPIRES_IN / 1000),
    jti: generateSecureToken(16) // Unique identifier for refresh token
  };
  
  const refreshToken = signJWT(refreshTokenPayload, secret);
  
  return { accessToken, refreshToken };
}

// Sign JWT token
function signJWT(payload: JWTPayload, secret: string): string {
  // Create header
  const header = {
    alg: JWT_CONFIG.ALGORITHM,
    typ: 'JWT'
  };
  
  // Encode header and payload
  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  const data = `${encodedHeader}.${encodedPayload}`;
  
  // Create signature
  const signature = crypto
    .createHmac('sha256', secret)
    .update(data)
    .digest('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_');
  
  return `${data}.${signature}`;
}

// Verify JWT token
export function verifyJWT(token: string): JWTPayload {
  const secret = getJWTSecret();
  
  // Split token into parts
  const parts = token.split('.');
  if (parts.length !== 3) {
    throw APIError.unauthenticated("Invalid token format");
  }
  
  const [encodedHeader, encodedPayload, signature] = parts;
  
  // Verify signature
  const data = `${encodedHeader}.${encodedPayload}`;
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(data)
    .digest('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_');
    
  if (signature !== expectedSignature) {
    throw APIError.unauthenticated("Invalid token signature");
  }
  
  // Decode payload
  try {
    const payload: JWTPayload = JSON.parse(base64UrlDecode(encodedPayload));
    
    // Check expiration
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      throw APIError.unauthenticated("Token has expired");
    }
    
    return payload;
  } catch (error) {
    throw APIError.unauthenticated("Invalid token payload");
  }
}

// Refresh JWT token
export function refreshJWT(refreshToken: string): { accessToken: string; refreshToken: string } {
  try {
    const payload = verifyJWT(refreshToken);
    
    // Create new tokens with updated expiration
    return createJWT({
      userId: payload.userId,
      email: payload.email,
      role: payload.role
    });
  } catch (error) {
    throw APIError.unauthenticated("Invalid refresh token");
  }
}

// Base64 URL encoding
function base64UrlEncode(str: string): string {
  return Buffer.from(str, 'utf8')
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_');
}

// Base64 URL decoding
function base64UrlDecode(str: string): string {
  // Add padding if needed
  const padding = '='.repeat((4 - (str.length % 4)) % 4);
  const base64 = str.replace(/-/g, '+').replace(/_/g, '/') + padding;
  return Buffer.from(base64, 'base64').toString('utf8');
}

// Hash password (for future implementation)
export function hashPassword(password: string): string {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
}

// Verify password (for future implementation)
export function verifyPassword(password: string, hash: string): boolean {
  const [salt, storedHash] = hash.split(':');
  const newHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return newHash === storedHash;
}

// Helper function to extract token from cookie string
export function extractTokenFromCookie(cookieHeader: string, tokenName: string): string | null {
  if (!cookieHeader) return null;
  
  const cookies = cookieHeader.split(';').map(cookie => cookie.trim());
  for (const cookie of cookies) {
    if (cookie.startsWith(`${tokenName}=`)) {
      return cookie.substring(tokenName.length + 1);
    }
  }
  
  return null;
}