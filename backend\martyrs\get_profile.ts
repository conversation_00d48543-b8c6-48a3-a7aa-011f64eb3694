import { api, APIError } from "encore.dev/api";
import { martyrsDB } from "./db";
import type { MartyrProfile } from "./types";

interface GetProfileParams {
  slug: string;
}

// Retrieves a martyr's complete profile by slug.
export const getProfile = api<GetProfileParams, MartyrProfile>(
  { expose: true, method: "GET", path: "/martyrs/:slug" },
  async ({ slug }) => {
    // Get martyr basic info
    const martyr = await martyrsDB.queryRow<{
      id: number;
      name: string;
      slug: string;
      birth_date?: string;
      birth_place?: string;
      death_date?: string;
      death_place?: string;
      martyrdom_cause?: string;
      martyrdom_context?: string;
      bio: string;
      sub_categories: string[];
      region?: string;
      period?: string;
      quotes: string[];
      family_info?: string;
      view_count: number;
      created_at: string;
      updated_at: string;
      latitude?: number;
      longitude?: number;
    }>`
      SELECT
        id, name, slug, birth_date, birth_place, death_date, death_place,
        martyrdom_cause, martyrdom_context, bio, sub_categories, region, period,
        quotes, family_info, view_count, created_at, updated_at,
        latitude::float8 as latitude,
        longitude::float8 as longitude
      FROM martyrs WHERE slug = ${slug}
    `;

    if (!martyr) {
      throw APIError.notFound("Martyr not found");
    }

    // Increment view count
    await Promise.all([
      martyrsDB.exec`
        UPDATE martyrs SET view_count = view_count + 1 WHERE id = ${martyr.id}
      `,
      martyrsDB.exec`
        INSERT INTO martyr_daily_views (martyr_id, view_date, view_count)
        VALUES (${martyr.id}, NOW()::DATE, 1)
        ON CONFLICT (martyr_id, view_date)
        DO UPDATE SET view_count = martyr_daily_views.view_count + 1
      `
    ]);

    // Get images
    const images = await martyrsDB.queryAll<{
      id: number;
      martyr_id: number;
      url: string;
      caption?: string;
      credit?: string;
      is_profile_image: boolean;
      created_at: string;
    }>`
      SELECT * FROM martyr_images WHERE martyr_id = ${martyr.id} ORDER BY is_profile_image DESC, created_at
    `;

    // Get timeline events
    const timelineEvents = await martyrsDB.queryAll<{
      id: number;
      martyr_id: number;
      event_date: string;
      description: string;
      image_url?: string;
      created_at: string;
    }>`
      SELECT * FROM timeline_events WHERE martyr_id = ${martyr.id} ORDER BY event_date
    `;

    return {
      id: martyr.id,
      name: martyr.name,
      slug: martyr.slug,
      birthDate: martyr.birth_date,
      birthPlace: martyr.birth_place,
      deathDate: martyr.death_date,
      deathPlace: martyr.death_place,
      martyrdomCause: martyr.martyrdom_cause,
      martyrdomContext: martyr.martyrdom_context,
      bio: martyr.bio,
      subCategories: martyr.sub_categories || [],
      region: martyr.region,
      period: martyr.period,
      quotes: martyr.quotes || [],
      familyInfo: martyr.family_info,
      viewCount: martyr.view_count + 1,
      createdAt: martyr.created_at,
      updatedAt: martyr.updated_at,
      latitude: martyr.latitude,
      longitude: martyr.longitude,
      images: images.map(img => ({
        id: img.id,
        martyrId: img.martyr_id,
        url: img.url,
        caption: img.caption,
        credit: img.credit,
        isProfileImage: img.is_profile_image,
        createdAt: img.created_at
      })),
      timelineEvents: timelineEvents.map(event => ({
        id: event.id,
        martyrId: event.martyr_id,
        eventDate: event.event_date,
        description: event.description,
        imageUrl: event.image_url,
        createdAt: event.created_at
      }))
    };
  }
);
