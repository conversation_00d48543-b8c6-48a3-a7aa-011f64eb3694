import { martyrImages } from "./bucket";
import type { SignedUploadURL } from "./types";

export class StorageService {
  private isDevelopment: boolean;

  constructor() {
    // Check if we're in development mode
    // In development, Encore will use local filesystem storage
    // In production, it will use cloud storage (S3, GCS, etc.)
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  async generateSignedUploadURL(
    filename: string,
    contentType: string,
    fileSize: number
  ): Promise<SignedUploadURL> {
    try {
      // Generate a signed upload URL that allows direct upload to the bucket
      // This URL is valid for 1 hour (3600 seconds)
      const uploadUrl = await martyrImages.signedUploadUrl(filename, {
        ttl: 3600  // 1 hour in seconds
      });
      
      // Get the public URL for the file once uploaded
      const fileUrl = martyrImages.publicUrl(filename);
      
      return {
        uploadUrl,
        fileUrl,
        fields: {
          key: filename,
          'Content-Type': contentType,
        },
      };
    } catch (error) {
      console.error('Error generating signed upload URL:', error);
      throw new Error(`Failed to generate upload URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async uploadFile(
    filename: string,
    fileData: Uint8Array,
    contentType: string
  ): Promise<string> {
    try {
      // Upload the file directly to the bucket
      await martyrImages.upload(filename, fileData, {
        contentType: contentType,
      });
      
      // Return the public URL for the uploaded file
      return martyrImages.publicUrl(filename);
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async deleteFile(fileUrl: string): Promise<boolean> {
    try {
      // Extract the filename from the URL
      // The URL format is typically: https://bucket-name.s3.region.amazonaws.com/filename
      // or in local dev: http://localhost:4000/files/bucket-name/filename
      const url = new URL(fileUrl);
      const pathParts = url.pathname.split('/');
      const filename = pathParts[pathParts.length - 1];
      
      // Delete the file from the bucket
      await martyrImages.remove(filename);
      
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      // Return false instead of throwing to indicate the file might not exist
      return false;
    }
  }

  async listFiles(prefix?: string): Promise<Array<{ url: string; size: number; lastModified: string }>> {
    try {
      const files = [];
      
      // List all files in the bucket (optionally filtered by prefix)
      for await (const entry of martyrImages.list({ prefix })) {
        files.push({
          url: martyrImages.publicUrl(entry.name),
          size: entry.size,
          lastModified: entry.lastModified.toISOString(),
        });
      }
      
      return files;
    } catch (error) {
      console.error('Error listing files:', error);
      throw new Error(`Failed to list files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Export singleton instance
export const storage = new StorageService();