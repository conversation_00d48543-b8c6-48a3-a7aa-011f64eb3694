import React, { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { Search, Users, Clock, BookOpen, Star, Moon, Heart, Shield, Archive, ArrowRight, MapPin } from 'lucide-react';
import { motion } from 'framer-motion';
import backend from '~backend/client';
import { Button } from '@/components/ui/button';
import MartyrCard from '../components/MartyrCard';
import MartyrCardSkeleton from '../components/MartyrCardSkeleton';
import OnThisDay from '../components/OnThisDay';
import { MapContainer } from '../components/maps';
import { QuranVerseRotator } from '../components/animations';
import ContributeModal from '../components/ContributeModal';
import ContributeButton from '../components/ContributeButton';
import { heroVerses } from '../lib/quranVerses';
import animationPatterns from '../lib/animationPatterns';
import { focusRing } from '../lib/designTokens';
import {
  generateWebsiteSchema,
  generateDatasetSchema,
  generateFAQSchema,
  injectStructuredData,
  removeStructuredData
} from '../lib/structuredData';

interface StatCardProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  index?: number;
}

const StatCard: React.FC<StatCardProps> = ({ icon, value, label, index = 0 }) => (
  <motion.div
    className="text-center group"
    initial={{ opacity: 0, y: 30 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: index * 0.2, ...animationPatterns.springs.gentle }}
    whileHover={{ scale: 1.05, y: -5 }}
  >
    <motion.div
      className="flex justify-center mb-3"
      whileHover={{ rotate: 5 }}
      transition={animationPatterns.springs.bouncy}
    >
      <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/30 group-hover:bg-white/30 transition-all duration-300">
        {icon}
      </div>
    </motion.div>
    <motion.div
      className="text-4xl font-bold text-white mb-1"
      animate={{ scale: [1, 1.02, 1] }}
      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
    >
      {value}
    </motion.div>
    <div className="text-sm text-emerald-200 font-medium">{label}</div>
  </motion.div>
);

export default function HomePage() {
  const [isContributeModalOpen, setIsContributeModalOpen] = useState(false);

  const { data: featured, isLoading } = useQuery({
    queryKey: ['featured'],
    queryFn: () => backend.martyrs.getFeatured(),
  });

  useEffect(() => {
    const structuredData = [generateWebsiteSchema(), generateDatasetSchema(), generateFAQSchema()];
    injectStructuredData(structuredData);
    return () => removeStructuredData();
  }, []);

  return (
    <main className="min-h-screen" role="main">
      {/* Enhanced Hero Section */}
      <section
        className="relative bg-gradient-to-br from-emerald-900 via-teal-800 to-slate-900 text-white py-20 overflow-hidden min-h-[80vh] flex items-center"
        aria-labelledby="hero-heading"
      >
        {/* Enhanced background layers */}
        <div className="absolute inset-0 bg-black/20"></div>
        <motion.div
          className="absolute inset-0 opacity-20"
          animate={{
            backgroundPosition: ["0% 0%", "100% 100%"],
          }}
          transition={{ duration: 20, repeat: Infinity, repeatType: "reverse" }}
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-48 0c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-48 0c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7z' fill='%23fff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E")`,
            backgroundSize: "100px 100px"
          }}
        ></motion.div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Enhanced logo animation */}
          <motion.div
            className="flex justify-center mb-8"
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={animationPatterns.springs.bouncy}
          >
            <motion.div
              className="w-24 h-24 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-2xl border-2 border-white/20"
              whileHover={{
                scale: 1.1,
                rotate: 5,
                boxShadow: "0 25px 50px rgba(0, 0, 0, 0.3)"
              }}
              animate={{
                boxShadow: [
                  "0 10px 30px rgba(16, 185, 129, 0.3)",
                  "0 15px 40px rgba(16, 185, 129, 0.5)",
                  "0 10px 30px rgba(16, 185, 129, 0.3)"
                ]
              }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              <div className="relative">
                <motion.div
                  animate={{ rotate: [0, 10, 0] }}
                  transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                >
                  <Star className="w-12 h-12 text-amber-200 absolute -top-2 -left-2" fill="currentColor" />
                </motion.div>
                <Moon className="w-8 h-8 text-white" fill="currentColor" />
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced heading with better animation */}
          <motion.h1
            id="hero-heading"
            className="text-4xl sm:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-emerald-100 to-teal-100 bg-clip-text text-transparent"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, ...animationPatterns.springs.gentle }}
          >
            Honoring the Martyrs
          </motion.h1>

          {/* Enhanced verse rotator container */}
          <motion.div
            className="min-h-[120px] mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <QuranVerseRotator
              verses={heroVerses}
              interval={12000}
              verseClassName="text-lg md:text-xl text-emerald-200 arabic-text-elegant leading-relaxed"
              sourceClassName="text-sm text-amber-200 font-medium"
            />
          </motion.div>

          {/* Enhanced description */}
          <motion.p
            className="text-lg md:text-xl mb-10 text-emerald-100 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
          >
            A comprehensive digital archive documenting the lives and sacrifices of martyrs from Nigeria,
            specifically focusing on the Islamic Movement of Nigeria (IMN), preserving their eternal legacy for future generations.
          </motion.p>

          {/* Enhanced CTA button */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={animationPatterns.springs.snappy}
            >
              <Link to="/search">
                <Button
                  size="lg"
                  className={`bg-white text-emerald-800 hover:bg-emerald-50 px-8 py-4 rounded-xl text-lg font-bold shadow-2xl transition-all duration-300 ${focusRing.emerald} group`}
                >
                  <Search className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" />
                  Explore the Archive
                  <motion.div
                    className="ml-2"
                    animate={{ x: [0, 4, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <ArrowRight className="w-5 h-5" />
                  </motion.div>
                </Button>
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={animationPatterns.springs.snappy}
            >
              <Link to="/timeline">
                <Button
                  variant="outline"
                  size="lg"
                  className={`relative border-2 border-white/90 text-white hover:bg-white/20 hover:border-white px-8 py-4 rounded-xl text-lg font-bold backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 ${focusRing.white} group overflow-hidden`}
                >
                  {/* Enhanced background effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Content */}
                  <div className="relative flex items-center">
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                      className="mr-2"
                    >
                      <Clock className="w-5 h-5 group-hover:scale-110 transition-transform duration-200" />
                    </motion.div>
                    View Timeline
                    <motion.div
                      className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </div>

                  {/* Subtle shine effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 opacity-0 group-hover:opacity-100"
                    animate={{ x: ["-100%", "100%"] }}
                    transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 3, ease: "easeInOut" }}
                  />
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Enhanced At a Glance Section */}
      <section className="bg-gradient-to-r from-emerald-800 via-emerald-700 to-teal-800 text-white py-16 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={animationPatterns.springs.gentle}
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-4">Archive Overview</h2>
            <p className="text-emerald-200 text-lg max-w-2xl mx-auto">
              Comprehensive documentation of Nigerian IMN martyrs spanning nearly three decades of historical records
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={animationPatterns.stagger.container}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            <StatCard icon={<Archive className="w-8 h-8 text-emerald-200"/>} value="950+" label="Total Records" index={0} />
            <StatCard icon={<Users className="w-8 h-8 text-emerald-200"/>} value="500+" label="IMN Martyrs" index={1} />
            <StatCard icon={<Clock className="w-8 h-8 text-emerald-200"/>} value="1996-Present" label="Timeline" index={2} />
          </motion.div>
        </div>
      </section>

      {/* Enhanced Featured Martyrs */}
      <section className="py-20 bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden" aria-labelledby="featured-martyrs-heading">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2310b981' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.header
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={animationPatterns.springs.gentle}
          >
            <motion.div
              className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl mb-6 shadow-lg"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={animationPatterns.springs.bouncy}
            >
              <Heart className="w-8 h-8 text-white" fill="currentColor" />
            </motion.div>

            <h2 id="featured-martyrs-heading" className="text-4xl md:text-5xl font-bold text-slate-800 mb-4">
              Featured Martyrs
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Highlighting the stories of courage, sacrifice, and unwavering dedication to justice and faith.
            </p>
          </motion.header>

          {isLoading ? (
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
              variants={animationPatterns.stagger.container}
              initial="initial"
              animate="animate"
            >
              {[...Array(3)].map((_, i) => (
                <motion.div key={i} variants={animationPatterns.fade.fadeInUp}>
                  <MartyrCardSkeleton />
                </motion.div>
              ))}
            </motion.div>
          ) : featured?.featured && featured.featured.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"
              variants={animationPatterns.stagger.container}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
            >
              {featured.featured.map((martyr) => (
                <motion.div
                  key={martyr.id}
                  variants={animationPatterns.fade.fadeInUp}
                  whileHover={{ y: -8 }}
                  transition={animationPatterns.springs.gentle}
                >
                  <MartyrCard {...martyr} />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              className="text-center py-16"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              <div className="w-16 h-16 bg-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <Archive className="w-8 h-8 text-slate-400" />
              </div>
              <p className="text-slate-500 text-lg">No featured martyrs available at the moment.</p>
            </motion.div>
          )}

          {/* View All Link */}
          {featured?.featured && featured.featured.length > 0 && (
            <motion.div
              className="text-center mt-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={animationPatterns.springs.snappy}
              >
                <Link to="/search">
                  <Button
                    variant="outline"
                    size="lg"
                    className={`border-2 border-emerald-600 text-emerald-700 hover:bg-emerald-600 hover:text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 ${focusRing.emerald} group`}
                  >
                    View All Martyrs
                    <motion.div
                      className="ml-2"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </Button>
                </Link>
              </motion.div>
            </motion.div>
          )}
        </div>
      </section>

      <OnThisDay />

      {/* Enhanced Martyrs Map Section */}
      <section className="py-20 bg-white relative overflow-hidden" aria-labelledby="map-heading">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-slate-50 to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-slate-50 to-transparent"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.header
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={animationPatterns.springs.gentle}
          >
            <motion.div
              className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl mb-6 shadow-lg"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={animationPatterns.springs.bouncy}
            >
              <MapPin className="w-8 h-8 text-white" />
            </motion.div>

            <h2 id="map-heading" className="text-4xl md:text-5xl font-bold text-slate-800 mb-4">
              Martyrs Map
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Visualizing the locations of Nigerian IMN martyrs across Nigeria, creating a comprehensive geographical perspective of their sacrifices.
            </p>
          </motion.header>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, ...animationPatterns.springs.gentle }}
            className="relative"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 rounded-3xl blur-xl"></div>
            <div className="relative bg-white rounded-3xl shadow-2xl border border-slate-200/60 overflow-hidden">
              <MapContainer
                center={[9.0820, 8.6753]}
                zoom={6}
                height="450px"
                showClustering={true}
                className="rounded-3xl"
              />
            </div>
          </motion.div>


        </div>
      </section>

      {/* Enhanced Our Mission Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 via-teal-50 to-amber-50 relative overflow-hidden" aria-labelledby="mission-heading">
        {/* Background decoration */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-10 left-10 w-32 h-32 bg-emerald-200 rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-teal-200 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-amber-200 rounded-full blur-2xl"></div>
        </div>

        <div className="relative max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={animationPatterns.springs.gentle}
          >
            <motion.div
              className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl mb-8 shadow-lg"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={animationPatterns.springs.bouncy}
            >
              <Shield className="w-8 h-8 text-white" />
            </motion.div>

            <h2 id="mission-heading" className="text-4xl md:text-5xl font-bold text-slate-800 mb-8">
              Our Sacred Mission
            </h2>

            <motion.p
              className="text-xl md:text-2xl text-slate-700 mb-10 leading-relaxed max-w-4xl mx-auto"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
            >
              Preserving the eternal legacy of those who sacrificed their lives for justice, faith, and human dignity.
              We are committed to providing accurate, verified information while honoring the dignity of those we commemorate.
            </motion.p>

            {/* Mission values */}
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
              variants={animationPatterns.stagger.container}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
            >
              <motion.div
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-emerald-200/60 shadow-lg"
                variants={animationPatterns.fade.fadeInUp}
                whileHover={{ y: -4, scale: 1.02 }}
                transition={animationPatterns.springs.gentle}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-6 h-6 text-white" fill="currentColor" />
                </div>
                <h3 className="text-lg font-bold text-slate-800 mb-2">Respectful</h3>
                <p className="text-slate-600">Honoring each martyr with dignity and reverence</p>
              </motion.div>

              <motion.div
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-blue-200/60 shadow-lg"
                variants={animationPatterns.fade.fadeInUp}
                whileHover={{ y: -4, scale: 1.02 }}
                transition={animationPatterns.springs.gentle}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-slate-800 mb-2">Educational</h3>
                <p className="text-slate-600">Providing accurate historical documentation</p>
              </motion.div>

              <motion.div
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/60 shadow-lg"
                variants={animationPatterns.fade.fadeInUp}
                whileHover={{ y: -4, scale: 1.02 }}
                transition={animationPatterns.springs.gentle}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <MapPin className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-slate-800 mb-2">Nigeria-Focused</h3>
                <p className="text-slate-600">Preserving stories from Nigeria and the Islamic Movement of Nigeria (IMN)</p>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.6 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={animationPatterns.springs.snappy}
              >
                <Link to="/about">
                  <Button
                    variant="outline"
                    size="lg"
                    className={`border-2 border-emerald-600 text-emerald-700 hover:bg-emerald-600 hover:text-white px-10 py-4 rounded-xl text-lg font-semibold transition-all duration-300 ${focusRing.emerald} group`}
                  >
                    <BookOpen className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" />
                    Learn More About Us
                    <motion.div
                      className="ml-2"
                      animate={{ x: [0, 4, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    >
                      <ArrowRight className="w-5 h-5" />
                    </motion.div>
                  </Button>
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Contribute Modal */}
      <ContributeModal
        isOpen={isContributeModalOpen}
        onClose={() => setIsContributeModalOpen(false)}
      />

      {/* Floating Contribute Button */}
      <ContributeButton onClick={() => setIsContributeModalOpen(true)} />
    </main>
  );
}