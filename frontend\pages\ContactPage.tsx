import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Mail, Phone, MapPin, Clock, Star, Moon, Heart, Send } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { IslamicGeometricPattern, IslamicCalligraphyBorder, MosqueDecoration, IslamicFrameBorder } from '@/components/IslamicPatterns';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    category: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Message Sent",
        description: "Thank you for your message. We'll get back to you soon.",
      });
      
      setFormData({
        name: '',
        email: '',
        subject: '',
        category: '',
        message: ''
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: Mail,
      title: "Sacred Email",
      content: "<EMAIL>",
      description: "Send us a message for sacred inquiries and remembrance",
      color: "from-emerald-500 to-teal-500",
      bgColor: "from-emerald-50 to-teal-50",
      borderColor: "border-emerald-200"
    },
    {
      icon: Phone,
      title: "Divine Contact",
      content: "+234 (0) ************",
      description: "Call us during sacred hours for urgent matters",
      color: "from-blue-500 to-indigo-500",
      bgColor: "from-blue-50 to-indigo-50",
      borderColor: "border-blue-200"
    },
    {
      icon: MapPin,
      title: "Sacred Location",
      content: "Abuja, Nigeria",
      description: "Our main office in the heart of Nigeria",
      color: "from-purple-500 to-violet-500",
      bgColor: "from-purple-50 to-violet-50",
      borderColor: "border-purple-200"
    },
    {
      icon: Clock,
      title: "Prayer Hours",
      content: "Mon - Fri: 9AM - 5PM",
      description: "West Africa Time (WAT) - Blessed hours of service",
      color: "from-amber-500 to-orange-500",
      bgColor: "from-amber-50 to-orange-50",
      borderColor: "border-amber-200"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden">
      {/* Enhanced Islamic Background Pattern */}
      <IslamicGeometricPattern className="fixed inset-0 z-0" opacity={0.03} color="#059669" />
      
      {/* Floating decorative elements */}
      <div className="fixed top-20 left-10 w-8 h-8 bg-emerald-400/20 rounded-full animate-pulse"></div>
      <div className="fixed top-40 right-20 w-12 h-12 bg-amber-400/15 rounded-full animate-bounce"></div>
      <div className="fixed bottom-20 left-20 w-6 h-6 bg-teal-400/25 rounded-full animate-ping"></div>

      {/* Header Section */}
      <section className="relative bg-gradient-to-br from-emerald-900 via-emerald-800 to-teal-800 text-white py-20 overflow-hidden">
        <IslamicGeometricPattern opacity={0.1} color="#ffffff" />
        
        {/* Floating Islamic elements */}
        <div className="absolute top-10 right-10 opacity-20">
          <MosqueDecoration color="#ffffff" />
        </div>
        <div className="absolute bottom-10 left-10 opacity-15">
          <Star className="w-16 h-16 text-amber-300" fill="currentColor" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center mb-8">
              <div className="relative group">
                <div className="w-24 h-24 bg-gradient-to-br from-emerald-500 via-emerald-600 to-teal-600 rounded-2xl flex items-center justify-center shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:scale-105">
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
                  <div className="relative">
                    <Mail className="w-12 h-12 text-white drop-shadow-xl" />
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-amber-400 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-emerald-100 to-teal-100 bg-clip-text text-transparent leading-tight">
              Sacred Contact
            </h1>
            
            <IslamicCalligraphyBorder className="max-w-md mx-auto mb-6" color="#10b981" />
            
            <div className="text-xl text-emerald-200 mb-6 font-medium tracking-wide" dir="rtl">
              تواصل مقدس معنا لخدمة ذكرى الشهداء
            </div>
            
            <p className="text-xl md:text-2xl mb-8 text-emerald-100 max-w-5xl mx-auto leading-relaxed">
              We welcome your sacred feedback, blessed corrections, and divine contributions to help us maintain 
              an accurate and respectful archive. Reach out to us for any inquiries about our martyrs' eternal legacy.
            </p>
          </div>
        </div>
      </section>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <IslamicFrameBorder className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-emerald-100" borderColor="#059669">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                <Heart className="w-8 h-8 text-white" />
              </div>
            </div>
            <h2 className="text-3xl font-bold text-slate-800 mb-4">Connect with Sacred Purpose</h2>
            <p className="text-lg text-slate-600 max-w-4xl mx-auto leading-relaxed">
              We welcome your sacred feedback, blessed corrections, and divine contributions to help us maintain 
              an accurate and respectful archive. Get in touch with us for any inquiries about preserving martyrs' eternal legacy.
            </p>
          </IslamicFrameBorder>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Enhanced Contact Information */}
          <div className="lg:col-span-1">
            <Card className="mb-8 bg-gradient-to-br from-white/95 to-emerald-50/50 backdrop-blur-sm shadow-2xl border-2 border-emerald-100 rounded-3xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-6">
                <CardTitle className="text-center flex items-center justify-center">
                  <Send className="w-6 h-6 mr-3" />
                  Sacred Communication
                  <Moon className="w-5 h-5 ml-3" fill="currentColor" />
                </CardTitle>
                <IslamicCalligraphyBorder className="mt-3" color="#ffffff" />
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-8">
                  {contactInfo.map((info) => {
                    const IconComponent = info.icon;
                    return (
                      <div key={info.title} className={`flex items-start space-x-4 p-4 rounded-2xl bg-gradient-to-r ${info.bgColor} border-2 ${info.borderColor} group hover:shadow-lg transition-all duration-300`}>
                        <div className="flex-shrink-0">
                          <div className={`w-12 h-12 bg-gradient-to-br ${info.color} rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110`}>
                            <IconComponent className="w-6 h-6 text-white" />
                          </div>
                        </div>
                        <div>
                          <h3 className="font-bold text-slate-800 mb-1">{info.title}</h3>
                          <p className="text-slate-700 font-medium mb-1">{info.content}</p>
                          <p className="text-sm text-slate-600">{info.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Quick Links */}
            <Card className="bg-gradient-to-br from-white/95 to-amber-50/50 backdrop-blur-sm shadow-2xl border-2 border-amber-100 rounded-3xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-amber-600 to-orange-600 text-white py-6">
                <CardTitle className="text-center">Sacred Inquiries</CardTitle>
                <IslamicCalligraphyBorder className="mt-3" color="#ffffff" />
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {[
                    {
                      title: "Information Corrections",
                      description: "Report inaccuracies or provide additional sacred information about profiles",
                      color: "from-emerald-100 to-emerald-200"
                    },
                    {
                      title: "Content Contributions", 
                      description: "Submit verified information about martyrs not yet in our sacred archive",
                      color: "from-blue-100 to-blue-200"
                    },
                    {
                      title: "Copyright Issues",
                      description: "Report copyright concerns or request sacred image removal",
                      color: "from-red-100 to-red-200"
                    },
                    {
                      title: "Research Collaboration",
                      description: "Partner with us for academic research and sacred documentation",
                      color: "from-purple-100 to-purple-200"
                    }
                  ].map((item, index) => (
                    <div key={index} className={`p-4 rounded-xl bg-gradient-to-r ${item.color} border border-opacity-50`}>
                      <h4 className="font-bold text-slate-800 text-sm mb-2">{item.title}</h4>
                      <p className="text-xs text-slate-600">{item.description}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Contact Form */}
          <div className="lg:col-span-2">
            <Card className="bg-gradient-to-br from-white/95 to-slate-50/50 backdrop-blur-sm shadow-2xl border-2 border-slate-100 rounded-3xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-slate-700 to-slate-800 text-white py-8">
                <CardTitle className="text-2xl text-center flex items-center justify-center">
                  <Heart className="w-6 h-6 mr-3" />
                  Send Sacred Message
                  <Star className="w-5 h-5 ml-3" fill="currentColor" />
                </CardTitle>
                <IslamicCalligraphyBorder className="mt-4" color="#ffffff" />
              </CardHeader>
              <CardContent className="p-10">
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-slate-700 font-semibold">Full Name *</Label>
                      <Input
                        id="name"
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        required
                        className="border-2 border-emerald-200 focus:border-emerald-400 rounded-xl py-3 px-4 bg-white/80 backdrop-blur-sm transition-all duration-300"
                        placeholder="Enter your blessed name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-slate-700 font-semibold">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                        className="border-2 border-emerald-200 focus:border-emerald-400 rounded-xl py-3 px-4 bg-white/80 backdrop-blur-sm transition-all duration-300"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category" className="text-slate-700 font-semibold">Sacred Inquiry Category</Label>
                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                      <SelectTrigger className="border-2 border-emerald-200 focus:border-emerald-400 rounded-xl py-3 px-4 bg-white/80 backdrop-blur-sm transition-all duration-300">
                        <SelectValue placeholder="Select a sacred category" />
                      </SelectTrigger>
                      <SelectContent className="bg-white/95 backdrop-blur-sm border-2 border-emerald-200 rounded-xl">
                        <SelectItem value="correction">Sacred Information Correction</SelectItem>
                        <SelectItem value="contribution">Divine Content Contribution</SelectItem>
                        <SelectItem value="copyright">Copyright Sacred Issue</SelectItem>
                        <SelectItem value="research">Research Sacred Collaboration</SelectItem>
                        <SelectItem value="general">General Sacred Inquiry</SelectItem>
                        <SelectItem value="technical">Technical Sacred Support</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-slate-700 font-semibold">Sacred Subject *</Label>
                    <Input
                      id="subject"
                      type="text"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      required
                      className="border-2 border-emerald-200 focus:border-emerald-400 rounded-xl py-3 px-4 bg-white/80 backdrop-blur-sm transition-all duration-300"
                      placeholder="Brief description of your sacred inquiry"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-slate-700 font-semibold">Sacred Message *</Label>
                    <Textarea
                      id="message"
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      required
                      rows={8}
                      className="border-2 border-emerald-200 focus:border-emerald-400 rounded-xl py-3 px-4 bg-white/80 backdrop-blur-sm transition-all duration-300 resize-none"
                      placeholder="Please share your sacred message with as much divine detail as possible..."
                    />
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-blue-50 border-2 border-blue-200 rounded-2xl p-6 relative overflow-hidden">
                    <IslamicGeometricPattern opacity={0.05} color="#3b82f6" />
                    <div className="relative">
                      <p className="text-blue-800 font-medium">
                        <strong>Sacred Note:</strong> When reporting information corrections or contributing 
                        divine content, please include sacred sources and verification details. For copyright issues, 
                        please provide specific details about the blessed content in question.
                      </p>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-emerald-600 via-emerald-700 to-teal-600 hover:from-emerald-700 hover:via-emerald-800 hover:to-teal-700 text-white py-4 px-8 rounded-2xl text-lg font-bold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border-0"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                        Sending Sacred Message...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Send className="w-5 h-5 mr-3" />
                        Send Sacred Message
                        <Heart className="w-5 h-5 ml-3" />
                      </div>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Enhanced Additional Information */}
        <div className="mt-16">
          <IslamicFrameBorder className="bg-gradient-to-br from-white/95 to-emerald-50/50 backdrop-blur-sm rounded-3xl shadow-2xl border border-emerald-100" borderColor="#059669">
            <div className="text-center mb-12">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Star className="w-8 h-8 text-white" fill="currentColor" />
                </div>
              </div>
              <h2 className="text-3xl font-bold text-slate-800 mb-4">Sacred Information</h2>
              <IslamicCalligraphyBorder className="max-w-md mx-auto" color="#059669" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                {
                  title: "Divine Response Time",
                  content: "We typically respond to sacred inquiries within 2-3 blessed business days. For urgent spiritual matters, please indicate this in your subject line.",
                  color: "from-emerald-100 to-emerald-200",
                  borderColor: "border-emerald-300",
                  icon: Clock
                },
                {
                  title: "Sacred Privacy Policy", 
                  content: "Your blessed contact information will only be used to respond to your divine inquiry and will not be shared with third parties or used for commercial purposes.",
                  color: "from-blue-100 to-blue-200",
                  borderColor: "border-blue-300",
                  icon: Heart
                },
                {
                  title: "Content Sacred Guidelines",
                  content: "All contributed blessed content must be verified and respectful. We reserve the right to review and approve all submissions to maintain the sanctity of our archive.",
                  color: "from-purple-100 to-purple-200", 
                  borderColor: "border-purple-300",
                  icon: Star
                },
                {
                  title: "Non-Profit Sacred Status",
                  content: "This platform operates as a non-profit educational resource dedicated to preserving martyrs' sacred memory. We do not accept commercial inquiries or advertisements.",
                  color: "from-amber-100 to-amber-200",
                  borderColor: "border-amber-300",
                  icon: Moon
                }
              ].map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <div key={index} className={`p-6 rounded-2xl bg-gradient-to-br ${item.color} border-2 ${item.borderColor} group hover:shadow-lg transition-all duration-300 relative overflow-hidden`}>
                    <div className="absolute top-0 right-0 w-16 h-16 opacity-10">
                      <IslamicGeometricPattern opacity={0.5} color="#059669" />
                    </div>
                    <div className="relative">
                      <div className="flex items-center mb-4">
                        <div className="w-10 h-10 bg-white/70 rounded-xl flex items-center justify-center mr-3 shadow-md">
                          <IconComponent className="w-5 h-5 text-slate-700" fill={item.title.includes("Sacred Privacy") ? "currentColor" : undefined} />
                        </div>
                        <h3 className="font-bold text-slate-800">{item.title}</h3>
                      </div>
                      <p className="text-slate-700 text-sm leading-relaxed">{item.content}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </IslamicFrameBorder>
        </div>
      </div>
    </div>
  );
}
