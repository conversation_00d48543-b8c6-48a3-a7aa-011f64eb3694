import React, { useEffect, useRef } from 'react';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Cluster from 'ol/source/Cluster';
import GeoJSON from 'ol/format/GeoJSON';
import { Style, Fill, Stroke, Circle, Text } from 'ol/style';
import type Map from 'ol/Map';
import type { Feature } from 'ol';
import type { Geometry } from 'ol/geom';
import type { MarkerLayerProps } from './types/mapTypes';

interface MarkerLayerComponentProps extends MarkerLayerProps {
  map: Map | null;
  geoJsonData: any;
}

const MarkerLayer: React.FC<MarkerLayerComponentProps> = ({
  map,
  geoJsonData,
  showClustering = true,
}) => {
  const layerRef = useRef<VectorLayer<VectorSource<Feature<Geometry>>> | null>(null);

  useEffect(() => {
    if (!map || !geoJsonData) return;

    // Create vector source from GeoJSON data
    const vectorSource = new VectorSource<Feature<Geometry>>({
      features: new GeoJSON().readFeatures(geoJsonData, {
        featureProjection: 'EPSG:3857',
      }) as Feature<Geometry>[],
    });

    // Create cluster source if clustering is enabled
    const source = showClustering 
      ? new Cluster({
          distance: 40,
          minDistance: 20,
          source: vectorSource,
        })
      : vectorSource;

    // Style function for markers and clusters
    const styleFunction = (feature: any) => {
      const size = showClustering ? feature.get('features')?.length || 1 : 1;
      
      if (size > 1) {
        // Cluster style
        return new Style({
          image: new Circle({
            radius: Math.min(15 + (size / 5), 30),
            fill: new Fill({
              color: 'rgba(5, 150, 105, 0.8)', // emerald-600 with opacity
            }),
            stroke: new Stroke({
              color: 'rgba(255, 255, 255, 0.9)',
              width: 2,
            }),
          }),
          text: new Text({
            text: size.toString(),
            fill: new Fill({
              color: '#ffffff',
            }),
            font: 'bold 12px Inter, system-ui, sans-serif',
          }),
        });
      } else {
        // Individual marker style
        return new Style({
          image: new Circle({
            radius: 8,
            fill: new Fill({
              color: '#059669', // emerald-600
            }),
            stroke: new Stroke({
              color: '#ffffff',
              width: 2,
            }),
          }),
        });
      }
    };

    // Create vector layer
    const vectorLayer = new VectorLayer({
      source: source as any,
      style: styleFunction,
    });

    // Add layer to map
    map.addLayer(vectorLayer);
    layerRef.current = vectorLayer;

    // Cleanup function
    return () => {
      if (layerRef.current) {
        map.removeLayer(layerRef.current);
        layerRef.current = null;
      }
    };
  }, [map, geoJsonData, showClustering]);

  return null; // This component doesn't render anything directly
};

export default MarkerLayer;