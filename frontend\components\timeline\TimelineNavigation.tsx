import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Heart, Users, BookOpen, AlertTriangle, Star, Clock, TrendingUp } from 'lucide-react';
import { timelineData, getTimelineStats } from './timelineData';
import { IslamicCalligraphyBorder, IslamicGeometricPattern } from '../IslamicPatterns';

interface TimelineNavigationProps {
  activeYear: number;
  onYearSelect: (year: number) => void;
  className?: string;
}

interface YearConfig {
  icon: React.ReactNode;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    border: string;
    text: string;
    hover: string;
  };
}

export default function TimelineNavigation({ activeYear, onYearSelect, className = '' }: TimelineNavigationProps) {
  const stats = getTimelineStats();

  const yearConfigs: Record<number, YearConfig> = {
    1996: {
      icon: <BookOpen className="w-4 h-4" />,
      colors: {
        primary: 'bg-emerald-600',
        secondary: 'bg-emerald-100',
        background: 'bg-emerald-50',
        border: 'border-emerald-500',
        text: 'text-emerald-800',
        hover: 'hover:bg-emerald-100'
      }
    },
    2014: {
      icon: <AlertTriangle className="w-4 h-4" />,
      colors: {
        primary: 'bg-orange-600',
        secondary: 'bg-orange-100',
        background: 'bg-orange-50',
        border: 'border-orange-500',
        text: 'text-orange-800',
        hover: 'hover:bg-orange-100'
      }
    },
    2015: {
      icon: <Heart className="w-4 h-4" />,
      colors: {
        primary: 'bg-red-600',
        secondary: 'bg-red-100',
        background: 'bg-red-50',
        border: 'border-red-500',
        text: 'text-red-800',
        hover: 'hover:bg-red-100'
      }
    }
  };

  const getYearConfig = (year: number): YearConfig => {
    return yearConfigs[year] || {
      icon: <Calendar className="w-4 h-4" />,
      colors: {
        primary: 'bg-slate-600',
        secondary: 'bg-slate-100',
        background: 'bg-slate-50',
        border: 'border-slate-500',
        text: 'text-slate-800',
        hover: 'hover:bg-slate-100'
      }
    };
  };

  const getYearDescription = (year: number) => {
    const yearData = timelineData.find(y => y.year === year);
    return yearData?.yearSummary || '';
  };

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-xl border-2 border-emerald-100 overflow-hidden ${className}`}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Enhanced Header with Islamic Elements */}
      <div className="relative bg-gradient-to-br from-emerald-800 via-emerald-900 to-teal-900 p-6 text-white overflow-hidden">
        <IslamicGeometricPattern className="absolute inset-0" opacity={0.1} color="#ffffff" />

        <div className="relative z-10">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mr-3">
              <Clock className="w-6 h-6 text-emerald-200" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">IMN Timeline</h2>
              <p className="text-emerald-200 text-sm font-arabic" dir="rtl">
                الجدول الزمني للحركة الإسلامية
              </p>
            </div>
          </div>

          <IslamicCalligraphyBorder className="mb-4" color="#10b981" />

          <p className="text-emerald-100 text-sm text-center leading-relaxed">
            Navigate through the historical timeline of the Islamic Movement of Nigeria
          </p>
        </div>
      </div>

      {/* Enhanced Statistics Section */}
      <div className="bg-gradient-to-r from-slate-50 to-emerald-50 p-6 border-b border-emerald-100">
        <h3 className="text-lg font-bold text-slate-800 mb-4 text-center flex items-center justify-center">
          <TrendingUp className="w-5 h-5 mr-2 text-emerald-600" />
          Timeline Statistics
        </h3>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <motion.div
            className="bg-white rounded-xl p-4 shadow-md border border-red-200 text-center"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Heart className="w-4 h-4 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-red-600">{stats.killed}</div>
            <div className="text-xs text-slate-600 font-medium">Total Martyrs</div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 shadow-md border border-orange-200 text-center"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Users className="w-4 h-4 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-orange-600">{stats.injured}</div>
            <div className="text-xs text-slate-600 font-medium">Total Injured</div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 shadow-md border border-blue-200 text-center"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Calendar className="w-4 h-4 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{stats.events}</div>
            <div className="text-xs text-slate-600 font-medium">Total Events</div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-4 shadow-md border border-emerald-200 text-center"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Star className="w-4 h-4 text-emerald-600" />
            </div>
            <div className="text-2xl font-bold text-emerald-600">{stats.yearsSpanned}</div>
            <div className="text-xs text-slate-600 font-medium">Years Covered</div>
          </motion.div>
        </div>
      </div>

      {/* Enhanced Year Navigation */}
      <div className="p-6">
        <h3 className="text-lg font-bold text-slate-800 mb-4 flex items-center">
          <Calendar className="w-5 h-5 mr-2 text-emerald-600" />
          Select Historical Period
        </h3>

        <div className="space-y-4">
          {timelineData.map((yearData, index) => {
            const config = getYearConfig(yearData.year);
            const isActive = activeYear === yearData.year;

            return (
              <motion.button
                key={yearData.year}
                onClick={() => onYearSelect(yearData.year)}
                className={`w-full text-left p-5 rounded-2xl border-2 transition-all duration-300 ${
                  isActive
                    ? `${config.colors.border} ${config.colors.background} shadow-lg transform scale-[1.02]`
                    : `border-slate-200 ${config.colors.hover} hover:shadow-md hover:border-slate-300`
                }`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                whileHover={{ scale: isActive ? 1.02 : 1.01 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-start space-x-4">
                  {/* Enhanced Year Circle */}
                  <div className={`flex-shrink-0 w-14 h-14 rounded-2xl flex items-center justify-center shadow-md ${
                    isActive
                      ? `${config.colors.primary} text-white`
                      : `${config.colors.secondary} ${config.colors.text}`
                  }`}>
                    {config.icon}
                  </div>

                  {/* Enhanced Year Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className={`text-2xl font-bold ${
                          isActive ? config.colors.text : 'text-slate-800'
                        }`}>
                          {yearData.year}
                        </h4>
                        <p className="text-xs text-slate-500 font-medium">
                          {yearData.year === 1996 && "Foundation Period"}
                          {yearData.year === 2014 && "First Major Incident"}
                          {yearData.year === 2015 && "Severe Persecution"}
                        </p>
                      </div>

                      <div className="flex flex-col items-end space-y-1">
                        {yearData.totalCasualties.killed > 0 && (
                          <div className="flex items-center space-x-1 bg-red-100 px-2 py-1 rounded-lg">
                            <Heart className="w-3 h-3 text-red-600" />
                            <span className="text-xs font-bold text-red-700">{yearData.totalCasualties.killed}</span>
                          </div>
                        )}
                        {yearData.totalCasualties.injured > 0 && (
                          <div className="flex items-center space-x-1 bg-orange-100 px-2 py-1 rounded-lg">
                            <Users className="w-3 h-3 text-orange-600" />
                            <span className="text-xs font-bold text-orange-700">{yearData.totalCasualties.injured}</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-1 bg-slate-100 px-2 py-1 rounded-lg">
                          <Calendar className="w-3 h-3 text-slate-600" />
                          <span className="text-xs font-medium text-slate-700">{yearData.events.length} events</span>
                        </div>
                      </div>
                    </div>

                    <p className={`text-sm leading-relaxed ${
                      isActive ? config.colors.text : 'text-slate-600'
                    }`}>
                      {getYearDescription(yearData.year)}
                    </p>
                  </div>
                </div>
              </motion.button>
            );
          })}
        </div>

        {/* Enhanced Timeline Period Summary */}
        <motion.div
          className="mt-8 pt-6 border-t border-emerald-200"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <h4 className="font-bold text-slate-800 mb-3 flex items-center">
            <BookOpen className="w-4 h-4 mr-2 text-emerald-600" />
            Timeline Overview
          </h4>
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-4 border border-emerald-200">
            <p className="text-sm text-emerald-800 leading-relaxed mb-2">
              This timeline documents the history of the Islamic Movement of Nigeria from <strong>{stats.firstYear}</strong> to <strong>{stats.lastYear}</strong>,
              covering <strong>{stats.yearsSpanned}</strong> documented years of significant events.
            </p>
            <p className="text-xs text-emerald-700 italic">
              From peaceful foundations to tragic persecutions that have shaped the movement's history in Nigeria.
            </p>
          </div>
        </motion.div>

        {/* Enhanced Legend with Islamic Elements */}
        <motion.div
          className="mt-6 pt-6 border-t border-emerald-200"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.7 }}
        >
          <h4 className="font-bold text-slate-800 mb-4 flex items-center">
            <Star className="w-4 h-4 mr-2 text-emerald-600" />
            Period Classification
          </h4>

          <div className="space-y-3">
            <motion.div
              className="flex items-center justify-between p-3 bg-emerald-50 rounded-xl border border-emerald-200"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-emerald-500 rounded-full shadow-sm"></div>
                <span className="text-sm font-medium text-emerald-800">Peaceful Foundation</span>
              </div>
              <span className="text-xs text-emerald-600 bg-emerald-100 px-2 py-1 rounded-lg">1996</span>
            </motion.div>

            <motion.div
              className="flex items-center justify-between p-3 bg-orange-50 rounded-xl border border-orange-200"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-orange-500 rounded-full shadow-sm"></div>
                <span className="text-sm font-medium text-orange-800">First Major Incident</span>
              </div>
              <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-lg">2014</span>
            </motion.div>

            <motion.div
              className="flex items-center justify-between p-3 bg-red-50 rounded-xl border border-red-200"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-red-500 rounded-full shadow-sm"></div>
                <span className="text-sm font-medium text-red-800">Severe Persecution</span>
              </div>
              <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded-lg">2015</span>
            </motion.div>
          </div>
        </motion.div>

        {/* Memorial Quote */}
        <motion.div
          className="mt-6 pt-6 border-t border-emerald-200"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.9 }}
        >
          <div className="bg-gradient-to-r from-slate-800 to-slate-900 rounded-xl p-4 text-white text-center">
            <p className="text-sm font-arabic mb-2" dir="rtl">
              وَلَا تَحْسَبَنَّ الَّذِينَ قُتِلُوا فِي سَبِيلِ اللَّهِ أَمْوَاتًا
            </p>
            <p className="text-xs text-slate-300">
              "Never think of those killed in Allah's cause as dead" - Quran 3:169
            </p>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}
