# Martyr Biography Website

A respectful digital memorial platform built with React and Appwrite Cloud, dedicated to preserving and sharing the stories of martyrs with dignity and honor.

## Architecture

### Frontend
- **Framework**: React 19.1.1+ with TypeScript
- **State Management**: TanStack Query 5.85.3+
- **Routing**: React Router DOM 7.8.1+
- **Styling**: Tailwind CSS 4.1.12+ with custom design tokens
- **UI Components**: Radix UI primitives
- **Rich Text Editing**: TipTap 3.3.0+
- **Maps**: OpenLayers 8.2.0+
- **Animations**: Framer Motion 12.23.12+
- **Build Tool**: Vite 6.3.5+

### Backend
- **Backend-as-a-Service**: Appwrite Cloud
- **Database**: Appwrite Database (collections)
- **Authentication**: Appwrite Auth
- **File Storage**: Appwrite Storage
- **API**: Appwrite SDK (no custom backend required)

## Features

### Public Features
- **Martyr Profiles**: Comprehensive biographical information
- **Interactive Maps**: Geographic visualization of martyr locations
- **Advanced Search**: Full-text search with filtering capabilities
- **Timeline View**: Chronological events and milestones
- **Category Browsing**: Browse by categories, regions, and periods
- **Responsive Design**: Optimized for mobile, tablet, and desktop

### Admin Features
- **Content Management**: Full CRUD operations for martyr profiles
- **Image Management**: Upload, organize, and manage martyr images
- **Rich Text Editing**: Advanced biography editing with formatting
- **Analytics Dashboard**: Content statistics and health monitoring
- **User Management**: Admin authentication and access control
- **Timeline Management**: Create and manage timeline events

## Project Structure

```
martyr-website/
├── frontend/                   # React application
│   ├── components/            # Reusable React components
│   ├── pages/                 # Page components
│   ├── lib/                   # Utility libraries and configurations
│   ├── hooks/                 # Custom React hooks
│   ├── styles/                # Styling files
│   └── ...
├── scripts/                   # Development scripts
├── migration.md              # Migration documentation
├── migrate-changes.md        # Migration change log
└── package.json              # Project configuration
```

## Getting Started

### Prerequisites
- Node.js 18+ and npm
- Appwrite Cloud account
- Modern web browser

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/inner-byte/martyr-website.git
   cd martyr-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Appwrite**
   - Create an Appwrite Cloud project
   - Set up database collections (martyrs, images, timeline_events)
   - Configure authentication and storage
   - Copy environment variables from `.env.example`

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:5173
   - Admin: http://localhost:5173/admin

### Environment Configuration

Create a `.env.local` file in the frontend directory:

```env
VITE_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
VITE_APPWRITE_PROJECT_ID=your-project-id
VITE_APPWRITE_DATABASE_ID=your-database-id
VITE_APPWRITE_STORAGE_BUCKET_ID=your-bucket-id
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Development Workflow

1. **Frontend Development**: All development happens in the `frontend/` directory
2. **Appwrite Integration**: Use Appwrite SDK for all backend operations
3. **State Management**: Use TanStack Query for data fetching and caching
4. **Styling**: Use Tailwind CSS with custom design tokens
5. **Testing**: Test all functionality thoroughly before deployment

## Deployment

### Frontend Deployment
The frontend can be deployed to any static hosting service:
- Vercel (recommended)
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

### Appwrite Configuration
- Database collections with proper indexes
- Authentication service configured
- Storage buckets with appropriate permissions
- API keys and security settings

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Cultural Sensitivity

This project handles culturally and religiously sensitive content. Please:
- Maintain respectful language and tone
- Ensure accurate historical information
- Handle personal data with care
- Follow Islamic guidelines for memorial content
- Respect privacy and dignity of families

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Review the documentation

---

**Note**: This project has been migrated from Encore.dev to Appwrite Cloud for improved scalability and maintainability. See `migration.md` for detailed migration documentation.
