import { middleware, APIError } from "encore.dev/api";

// Simple in-memory store for rate limiting
// In production, you would use Redis or similar
class RateLimiter {
  private requests: Map<string, { count: number; resetTime: number }> = new Map();
  
  // Check if a request is allowed based on rate limits
  isAllowed(key: string, maxRequests: number, windowMs: number): { allowed: boolean; resetTime: number; remaining: number } {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean up old entries
    for (const [k, value] of this.requests.entries()) {
      if (value.resetTime < now) {
        this.requests.delete(k);
      }
    }
    
    const entry = this.requests.get(key);
    
    // If no entry exists or the window has expired, create a new one
    if (!entry || entry.resetTime < now) {
      const resetTime = now + windowMs;
      this.requests.set(key, { count: 1, resetTime });
      return { allowed: true, resetTime, remaining: maxRequests - 1 };
    }
    
    // If we're still within the window, check if we've exceeded the limit
    if (entry.count >= maxRequests) {
      return { allowed: false, resetTime: entry.resetTime, remaining: 0 };
    }
    
    // Increment the count and return allowed
    entry.count++;
    return { allowed: true, resetTime: entry.resetTime, remaining: maxRequests - entry.count };
  }
}

const rateLimiter = new RateLimiter();

// Rate limiting middleware for login endpoint
// Allows 5 requests per minute per IP
export const loginRateLimiter = middleware(
  { target: { tags: ["login"] } },
  async (req, next) => {
    // Get IP address from request metadata
    const ip = req.requestMeta?.clientIP || 'unknown';
    
    // Apply rate limiting (5 requests per minute)
    const { allowed, resetTime, remaining } = rateLimiter.isAllowed(
      `login:${ip}`,
      5, // max requests
      60 * 1000 // window in milliseconds (1 minute)
    );
    
    // Set rate limit headers
    const resetSeconds = Math.ceil((resetTime - Date.now()) / 1000);
    
    if (!allowed) {
      throw APIError.resourceExhausted("Too many login attempts. Please try again later.", {
        headers: {
          "X-RateLimit-Limit": "5",
          "X-RateLimit-Remaining": "0",
          "X-RateLimit-Reset": resetSeconds.toString(),
        }
      });
    }
    
    // Add rate limit headers to response
    const response = await next(req);
    response.headers.set("X-RateLimit-Limit", "5");
    response.headers.set("X-RateLimit-Remaining", remaining.toString());
    response.headers.set("X-RateLimit-Reset", resetSeconds.toString());
    
    return response;
  }
);