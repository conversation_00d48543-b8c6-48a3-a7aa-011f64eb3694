/* Critical CSS - Essential styles for above-the-fold content */
/* This should be inlined in the HTML head for optimal performance */

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #334155;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Essential Layout */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Critical Typography - Arabic Support */
.arabic-text {
  font-family: '<PERSON><PERSON>', 'Noto Naskh Arabic', 'Traditional Arabic', serif;
  direction: rtl;
  text-align: right;
  font-feature-settings: 'liga' 1, 'dlig' 1, 'calt' 1;
  line-height: 1.8;
  letter-spacing: 0.02em;
}

/* Hero Section Critical Styles */
.hero-section {
  position: relative;
  background: linear-gradient(135deg, #065f46 0%, #0f766e 50%, #1e293b 100%);
  color: white;
  overflow: hidden;
  min-height: 60vh;
}

@media (min-width: 768px) {
  .hero-section {
    min-height: 70vh;
  }
}

@media (min-width: 1024px) {
  .hero-section {
    min-height: 80vh;
  }
}

/* Critical Button Styles */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
  background: linear-gradient(135deg, #10b981 0%, #0d9488 100%);
  color: white;
  text-decoration: none;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

.btn-primary:focus {
  outline: none;
  ring: 4px;
  ring-color: rgba(16, 185, 129, 0.5);
}

.btn-outline {
  background: transparent;
  border: 2px solid #10b981;
  color: #10b981;
}

.btn-outline:hover {
  background: #10b981;
  color: white;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Navigation Critical Styles */
.nav-header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

/* Card Base Styles */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Grid System */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

/* Spacing Utilities */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

.m-4 { margin: 1rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

/* Typography */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

/* Colors */
.text-white { color: #ffffff; }
.text-slate-600 { color: #475569; }
.text-slate-700 { color: #334155; }
.text-slate-800 { color: #1e293b; }
.text-emerald-600 { color: #059669; }
.text-emerald-700 { color: #047857; }

.bg-white { background-color: #ffffff; }
.bg-emerald-600 { background-color: #059669; }
.bg-slate-50 { background-color: #f8fafc; }

/* Display and Layout */
.flex { display: flex; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-0 { top: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.max-w-7xl { max-width: 80rem; }
.max-w-4xl { max-width: 56rem; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }

.shadow { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1); }
.shadow-xl { box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1); }

.overflow-hidden { overflow: hidden; }
.overflow-x-auto { overflow-x: auto; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-50 { z-index: 50; }

/* Responsive Utilities */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
  .sm\:text-lg { font-size: 1.125rem; }
  .sm\:text-xl { font-size: 1.25rem; }
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:py-16 { padding-top: 4rem; padding-bottom: 4rem; }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:hidden { display: none; }
  .md\:text-xl { font-size: 1.25rem; }
  .md\:text-2xl { font-size: 1.5rem; }
  .md\:text-4xl { font-size: 2.25rem; }
  .md\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .md\:py-20 { padding-top: 5rem; padding-bottom: 5rem; }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
  .lg\:text-2xl { font-size: 1.5rem; }
  .lg\:text-3xl { font-size: 1.875rem; }
  .lg\:text-5xl { font-size: 3rem; }
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:py-24 { padding-top: 6rem; padding-bottom: 6rem; }
}

/* Focus States for Accessibility */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.5);
}

/* Smooth Transitions */
.transition-all {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* Print Styles */
@media print {
  .print\:hidden {
    display: none;
  }
  
  .hero-section {
    background: #065f46 !important;
    color: white !important;
  }
  
  .card {
    break-inside: avoid;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid #000;
  }
  
  .btn-primary {
    border: 2px solid #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .loading-skeleton {
    animation: none;
    background: #e2e8f0;
  }
}