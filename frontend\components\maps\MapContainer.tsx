import React, { useMemo, useEffect, useRef } from 'react';
import Overlay from 'ol/Overlay';
import { fromLonLat } from 'ol/proj';
import { useMapInstance } from './hooks/useMapInstance';
import { useMapEvents } from './hooks/useMapEvents';
import { useMarkerData } from './hooks/useMarkerData';
import MarkerLayer from './MarkerLayer';
import PopupOverlay from './PopupOverlay';
import type { MapContainerProps } from './types/mapTypes';
import styles from './styles/map.module.css';

const MapContainer: React.FC<MapContainerProps> = ({
  center = [0, 20], // Default center coordinates
  zoom = 2,
  height = '400px',
  className = '',
  onMarkerClick,
  showClustering = true,
}) => {
  // Fetch martyr data
  const { markers, geoJsonData, isLoading, error } = useMarkerData();

  // Initialize map instance
  const { mapRef, mapContainerRef, isMapReady } = useMapInstance({
    center,
    zoom,
  });

  // Handle map events
  const { selectedMartyr, popupPosition, closePopup } = useMapEvents({
    map: mapRef.current,
    onMarkerClick,
  });

  // Create popup overlay when needed
  const popupElementRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<Overlay | null>(null);

  useEffect(() => {
    if (!mapRef.current || !selectedMartyr || !popupPosition) {
      if (overlayRef.current) {
        mapRef.current?.removeOverlay(overlayRef.current);
        overlayRef.current = null;
      }
      return;
    }

    if (!overlayRef.current && popupElementRef.current) {
      const overlay = new Overlay({
        element: popupElementRef.current,
        positioning: 'bottom-center',
        stopEvent: false,
        offset: [0, -10],
      });
      
      mapRef.current.addOverlay(overlay);
      overlayRef.current = overlay;
    }

    if (overlayRef.current) {
      overlayRef.current.setPosition(fromLonLat([popupPosition[0], popupPosition[1]]));
    }
  }, [mapRef.current, selectedMartyr, popupPosition]);

  // Handle loading state
  if (isLoading) {
    return (
      <div
        className={`${styles.mapContainer} ${styles.mapLoading} ${className}`}
        style={{ height }}
        role="status"
        aria-label="Loading map data"
      >
        <div className={styles.loadingSpinner} aria-hidden="true"></div>
        Loading map data...
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div
        className={`${styles.mapContainer} ${className}`}
        style={{ height }}
        role="alert"
      >
        <div className={styles.mapLoading}>
          <p>Error loading map data. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${styles.mapContainer} ${className}`} style={{ height }}>
      <div
        ref={mapContainerRef}
        style={{ width: '100%', height: '100%' }}
        role="application"
        aria-label={`Interactive map showing ${markers.length} martyr locations`}
      />
      
      {/* Render marker layer when map is ready */}
      {isMapReady && mapRef.current && geoJsonData && (
        <MarkerLayer
          map={mapRef.current}
          geoJsonData={geoJsonData}
          markers={markers}
          onMarkerClick={onMarkerClick}
          showClustering={showClustering}
        />
      )}

      {/* Render popup overlay */}
      {selectedMartyr && (
        <div ref={popupElementRef} style={{ position: 'absolute', zIndex: 1000 }}>
          <PopupOverlay
            martyr={selectedMartyr}
            position={popupPosition}
            onClose={closePopup}
          />
        </div>
      )}
    </div>
  );
};

export default MapContainer;