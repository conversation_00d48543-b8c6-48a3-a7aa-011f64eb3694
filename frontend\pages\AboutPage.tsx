import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Heart, Shield, BookOpen, Users, Star, Moon, Archive, Globe,
  Clock, Scroll, Eye, Target, Award, Compass, Lightbulb,
  MessageCircle, CheckCircle, AlertTriangle, Info
} from 'lucide-react';
import { IslamicGeometricPattern, IslamicCalligraphyBorder, MosqueDecoration, IslamicFrameBorder } from '@/components/IslamicPatterns';
import { TypewriterText, QuranVerseRotator } from '../components/animations';
import ReadingProgress from '../components/ReadingProgress';
import { fadeAnimations, springPresets } from '../lib/animationPatterns';
import { QuranVerse } from '../lib/quranVerses';

export default function AboutPage() {

  // Quran verses for the hero section
  const aboutVerses: QuranVerse[] = [
    {
      id: "about-verse-1",
      surah: "At-Taghabun",
      ayah: 9,
      arabicText: "وَمَن يُؤْمِن بِاللَّهِ وَيَعْمَلْ صَالِحًا يُدْخِلْهُ جَنَّاتٍ تَجْرِي مِن تَحْتِهَا الْأَنْهَارُ",
      englishTranslation: "And whoever believes in Allah and does righteousness - He will admit him into gardens beneath which rivers flow",
      reference: "Quran 64:9",
      theme: "eternal-life" as const
    },
    {
      id: "about-verse-2",
      surah: "Al-Baqarah",
      ayah: 25,
      arabicText: "إِنَّ الَّذِينَ آمَنُوا وَعَمِلُوا الصَّالِحَاتِ لَهُمْ جَنَّاتٌ تَجْرِي مِن تَحْتِهَا الْأَنْهَارُ",
      englishTranslation: "Indeed, those who believe and do righteous deeds will have gardens beneath which rivers flow",
      reference: "Quran 2:25",
      theme: "eternal-life" as const
    },
    {
      id: "about-verse-3",
      surah: "Aal-e-Imran",
      ayah: 169,
      arabicText: "وَلَا تَحْسَبَنَّ الَّذِينَ قُتِلُوا فِي سَبِيلِ اللَّهِ أَمْوَاتًا ۚ بَلْ أَحْيَاءٌ عِندَ رَبِّهِمْ يُرْزَقُونَ",
      englishTranslation: "And never think of those who have been killed in the cause of Allah as dead. Rather, they are alive with their Lord, receiving provision",
      reference: "Quran 3:169",
      theme: "martyrdom" as const
    }
  ];

  // Core values with enhanced structure
  const coreValues = [
    {
      icon: Heart,
      title: "Sacred Memory Preservation",
      subtitle: "حفظ الذكرى المقدسة",
      description: "We honor the memory of Nigerian IMN martyrs with utmost respect, ensuring their stories are preserved with dignity and historical accuracy for future generations.",
      color: "from-red-500 to-rose-600",
      bgColor: "from-red-50 to-rose-50",
      borderColor: "border-red-200",
      stats: "1000+ Martyrs Documented"
    },
    {
      icon: Shield,
      title: "Truth & Verification",
      subtitle: "الحقيقة والتحقق",
      description: "Every piece of information undergoes rigorous verification through multiple sources, historical documentation, and community validation to maintain authenticity.",
      color: "from-blue-500 to-indigo-600",
      bgColor: "from-blue-50 to-indigo-50",
      borderColor: "border-blue-200",
      stats: "Multiple Source Verification"
    },
    {
      icon: BookOpen,
      title: "Educational Excellence",
      subtitle: "التميز التعليمي",
      description: "Our platform serves as a comprehensive educational resource for researchers, students, and the global community to learn about IMN history and sacrifice.",
      color: "from-emerald-500 to-teal-600",
      bgColor: "from-emerald-50 to-teal-50",
      borderColor: "border-emerald-200",
      stats: "Educational Resources Available"
    },
    {
      icon: Users,
      title: "Community Service",
      subtitle: "خدمة المجتمع",
      description: "We operate as a non-profit platform dedicated to serving the Nigerian IMN community and humanity, fostering unity through shared remembrance.",
      color: "from-purple-500 to-violet-600",
      bgColor: "from-purple-50 to-violet-50",
      borderColor: "border-purple-200",
      stats: "Community-Driven Initiative"
    },
    {
      icon: Globe,
      title: "Global Awareness",
      subtitle: "الوعي العالمي",
      description: "Raising international awareness about the persecution of Nigerian IMN members and advocating for justice, human rights, and religious freedom.",
      color: "from-amber-500 to-orange-600",
      bgColor: "from-amber-50 to-orange-50",
      borderColor: "border-amber-200",
      stats: "International Advocacy"
    },
    {
      icon: Archive,
      title: "Digital Legacy",
      subtitle: "الإرث الرقمي",
      description: "Creating a permanent digital archive that ensures the stories and sacrifices of IMN martyrs are never forgotten and remain accessible to all.",
      color: "from-teal-500 to-cyan-600",
      bgColor: "from-teal-50 to-cyan-50",
      borderColor: "border-teal-200",
      stats: "Permanent Digital Archive"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden">
      {/* Reading Progress Indicator */}
      <ReadingProgress
        showPercentage={true}
        showBackToTop={true}
        position="top"
        color="from-emerald-600 to-teal-600"
        thickness={3}
        showOnScroll={true}
      />

      {/* Enhanced Islamic Background Pattern */}
      <IslamicGeometricPattern className="fixed inset-0 z-0" opacity={0.03} color="#059669" />

      {/* Floating decorative elements */}
      <div className="fixed top-20 left-10 w-8 h-8 bg-emerald-400/20 rounded-full animate-pulse"></div>
      <div className="fixed top-40 right-20 w-12 h-12 bg-amber-400/15 rounded-full animate-bounce"></div>
      <div className="fixed bottom-20 left-20 w-6 h-6 bg-teal-400/25 rounded-full animate-ping"></div>

      {/* Revolutionary Hero Section */}
      <section className="relative bg-gradient-to-br from-emerald-900 via-emerald-800 to-teal-800 text-white py-24 overflow-hidden">
        <IslamicGeometricPattern className="absolute inset-0" opacity={0.1} color="#ffffff" />

        {/* Enhanced floating Islamic elements */}
        <motion.div
          className="absolute top-10 right-10 opacity-20"
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <MosqueDecoration color="#ffffff" />
        </motion.div>
        <motion.div
          className="absolute bottom-10 left-10 opacity-15"
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          <Star className="w-16 h-16 text-amber-300" fill="currentColor" />
        </motion.div>
        <motion.div
          className="absolute top-1/2 left-10 opacity-10"
          animate={{ y: [-10, 10, -10] }}
          transition={{ duration: 4, repeat: Infinity }}
        >
          <Moon className="w-12 h-12 text-teal-300" fill="currentColor" />
        </motion.div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Enhanced Icon Section */}
            <motion.div
              className="flex justify-center mb-8"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 1, type: "spring", stiffness: 100 }}
            >
              <div className="relative group">
                <div className="w-28 h-28 bg-gradient-to-br from-emerald-500 via-emerald-600 to-teal-600 rounded-3xl flex items-center justify-center shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:scale-105 border-4 border-white/20">
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
                  <div className="relative">
                    <Archive className="w-14 h-14 text-white drop-shadow-xl" />
                    <div className="absolute -top-3 -right-3 w-8 h-8 bg-amber-400 rounded-full flex items-center justify-center">
                      <Heart className="w-4 h-4 text-white" />
                    </div>
                  </div>
                </div>
                <div className="absolute -top-2 -left-2 w-6 h-6 bg-teal-400 rounded-full animate-pulse"></div>
                <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-emerald-400 rounded-full"></div>
              </div>
            </motion.div>

            {/* Enhanced Title with Typewriter Effect */}
            <TypewriterText
              text="Nigerian IMN Martyrs Archive"
              className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-emerald-100 to-teal-100 bg-clip-text text-transparent leading-tight block"
              speed={80}
              startDelay={1000}
            />

            <IslamicCalligraphyBorder className="max-w-md mx-auto mb-6" color="#10b981" />

            {/* Arabic Title */}
            <motion.div
              className="text-2xl text-emerald-200 mb-8 font-medium tracking-wide font-arabic"
              dir="rtl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 3 }}
            >
              أرشيف شهداء الحركة الإسلامية في نيجيريا
            </motion.div>

            {/* Rotating Quran Verses */}
            <div className="min-h-[140px] mb-8">
              <QuranVerseRotator
                verses={aboutVerses}
                interval={10000}
                showTypewriter={true}
                typewriterSpeed={30}
                verseClassName="text-xl text-emerald-200 font-medium tracking-wide leading-relaxed"
                sourceClassName="text-lg text-amber-200 font-medium"
                containerClassName="max-w-5xl mx-auto"
                pauseOnHover={true}
              />
            </div>

            {/* Enhanced Description */}
            <TypewriterText
              text="A sacred digital sanctuary preserving the memories, stories, and eternal legacies of Nigerian IMN martyrs who made the ultimate sacrifice for their faith, justice, and human dignity."
              className="text-xl md:text-2xl mb-12 text-emerald-100 max-w-6xl mx-auto leading-relaxed block"
              speed={25}
              startDelay={5000}
              cursor={false}
            />

            {/* Statistics Cards */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 7 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
                <div className="text-3xl font-bold text-white">1000+</div>
                <div className="text-sm text-emerald-200">Martyrs Documented</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
                <div className="text-3xl font-bold text-white">1996-Present</div>
                <div className="text-sm text-emerald-200">Years Covered</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
                <div className="text-3xl font-bold text-white">Nigeria</div>
                <div className="text-sm text-emerald-200">Primary Focus</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 text-center border border-white/20">
                <div className="text-3xl font-bold text-white">IMN</div>
                <div className="text-sm text-emerald-200">Community Served</div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Mission Statement Section */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <IslamicFrameBorder className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border-2 border-emerald-100" borderColor="#059669">
            <div className="flex justify-center mb-8">
              <motion.div
                className="w-20 h-20 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-2xl flex items-center justify-center shadow-xl"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <Target className="w-10 h-10 text-white" />
              </motion.div>
            </div>
            <h2 className="text-4xl font-bold text-slate-800 mb-6">Our Sacred Mission</h2>
            <h3 className="text-xl text-emerald-700 mb-6 font-arabic" dir="rtl">
              رسالتنا المقدسة في حفظ ذكرى شهداء الحركة الإسلامية
            </h3>
            <IslamicCalligraphyBorder className="max-w-lg mx-auto mb-8" color="#059669" />

            <div className="max-w-5xl mx-auto">
              <p className="text-xl text-slate-700 leading-relaxed mb-8">
                The Nigerian IMN Martyrs Archive serves as a comprehensive, respectful, and educational platform
                dedicated to documenting and honoring the martyrs of the Islamic Movement of Nigeria (IMN).
                We are committed to preserving their stories, sacrifices, and legacies for future generations
                while maintaining the highest standards of accuracy, respect, and dignity.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <motion.div
                  className="bg-emerald-50 rounded-2xl p-6 border-2 border-emerald-200"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <Eye className="w-8 h-8 text-emerald-600 mx-auto mb-3" />
                  <h4 className="font-bold text-emerald-800 mb-2">Our Vision</h4>
                  <p className="text-sm text-emerald-700">
                    A world where the sacrifices of IMN martyrs are remembered, honored, and serve as inspiration for justice and peace.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-teal-50 rounded-2xl p-6 border-2 border-teal-200"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <Compass className="w-8 h-8 text-teal-600 mx-auto mb-3" />
                  <h4 className="font-bold text-teal-800 mb-2">Our Purpose</h4>
                  <p className="text-sm text-teal-700">
                    To create a permanent digital memorial that ensures the stories of Nigerian IMN martyrs are never forgotten.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-amber-50 rounded-2xl p-6 border-2 border-amber-200"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <Lightbulb className="w-8 h-8 text-amber-600 mx-auto mb-3" />
                  <h4 className="font-bold text-amber-800 mb-2">Our Impact</h4>
                  <p className="text-sm text-amber-700">
                    Educating the world about religious persecution and advocating for human rights and religious freedom.
                  </p>
                </motion.div>
              </div>
            </div>
          </IslamicFrameBorder>
        </motion.div>

        {/* Enhanced Core Values Section */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="text-center mb-16">
            <motion.div
              className="flex justify-center mb-8"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="w-20 h-20 bg-gradient-to-br from-amber-600 to-orange-600 rounded-2xl flex items-center justify-center shadow-xl">
                <Award className="w-10 h-10 text-white" />
              </div>
            </motion.div>
            <h2 className="text-4xl font-bold text-slate-800 mb-6">Our Core Values</h2>
            <h3 className="text-xl text-amber-700 mb-6 font-arabic" dir="rtl">
              قيمنا الأساسية في خدمة ذكرى الشهداء
            </h3>
            <IslamicCalligraphyBorder className="max-w-lg mx-auto mb-6" color="#059669" />
            <p className="text-lg text-slate-600 max-w-4xl mx-auto leading-relaxed">
              These fundamental principles guide our work in preserving the memory of Nigerian IMN martyrs
              and serving their families and communities with dignity and respect.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {coreValues.map((value, index) => {
              const IconComponent = value.icon;
              return (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className={`group hover:shadow-2xl transition-all duration-500 bg-gradient-to-br ${value.bgColor} border-2 ${value.borderColor} shadow-lg hover:scale-105 overflow-hidden relative rounded-3xl h-full`}>
                    {/* Decorative background pattern */}
                    <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                      <IslamicGeometricPattern opacity={0.3} color="#059669" />
                    </div>

                    <CardContent className="p-8 relative h-full flex flex-col">
                      <div className="flex items-start space-x-4 mb-6">
                        <div className="flex-shrink-0">
                          <motion.div
                            className={`w-16 h-16 bg-gradient-to-br ${value.color} rounded-2xl flex items-center justify-center shadow-xl group-hover:shadow-2xl transition-all duration-300`}
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            transition={{ duration: 0.3 }}
                          >
                            <IconComponent className="w-8 h-8 text-white" />
                          </motion.div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-slate-800 mb-2 group-hover:text-slate-900 transition-colors duration-300">
                            {value.title}
                          </h3>
                          <p className="text-sm text-slate-500 font-arabic mb-3" dir="rtl">
                            {value.subtitle}
                          </p>
                        </div>
                      </div>

                      <IslamicCalligraphyBorder className="mb-4" color="#059669" />

                      <div className="flex-1">
                        <p className="text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-300 mb-4">
                          {value.description}
                        </p>

                        <div className="mt-auto">
                          <div className="bg-white/80 rounded-xl p-3 border border-slate-200">
                            <div className="flex items-center justify-between">
                              <CheckCircle className="w-4 h-4 text-emerald-600" />
                              <span className="text-xs font-medium text-slate-700">{value.stats}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* IMN Focus and Timeline Section */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Card className="bg-gradient-to-br from-white/95 to-slate-50/50 backdrop-blur-sm shadow-2xl border-2 border-slate-200 rounded-3xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-900 text-white py-10">
              <CardTitle className="text-3xl text-center flex items-center justify-center">
                <Clock className="w-8 h-8 mr-4" />
                Our Focus: Nigerian IMN History
                <Scroll className="w-8 h-8 ml-4" />
              </CardTitle>
              <IslamicCalligraphyBorder className="mt-4" color="#ffffff" />
              <p className="text-center text-slate-300 mt-4 font-arabic" dir="rtl">
                تركيزنا على تاريخ الحركة الإسلامية في نيجيريا
              </p>
            </CardHeader>
            <CardContent className="p-10">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Left Column - Focus Areas */}
                <div className="space-y-8">
                  <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 rounded-2xl p-6 border-2 border-emerald-200">
                    <h3 className="text-xl font-bold text-emerald-800 mb-4 flex items-center">
                      <div className="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center mr-3">
                        <Heart className="w-4 h-4 text-white" />
                      </div>
                      Nigerian IMN Martyrs (Primary Focus)
                    </h3>
                    <p className="text-emerald-700 leading-relaxed mb-4">
                      We primarily document the stories of martyrs from the Islamic Movement
                      of Nigeria (IMN), who have faced persecution and violence for their religious
                      beliefs and peaceful advocacy for justice and human rights.
                    </p>
                    <div className="bg-white/80 rounded-lg p-3 border border-emerald-300">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-emerald-800 font-medium">Coverage Period:</span>
                        <span className="text-emerald-600 font-bold">1996 - Present</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl p-6 border-2 border-blue-200">
                    <h3 className="text-xl font-bold text-blue-800 mb-4 flex items-center">
                      <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <BookOpen className="w-4 h-4 text-white" />
                      </div>
                      Marhum (Religious Leaders)
                    </h3>
                    <p className="text-blue-700 leading-relaxed mb-4">
                      We document the lives of spiritual leaders, clerics, and religious scholars
                      who were martyred for their faith and religious convictions within the IMN community.
                    </p>
                    <div className="bg-white/80 rounded-lg p-3 border border-blue-300">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-blue-800 font-medium">Special Focus:</span>
                        <span className="text-blue-600 font-bold">Religious Leadership</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Column - Timeline Highlights */}
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-slate-800 mb-6 text-center">Key Historical Periods</h3>

                  <motion.div
                    className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-4 border-l-4 border-emerald-500"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-bold text-emerald-800">Foundation Period</h4>
                      <span className="text-sm bg-emerald-200 text-emerald-800 px-2 py-1 rounded-lg">1996</span>
                    </div>
                    <p className="text-sm text-emerald-700">
                      Peaceful establishment and organizational development of the Islamic Movement of Nigeria.
                    </p>
                  </motion.div>

                  <motion.div
                    className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4 border-l-4 border-orange-500"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-bold text-orange-800">First Major Incident</h4>
                      <span className="text-sm bg-orange-200 text-orange-800 px-2 py-1 rounded-lg">2014</span>
                    </div>
                    <p className="text-sm text-orange-700">
                      Zaria Quds Day Massacre - First major military assault resulting in 35 martyrs.
                    </p>
                  </motion.div>

                  <motion.div
                    className="bg-gradient-to-r from-red-50 to-rose-50 rounded-xl p-4 border-l-4 border-red-500"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-bold text-red-800">Zaria Massacre</h4>
                      <span className="text-sm bg-red-200 text-red-800 px-2 py-1 rounded-lg">2015</span>
                    </div>
                    <p className="text-sm text-red-700">
                      Three-day massacre resulting in over 1000 casualties and arrest of Sheikh Zakzaky.
                    </p>
                  </motion.div>

                  <motion.div
                    className="bg-gradient-to-r from-slate-50 to-slate-100 rounded-xl p-4 border-l-4 border-slate-500"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-bold text-slate-800">Ongoing Documentation</h4>
                      <span className="text-sm bg-slate-200 text-slate-800 px-2 py-1 rounded-lg">Present</span>
                    </div>
                    <p className="text-sm text-slate-700">
                      Continued persecution and ongoing efforts to preserve memory and seek justice.
                    </p>
                  </motion.div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Methodology and Ethics Section */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card className="bg-gradient-to-br from-amber-50 to-orange-50 border-2 border-amber-200 rounded-3xl shadow-xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-amber-600 to-orange-600 text-white rounded-t-3xl py-6">
                <CardTitle className="text-xl text-center flex items-center justify-center">
                  <Shield className="w-6 h-6 mr-3" />
                  Research Methodology
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <p className="text-amber-800 mb-6 leading-relaxed">
                  Our research methodology follows strict academic and ethical standards to ensure
                  accuracy, respect, and cultural sensitivity in documenting Nigerian IMN martyrs.
                </p>

                <div className="space-y-4">
                  <div className="bg-white/80 border border-amber-300 rounded-xl p-4">
                    <h4 className="font-bold text-amber-800 mb-3 flex items-center">
                      <CheckCircle className="w-5 h-5 mr-2" />
                      Verification Process:
                    </h4>
                    <ul className="list-disc list-inside text-amber-700 space-y-2 text-sm">
                      <li>Multiple source cross-referencing</li>
                      <li>Community validation and testimony</li>
                      <li>Historical document authentication</li>
                      <li>Family and witness interviews</li>
                      <li>Academic peer review process</li>
                    </ul>
                  </div>

                  <div className="bg-white/80 border border-amber-300 rounded-xl p-4">
                    <h4 className="font-bold text-amber-800 mb-3 flex items-center">
                      <Info className="w-5 h-5 mr-2" />
                      Data Sources:
                    </h4>
                    <ul className="list-disc list-inside text-amber-700 space-y-2 text-sm">
                      <li>Human rights organization reports</li>
                      <li>Government and court documents</li>
                      <li>Media coverage and journalism</li>
                      <li>Academic research and publications</li>
                      <li>Community records and testimonies</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-emerald-50 to-teal-50 border-2 border-emerald-200 rounded-3xl shadow-xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-t-3xl py-6">
                <CardTitle className="text-xl text-center flex items-center justify-center">
                  <Heart className="w-6 h-6 mr-3" />
                  Ethics & Respect
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <p className="text-emerald-800 mb-6 leading-relaxed">
                  We maintain the highest ethical standards in documenting and presenting
                  the stories of Nigerian IMN martyrs, honoring their memory with dignity.
                </p>

                <div className="space-y-4">
                  <div className="bg-white/80 border border-emerald-300 rounded-xl p-4">
                    <h4 className="font-bold text-emerald-800 mb-3 flex items-center">
                      <Users className="w-5 h-5 mr-2" />
                      Family Privacy & Consent:
                    </h4>
                    <ul className="list-disc list-inside text-emerald-700 space-y-2 text-sm">
                      <li>Explicit family consent for sensitive information</li>
                      <li>Protective measures for living relatives</li>
                      <li>Cultural and religious sensitivity</li>
                      <li>Right to request information removal</li>
                    </ul>
                  </div>

                  <div className="bg-white/80 border border-emerald-300 rounded-xl p-4">
                    <h4 className="font-bold text-emerald-800 mb-3 flex items-center">
                      <AlertTriangle className="w-5 h-5 mr-2" />
                      Content Guidelines:
                    </h4>
                    <ul className="list-disc list-inside text-emerald-700 space-y-2 text-sm">
                      <li>No graphic or disturbing imagery</li>
                      <li>Focus on life and legacy, not death</li>
                      <li>Respectful language and terminology</li>
                      <li>Cultural and Islamic sensitivity</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Contact and Community Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <IslamicFrameBorder className="bg-gradient-to-br from-white/95 to-slate-50/50 backdrop-blur-sm rounded-3xl shadow-2xl border-2 border-slate-200" borderColor="#059669">
            <div className="text-center">
              <motion.div
                className="flex justify-center mb-8"
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.3 }}
              >
                <div className="w-20 h-20 bg-gradient-to-br from-slate-600 to-slate-700 rounded-2xl flex items-center justify-center shadow-xl">
                  <MessageCircle className="w-10 h-10 text-white" />
                </div>
              </motion.div>

              <h2 className="text-4xl font-bold text-slate-800 mb-6">Community & Contact</h2>
              <h3 className="text-xl text-slate-600 mb-6 font-arabic" dir="rtl">
                المجتمع والتواصل
              </h3>
              <IslamicCalligraphyBorder className="max-w-lg mx-auto mb-8" color="#059669" />

              <p className="text-lg text-slate-700 mb-12 leading-relaxed max-w-4xl mx-auto">
                We welcome contributions, corrections, and feedback from the Nigerian IMN community,
                families of martyrs, researchers, and anyone committed to preserving this sacred history.
                Together, we ensure that no martyr is forgotten.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <motion.div
                  className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-2xl p-6 border-2 border-emerald-200"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <BookOpen className="w-8 h-8 text-emerald-600 mx-auto mb-4" />
                  <h4 className="font-bold text-emerald-800 mb-3">Information Corrections</h4>
                  <p className="text-emerald-700 text-sm leading-relaxed">
                    Help us maintain accuracy by reporting any inaccuracies or providing additional
                    verified information about Nigerian IMN martyrs.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border-2 border-blue-200"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <Archive className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                  <h4 className="font-bold text-blue-800 mb-3">Content Contributions</h4>
                  <p className="text-blue-700 text-sm leading-relaxed">
                    Submit verified information, testimonies, or documentation about martyrs
                    not yet included in our archive.
                  </p>
                </motion.div>

                <motion.div
                  className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border-2 border-purple-200"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <Users className="w-8 h-8 text-purple-600 mx-auto mb-4" />
                  <h4 className="font-bold text-purple-800 mb-3">Community Support</h4>
                  <p className="text-purple-700 text-sm leading-relaxed">
                    Connect with other community members, researchers, and families
                    working to preserve IMN history and memory.
                  </p>
                </motion.div>
              </div>
            </div>
          </IslamicFrameBorder>
        </motion.div>

        {/* Final Memorial Quote */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <div className="bg-gradient-to-r from-slate-800 via-slate-900 to-slate-800 rounded-3xl p-12 text-white relative overflow-hidden">
            <IslamicGeometricPattern className="absolute inset-0" opacity={0.1} color="#ffffff" />

            <div className="relative z-10">
              <Star className="w-16 h-16 text-amber-400 mx-auto mb-6" fill="currentColor" />

              <blockquote className="text-2xl font-medium mb-6 leading-relaxed max-w-4xl mx-auto">
                "This archive stands as a testament to the courage, faith, and sacrifice of Nigerian IMN martyrs.
                Their stories illuminate the path of justice and remind us that truth and righteousness will ultimately prevail."
              </blockquote>

              <div className="text-xl text-emerald-400 font-arabic mb-4" dir="rtl">
                إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ
              </div>

              <p className="text-slate-300">
                "Indeed we belong to Allah, and indeed to Him we will return" - Quran 2:156
              </p>

              <IslamicCalligraphyBorder className="mt-8 max-w-md mx-auto" color="#10b981" />
            </div>
          </div>
        </motion.div>

      </div>
    </div>
  );
}
