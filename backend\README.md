# Backend Setup

## Environment Variables

For local development, you need to set up environment variables. Copy the example file and modify as needed:

```bash
cp .env.example .env
```

Then edit the `.env` file to set your local development secrets.

## Required Secrets

The application requires the following secrets to be set either through Encore's secret management or environment variables:

1. `AdminSecret` - Used for admin authentication
2. `JWTSecret` - Used for JWT token signing

### Using Encore CLI (Recommended)

From the backend directory, set the secrets using the Encore CLI:

```bash
encore secret set AdminSecret "your-admin-secret"
encore secret set JWTSecret "your-jwt-secret"
```

### Using Environment Variables (Alternative)

Set the following environment variables:

```bash
export DEV_ADMIN_SECRET="your-admin-secret"
export DEV_JWT_SECRET="your-jwt-secret"
export DEV_ADMIN_PASSWORD="your-admin-password"  # Optional, defaults to Admin@2025!
```

Or use the `.env` file (not committed to version control):

```bash
# In .env file
DEV_ADMIN_SECRET=your-admin-secret
DEV_JWT_SECRET=your-jwt-secret
DEV_ADMIN_PASSWORD=your-admin-password
```

## Running the Application

1. Start the Encore development server:
   ```bash
   encore run
   ```

2. The backend will be available at the URL shown in your terminal (typically `http://localhost:4000`).

## Generating Secrets

You can use the helper script to generate secure random secrets:

```bash
../scripts/setup-dev-secret.sh
```

This script will generate secure secrets and show you how to set them using either the Encore CLI or environment variables.