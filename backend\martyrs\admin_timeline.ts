import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";
import { logTimelineEventCreate, logTimelineEventDelete } from "../auth/auditLog";

interface AddTimelineEventRequest {
  martyrId: number;
  eventDate: string;
  description: string;
  imageUrl?: string;
}

interface TimelineEvent {
  id: number;
  martyrId: number;
  eventDate: string;
  description: string;
  imageUrl?: string;
  createdAt: string;
}

interface DeleteTimelineEventRequest {
  id: number;
}

interface DeleteTimelineEventResponse {
  success: boolean;
  message: string;
}

// Adds a timeline event to a martyr profile.
export const addTimelineEvent = api<AddTimelineEventRequest, TimelineEvent>(
  { auth: true, expose: true, method: "POST", path: "/admin/martyrs/timeline" },
  async (req) => {
    const auth = getAuthData();
    if (!auth || auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate required fields
    if (!req.martyrId || !req.eventDate || !req.description) {
      throw APIError.invalidArgument("Missing required fields");
    }

    const timestamp = new Date().toISOString();

    try {
      const result = await martyrsDB.queryRow<{
        id: number;
        martyr_id: number;
        event_date: string;
        description: string;
        image_url?: string;
        created_at: string;
      }>`
        INSERT INTO timeline_events (
          martyr_id, event_date, description, image_url, created_at
        ) VALUES (
          ${req.martyrId}, ${req.eventDate}, ${req.description}, ${req.imageUrl}, ${timestamp}
        )
        RETURNING *
      `;

      if (!result) {
        throw APIError.internal("Failed to add timeline event");
      }

      // Log the timeline event creation
      await logTimelineEventCreate(result.id, result.martyr_id);

      return {
        id: result.id,
        martyrId: result.martyr_id,
        eventDate: result.event_date,
        description: result.description,
        imageUrl: result.image_url,
        createdAt: result.created_at
      };
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      console.error("Database error in addTimelineEvent:", error);
      throw APIError.internal("Database error: " + (error as Error).message);
    }
  }
);

// Deletes a timeline event from a martyr profile.
export const deleteTimelineEvent = api<DeleteTimelineEventRequest, DeleteTimelineEventResponse>(
  { auth: true, expose: true, method: "DELETE", path: "/admin/martyrs/timeline/:id" },
  async ({ id }) => {
    const auth = getAuthData();
    if (!auth || auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate ID
    if (!id || id <= 0) {
      throw APIError.invalidArgument("Invalid timeline event ID");
    }

    try {
      // First get the event details for logging
      const event = await martyrsDB.queryRow<{ id: number; martyr_id: number }>`
        SELECT id, martyr_id FROM timeline_events WHERE id = ${id}
      `;

      if (!event) {
        throw APIError.notFound("Timeline event not found");
      }

      // Log the timeline event deletion before actually deleting it
      await logTimelineEventDelete(event.id, event.martyr_id);

      const result = await martyrsDB.exec`
        DELETE FROM timeline_events WHERE id = ${id}
      `;

      if (result.rowsAffected === 0) {
        throw APIError.notFound("Timeline event not found");
      }

      return {
        success: true,
        message: "Timeline event deleted successfully"
      };
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      console.error("Database error in deleteTimelineEvent:", error);
      throw APIError.internal("Database error: " + (error as Error).message);
    }
  }
);