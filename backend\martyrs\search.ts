import { api, Query } from "encore.dev/api";
import { martyrsDB } from "./db";
import type { SearchResult, SearchFilters } from "./types";

interface SearchParams {
  query?: Query<string>;
  subCategories?: Query<string>;
  regions?: Query<string>;
  periods?: Query<string>;
  causes?: Query<string>;
  limit?: Query<number>;
  offset?: Query<number>;
}

// Searches martyrs based on query and filters.
export const search = api<SearchParams, SearchResult>(
  { expose: true, method: "GET", path: "/search" },
  async (params) => {
    const {
      query = "",
      subCategories = "",
      regions = "",
      periods = "",
      causes = "",
      limit = 20,
      offset = 0
    } = params;

    let whereClause = "WHERE 1=1";
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Full-text search
    if (query) {
      whereClause += ` AND (
        name ILIKE $${paramIndex} OR 
        bio ILIKE $${paramIndex} OR 
        martyrdom_cause ILIKE $${paramIndex} OR
        martyrdom_context ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${query}%`);
      paramIndex++;
    }

    // Filter by sub-categories
    if (subCategories) {
      const subCatArray = subCategories.split(",").filter(Boolean);
      if (subCatArray.length > 0) {
        whereClause += ` AND sub_categories && $${paramIndex}`;
        queryParams.push(subCatArray);
        paramIndex++;
      }
    }

    // Filter by regions
    if (regions) {
      const regionArray = regions.split(",").filter(Boolean);
      if (regionArray.length > 0) {
        whereClause += ` AND region = ANY($${paramIndex})`;
        queryParams.push(regionArray);
        paramIndex++;
      }
    }

    // Filter by periods
    if (periods) {
      const periodArray = periods.split(",").filter(Boolean);
      if (periodArray.length > 0) {
        whereClause += ` AND period = ANY($${paramIndex})`;
        queryParams.push(periodArray);
        paramIndex++;
      }
    }

    // Filter by causes
    if (causes) {
      const causeArray = causes.split(",").filter(Boolean);
      if (causeArray.length > 0) {
        whereClause += ` AND martyrdom_cause ILIKE ANY($${paramIndex})`;
        queryParams.push(causeArray.map(cause => `%${cause}%`));
        paramIndex++;
      }
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM martyrs
      ${whereClause}
    `;
    const countResult = await martyrsDB.rawQueryRow<{ total: number }>(countQuery, ...queryParams);
    const total = countResult?.total || 0;

    // Get martyrs with profile images
    const searchQuery = `
      SELECT 
        m.id,
        m.name,
        m.slug,
        LEFT(m.bio, 150) as bio,
        m.sub_categories,
        m.region,
        mi.url as profile_image
      FROM martyrs m
      LEFT JOIN martyr_images mi ON m.id = mi.martyr_id AND mi.is_profile_image = true
      ${whereClause}
      ORDER BY m.name
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    queryParams.push(limit, offset);

    const martyrs = await martyrsDB.rawQueryAll<{
      id: number;
      name: string;
      slug: string;
      bio: string;
      sub_categories: string[];
      region?: string;
      profile_image?: string;
    }>(searchQuery, ...queryParams);

    return {
      martyrs: martyrs.map(m => ({
        id: m.id,
        name: m.name,
        slug: m.slug,
        bio: m.bio,
        subCategories: m.sub_categories || [],
        region: m.region,
        profileImage: m.profile_image
      })),
      total
    };
  }
);
