import React from 'react';

// Islamic Geometric Pattern Component
export const IslamicGeometricPattern = ({ 
  className = "", 
  opacity = 0.05, 
  color = "#10b981" 
}: { 
  className?: string; 
  opacity?: number; 
  color?: string; 
}) => (
  <div className={`absolute inset-0 ${className}`} style={{ opacity }}>
    <svg
      className="w-full h-full"
      viewBox="0 0 400 400"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <pattern id="islamicPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
          {/* 8-pointed star pattern */}
          <g transform="translate(50,50)">
            <path
              d="M0,-40 L12,-12 L40,0 L12,12 L0,40 L-12,12 L-40,0 L-12,-12 Z"
              fill={color}
              fillOpacity="0.3"
            />
            <circle cx="0" cy="0" r="25" fill="none" stroke={color} strokeWidth="1" strokeOpacity="0.4"/>
            <circle cx="0" cy="0" r="15" fill="none" stroke={color} strokeWidth="1" strokeOpacity="0.6"/>
            <circle cx="0" cy="0" r="8" fill={color} fillOpacity="0.5"/>
          </g>
        </pattern>
        <pattern id="geometricBorder" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
          <path
            d="M25,0 L50,25 L25,50 L0,25 Z"
            fill="none"
            stroke={color}
            strokeWidth="1"
            strokeOpacity="0.3"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#islamicPattern)" />
    </svg>
  </div>
);

// Calligraphy-style decorative element
export const IslamicCalligraphyBorder = ({ 
  className = "", 
  color = "#059669" 
}: { 
  className?: string; 
  color?: string; 
}) => (
  <div className={`${className}`}>
    <svg
      viewBox="0 0 200 50"
      className="w-full h-8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20,25 Q40,10 60,25 T100,25 T140,25 T180,25"
        stroke={color}
        strokeWidth="2"
        fill="none"
        strokeLinecap="round"
      />
      <path
        d="M10,25 Q30,35 50,25 T90,25 T130,25 T170,25 T190,25"
        stroke={color}
        strokeWidth="1.5"
        fill="none"
        strokeLinecap="round"
        strokeOpacity="0.7"
      />
      <circle cx="15" cy="25" r="3" fill={color} fillOpacity="0.6"/>
      <circle cx="185" cy="25" r="3" fill={color} fillOpacity="0.6"/>
    </svg>
  </div>
);

// Decorative Islamic corner
export const IslamicCornerDecoration = ({ 
  className = "", 
  position = "top-left",
  color = "#059669" 
}: { 
  className?: string; 
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
  color?: string; 
}) => {
  const rotations = {
    "top-left": "rotate-0",
    "top-right": "rotate-90",
    "bottom-right": "rotate-180",
    "bottom-left": "rotate-270"
  };

  return (
    <div className={`absolute w-16 h-16 ${className}`}>
      <svg
        viewBox="0 0 64 64"
        className={`w-full h-full ${rotations[position]}`}
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0,0 Q16,0 16,16 L16,32 Q16,48 32,48 L48,48 Q64,48 64,64"
          stroke={color}
          strokeWidth="2"
          fill="none"
        />
        <path
          d="M8,8 Q12,8 12,12 L12,20 Q12,24 16,24 L24,24 Q28,24 28,28"
          stroke={color}
          strokeWidth="1.5"
          fill="none"
          strokeOpacity="0.7"
        />
        <circle cx="4" cy="4" r="2" fill={color} fillOpacity="0.8"/>
        <circle cx="12" cy="12" r="1.5" fill={color} fillOpacity="0.6"/>
        <circle cx="20" cy="20" r="1" fill={color} fillOpacity="0.4"/>
      </svg>
    </div>
  );
};

// Mosque silhouette decoration
export const MosqueDecoration = ({ 
  className = "", 
  color = "#059669" 
}: { 
  className?: string; 
  color?: string; 
}) => (
  <div className={`${className}`}>
    <svg
      viewBox="0 0 120 60"
      className="w-full h-16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Mosque silhouette */}
      <path
        d="M20,50 L20,30 Q20,20 30,20 L35,20 L35,10 Q35,5 40,5 L45,5 Q50,5 50,10 L50,20 L55,20 Q65,20 65,30 L65,50"
        fill={color}
        fillOpacity="0.3"
      />
      <path
        d="M75,50 L75,25 Q75,15 85,15 L90,15 L90,8 Q90,3 95,3 L100,3 Q105,3 105,8 L105,15 L110,15 Q120,15 120,25 L120,50"
        fill={color}
        fillOpacity="0.2"
      />
      {/* Minarets */}
      <rect x="40" y="5" width="5" height="15" fill={color} fillOpacity="0.4"/>
      <rect x="95" y="3" width="5" height="17" fill={color} fillOpacity="0.3"/>
      {/* Crescents */}
      <path d="M42,8 Q44,6 45,8 Q44,10 42,8" fill={color}/>
      <path d="M97,6 Q99,4 100,6 Q99,8 97,6" fill={color}/>
    </svg>
  </div>
);

// Memorial Flower - Elegant Rose with Realistic Petals and Reflections
export const MemorialRose = ({ 
  className = "", 
  size = "medium",
  color = "#dc2626",
  stemColor = "#16a34a"
}: { 
  className?: string; 
  size?: "small" | "medium" | "large";
  color?: string;
  stemColor?: string;
}) => {
  const sizes = {
    small: { width: "w-12", height: "h-16", viewBox: "0 0 80 100" },
    medium: { width: "w-20", height: "h-24", viewBox: "0 0 120 140" },
    large: { width: "w-32", height: "h-40", viewBox: "0 0 160 200" }
  };

  const currentSize = sizes[size];

  return (
    <div className={`${currentSize.width} ${currentSize.height} ${className}`}>
      <svg
        viewBox={currentSize.viewBox}
        className="w-full h-full filter drop-shadow-lg"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          {/* Gradient definitions for realistic flower effects */}
          <radialGradient id="petalGradient" cx="0.3" cy="0.3" r="0.7">
            <stop offset="0%" stopColor={color} stopOpacity="0.9"/>
            <stop offset="40%" stopColor={color} stopOpacity="0.7"/>
            <stop offset="80%" stopColor="#7f1d1d" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#450a0a" stopOpacity="0.9"/>
          </radialGradient>
          
          <radialGradient id="centerGradient" cx="0.5" cy="0.5" r="0.6">
            <stop offset="0%" stopColor="#fbbf24" stopOpacity="0.8"/>
            <stop offset="50%" stopColor="#f59e0b" stopOpacity="0.9"/>
            <stop offset="100%" stopColor="#d97706" stopOpacity="1"/>
          </radialGradient>
          
          <linearGradient id="stemGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor={stemColor}/>
            <stop offset="50%" stopColor="#22c55e"/>
            <stop offset="100%" stopColor="#15803d"/>
          </linearGradient>
          
          <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#22c55e"/>
            <stop offset="50%" stopColor="#16a34a"/>
            <stop offset="100%" stopColor="#15803d"/>
          </linearGradient>
          
          {/* Shadow and reflection filters */}
          <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#000000" floodOpacity="0.3"/>
          </filter>
          
          <filter id="glowEffect" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* Flower stem */}
        <path
          d={`M${60},${100} Q${58},${80} ${60},${60} Q${62},${50} ${60},${40}`}
          stroke="url(#stemGradient)"
          strokeWidth="4"
          fill="none"
          strokeLinecap="round"
          filter="url(#softShadow)"
        />
        
        {/* Leaves */}
        <path
          d={`M${50},${70} Q${40},${65} ${35},${75} Q${40},${80} ${50},${75}`}
          fill="url(#leafGradient)"
          filter="url(#softShadow)"
        />
        <path
          d={`M${70},${80} Q${80},${75} ${85},${85} Q${80},${90} ${70},${85}`}
          fill="url(#leafGradient)"
          filter="url(#softShadow)"
        />
        
        {/* Outer petals (back layer) */}
        <ellipse cx="60" cy="35" rx="25" ry="15" 
          fill="url(#petalGradient)" 
          transform="rotate(-30 60 35)" 
          opacity="0.8"
          filter="url(#softShadow)"
        />
        <ellipse cx="60" cy="35" rx="25" ry="15" 
          fill="url(#petalGradient)" 
          transform="rotate(30 60 35)" 
          opacity="0.8"
          filter="url(#softShadow)"
        />
        <ellipse cx="60" cy="35" rx="25" ry="15" 
          fill="url(#petalGradient)" 
          transform="rotate(90 60 35)" 
          opacity="0.8"
          filter="url(#softShadow)"
        />
        <ellipse cx="60" cy="35" rx="25" ry="15" 
          fill="url(#petalGradient)" 
          transform="rotate(150 60 35)" 
          opacity="0.8"
          filter="url(#softShadow)"
        />
        
        {/* Middle petals */}
        <ellipse cx="60" cy="35" rx="18" ry="12" 
          fill="url(#petalGradient)" 
          transform="rotate(0 60 35)" 
          opacity="0.9"
          filter="url(#glowEffect)"
        />
        <ellipse cx="60" cy="35" rx="18" ry="12" 
          fill="url(#petalGradient)" 
          transform="rotate(60 60 35)" 
          opacity="0.9"
          filter="url(#glowEffect)"
        />
        <ellipse cx="60" cy="35" rx="18" ry="12" 
          fill="url(#petalGradient)" 
          transform="rotate(120 60 35)" 
          opacity="0.9"
          filter="url(#glowEffect)"
        />
        
        {/* Inner petals */}
        <ellipse cx="60" cy="35" rx="12" ry="8" 
          fill="url(#petalGradient)" 
          transform="rotate(45 60 35)" 
          opacity="1"
          filter="url(#glowEffect)"
        />
        <ellipse cx="60" cy="35" rx="12" ry="8" 
          fill="url(#petalGradient)" 
          transform="rotate(135 60 35)" 
          opacity="1"
          filter="url(#glowEffect)"
        />
        
        {/* Flower center */}
        <circle cx="60" cy="35" r="6" 
          fill="url(#centerGradient)" 
          filter="url(#glowEffect)"
        />
        
        {/* Highlight reflections for realism */}
        <ellipse cx="55" cy="30" rx="8" ry="4" 
          fill="rgba(255,255,255,0.4)" 
          transform="rotate(-20 55 30)"
        />
        <ellipse cx="65" cy="32" rx="6" ry="3" 
          fill="rgba(255,255,255,0.3)" 
          transform="rotate(40 65 32)"
        />
        
        {/* Dewdrops for extra realism */}
        <circle cx="45" cy="25" r="2" 
          fill="rgba(255,255,255,0.6)" 
          filter="url(#glowEffect)"
        />
        <circle cx="75" cy="40" r="1.5" 
          fill="rgba(255,255,255,0.5)" 
          filter="url(#glowEffect)"
        />
      </svg>
    </div>
  );
};

// Memorial Lily - Symbol of Resurrection and Purity
export const MemorialLily = ({ 
  className = "", 
  color = "#ffffff",
  centerColor = "#fbbf24"
}: { 
  className?: string; 
  color?: string;
  centerColor?: string;
}) => (
  <div className={`w-24 h-32 ${className}`}>
    <svg
      viewBox="0 0 120 160"
      className="w-full h-full filter drop-shadow-lg"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <radialGradient id="lilyPetalGradient" cx="0.3" cy="0.2" r="0.8">
          <stop offset="0%" stopColor={color} stopOpacity="1"/>
          <stop offset="60%" stopColor={color} stopOpacity="0.9"/>
          <stop offset="100%" stopColor="#e5e7eb" stopOpacity="0.8"/>
        </radialGradient>
        
        <radialGradient id="lilyCenter" cx="0.5" cy="0.5" r="0.5">
          <stop offset="0%" stopColor={centerColor}/>
          <stop offset="70%" stopColor="#f59e0b"/>
          <stop offset="100%" stopColor="#d97706"/>
        </radialGradient>
        
        <filter id="lilyGlow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      
      {/* Lily stem */}
      <path
        d="M60,140 Q58,120 60,100 Q62,80 60,60"
        stroke="#16a34a"
        strokeWidth="3"
        fill="none"
        strokeLinecap="round"
      />
      
      {/* Lily petals */}
      <path
        d="M60,50 Q40,30 35,60 Q50,45 60,50"
        fill="url(#lilyPetalGradient)"
        filter="url(#lilyGlow)"
      />
      <path
        d="M60,50 Q80,30 85,60 Q70,45 60,50"
        fill="url(#lilyPetalGradient)"
        filter="url(#lilyGlow)"
      />
      <path
        d="M60,50 Q60,20 45,55 Q55,40 60,50"
        fill="url(#lilyPetalGradient)"
        filter="url(#lilyGlow)"
      />
      <path
        d="M60,50 Q60,20 75,55 Q65,40 60,50"
        fill="url(#lilyPetalGradient)"
        filter="url(#lilyGlow)"
      />
      <path
        d="M60,50 Q50,25 50,65 Q58,50 60,50"
        fill="url(#lilyPetalGradient)"
        filter="url(#lilyGlow)"
      />
      <path
        d="M60,50 Q70,25 70,65 Q62,50 60,50"
        fill="url(#lilyPetalGradient)"
        filter="url(#lilyGlow)"
      />
      
      {/* Lily center with stamens */}
      <circle cx="60" cy="50" r="4" fill="url(#lilyCenter)"/>
      <line x1="60" y1="50" x2="58" y2="45" stroke={centerColor} strokeWidth="1"/>
      <line x1="60" y1="50" x2="62" y2="45" stroke={centerColor} strokeWidth="1"/>
      <line x1="60" y1="50" x2="55" y2="47" stroke={centerColor} strokeWidth="1"/>
      <line x1="60" y1="50" x2="65" y2="47" stroke={centerColor} strokeWidth="1"/>
      
      {/* Small circles at stamen tips */}
      <circle cx="58" cy="45" r="1" fill="#92400e"/>
      <circle cx="62" cy="45" r="1" fill="#92400e"/>
      <circle cx="55" cy="47" r="1" fill="#92400e"/>
      <circle cx="65" cy="47" r="1" fill="#92400e"/>
    </svg>
  </div>
);

// Memorial Bouquet - Combination of flowers
export const MemorialBouquet = ({ 
  className = "",
  flowers = ["rose", "lily", "rose"]
}: { 
  className?: string;
  flowers?: ("rose" | "lily")[];
}) => (
  <div className={`relative ${className}`}>
    <div className="flex items-end justify-center space-x-2">
      {flowers.map((flower, index) => (
        <div key={index} className={`transform ${index % 2 === 0 ? 'rotate-12' : '-rotate-12'}`}>
          {flower === "rose" ? (
            <MemorialRose 
              size="medium" 
              color={index === 0 ? "#dc2626" : "#be185d"}
              className={`${index === 1 ? 'scale-110 z-10' : 'scale-90'}`}
            />
          ) : (
            <MemorialLily 
              className={`${index === 1 ? 'scale-110 z-10' : 'scale-90'}`}
            />
          )}
        </div>
      ))}
    </div>
    
    {/* Ribbon */}
    <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
      <svg viewBox="0 0 80 20" className="w-20 h-5">
        <path
          d="M10,10 Q40,5 70,10 Q40,15 10,10"
          fill="#1f2937"
          opacity="0.7"
        />
        <text x="40" y="13" textAnchor="middle" className="text-xs fill-white" fontSize="8">
          ذكرى
        </text>
      </svg>
    </div>
  </div>
);

// Floating Petals Animation
export const FloatingPetals = ({ 
  className = "",
  count = 8
}: { 
  className?: string;
  count?: number;
}) => (
  <div className={`absolute inset-0 pointer-events-none ${className}`}>
    {Array.from({ length: count }).map((_, i) => (
      <div
        key={i}
        className="absolute animate-float opacity-30"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          animationDelay: `${Math.random() * 5}s`,
          animationDuration: `${3 + Math.random() * 4}s`,
        }}
      >
        <svg viewBox="0 0 20 20" className="w-4 h-4">
          <ellipse
            cx="10"
            cy="10"
            rx="8"
            ry="4"
            fill="#dc2626"
            opacity="0.6"
            transform={`rotate(${Math.random() * 360} 10 10)`}
          />
        </svg>
      </div>
    ))}
  </div>
);

// Islamic frame border with memorial flowers
export const IslamicFrameBorder = ({ 
  children, 
  className = "",
  borderColor = "#059669",
  showFlowers = false
}: { 
  children: React.ReactNode; 
  className?: string;
  borderColor?: string;
  showFlowers?: boolean;
}) => (
  <div className={`relative ${className}`}>
    <IslamicCornerDecoration 
      className="top-0 left-0" 
      position="top-left" 
      color={borderColor}
    />
    <IslamicCornerDecoration 
      className="top-0 right-0" 
      position="top-right" 
      color={borderColor}
    />
    <IslamicCornerDecoration 
      className="bottom-0 left-0" 
      position="bottom-left" 
      color={borderColor}
    />
    <IslamicCornerDecoration 
      className="bottom-0 right-0" 
      position="bottom-right" 
      color={borderColor}
    />
    
    {/* Memorial flowers in corners */}
    {showFlowers && (
      <>
        <div className="absolute top-4 left-16">
          <MemorialRose size="small" color="#dc2626" className="opacity-70" />
        </div>
        <div className="absolute top-4 right-16">
          <MemorialLily className="opacity-70 scale-75" />
        </div>
        <div className="absolute bottom-4 left-16">
          <MemorialLily className="opacity-70 scale-75" />
        </div>
        <div className="absolute bottom-4 right-16">
          <MemorialRose size="small" color="#be185d" className="opacity-70" />
        </div>
      </>
    )}
    
    <div className="p-8">
      {children}
    </div>
  </div>
);