import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronUp, BookOpen, Star } from 'lucide-react';
import animationPatterns from '../lib/animationPatterns';

interface ReadingProgressProps {
  target?: string | HTMLElement; // Target element to track or defaults to window
  showPercentage?: boolean;
  showBackToTop?: boolean;
  position?: 'top' | 'bottom' | 'side';
  color?: string;
  thickness?: number;
  showOnScroll?: boolean;
  className?: string;
}

interface ProgressSection {
  id: string;
  title: string;
  element: HTMLElement;
  progress: number;
}

const ReadingProgress: React.FC<ReadingProgressProps> = ({
  target,
  showPercentage = true,
  showBackToTop = true,
  position = 'top',
  color = 'from-emerald-600 to-teal-600',
  thickness = 4,
  showOnScroll = true,
  className = ''
}) => {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(!showOnScroll);
  const [sections, setSections] = useState<ProgressSection[]>([]);
  const [activeSection, setActiveSection] = useState<string>('');

  // Calculate scroll progress
  const calculateProgress = useCallback(() => {
    let element: HTMLElement | Window;
    let scrollTop: number;
    let scrollHeight: number;
    let clientHeight: number;

    if (typeof target === 'string') {
      const targetEl = document.querySelector(target) as HTMLElement;
      if (!targetEl) return 0;
      element = targetEl;
      scrollTop = targetEl.scrollTop;
      scrollHeight = targetEl.scrollHeight;
      clientHeight = targetEl.clientHeight;
    } else if (target instanceof HTMLElement) {
      element = target;
      scrollTop = target.scrollTop;
      scrollHeight = target.scrollHeight;
      clientHeight = target.clientHeight;
    } else {
      element = window;
      scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      scrollHeight = document.documentElement.scrollHeight;
      clientHeight = window.innerHeight;
    }

    const scrollDistance = scrollHeight - clientHeight;
    return scrollDistance > 0 ? (scrollTop / scrollDistance) * 100 : 0;
  }, [target]);

  // Find sections in content
  const findSections = useCallback(() => {
    const headings = document.querySelectorAll('h1, h2, h3[id], section[aria-labelledby], article[id]');
    const sectionList: ProgressSection[] = Array.from(headings)
      .filter(heading => heading.id || heading.getAttribute('aria-labelledby'))
      .map(heading => {
        const id = heading.id || heading.getAttribute('aria-labelledby') || '';
        const title = heading.textContent?.trim() || 'Section';
        return {
          id,
          title: title.length > 30 ? title.substring(0, 30) + '...' : title,
          element: heading as HTMLElement,
          progress: 0
        };
      });
    
    setSections(sectionList);
  }, []);

  // Update active section based on scroll position
  const updateActiveSection = useCallback(() => {
    const scrollY = window.scrollY + 100; // Offset for better UX
    
    for (let i = sections.length - 1; i >= 0; i--) {
      const section = sections[i];
      if (section.element.offsetTop <= scrollY) {
        setActiveSection(section.id);
        break;
      }
    }
  }, [sections]);

  // Handle scroll events
  const handleScroll = useCallback(() => {
    const progress = calculateProgress();
    setScrollProgress(progress);
    
    if (showOnScroll) {
      setIsVisible(progress > 5 && progress < 95);
    }
    
    updateActiveSection();
  }, [calculateProgress, showOnScroll, updateActiveSection]);

  // Scroll to top function
  const scrollToTop = () => {
    if (target && typeof target !== 'string') {
      target.scrollTo({ top: 0, behavior: 'smooth' });
    } else if (typeof target === 'string') {
      const targetEl = document.querySelector(target) as HTMLElement;
      if (targetEl) {
        targetEl.scrollTo({ top: 0, behavior: 'smooth' });
      }
    } else {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Scroll to section function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId) || 
                   document.querySelector(`[aria-labelledby="${sectionId}"]`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  useEffect(() => {
    findSections();
  }, [findSections]);

  useEffect(() => {
    const scrollElement = target && typeof target !== 'string' ? target : window;
    
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll, { passive: true });
      handleScroll(); // Initial call
      
      return () => {
        scrollElement.removeEventListener('scroll', handleScroll);
      };
    }
  }, [target, handleScroll]);

  // Progress bar styles based on position
  const getProgressBarStyles = () => {
    const baseStyles = `fixed z-50 bg-gradient-to-r ${color} transition-all duration-300 ${className}`;
    
    switch (position) {
      case 'top':
        return `${baseStyles} top-0 left-0 right-0`;
      case 'bottom':
        return `${baseStyles} bottom-0 left-0 right-0`;
      case 'side':
        return `${baseStyles} top-0 left-0 bottom-0 w-1`;
      default:
        return `${baseStyles} top-0 left-0 right-0`;
    }
  };

  // Get progress bar dimensions
  const getProgressDimensions = () => {
    switch (position) {
      case 'side':
        return { height: `${scrollProgress}%` };
      default:
        return { 
          width: `${scrollProgress}%`,
          height: `${thickness}px`
        };
    }
  };

  return (
    <>
      {/* Main Progress Bar */}
      <div className={getProgressBarStyles()}>
        <motion.div
          className="bg-gradient-to-r from-emerald-600 to-teal-600 shadow-lg"
          style={getProgressDimensions()}
          initial={{ width: 0 }}
          animate={{ width: position === 'side' ? '100%' : `${scrollProgress}%` }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
        />
      </div>

      {/* Progress Details & Controls */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            {...animationPatterns.fade.fadeInRight}
            className="fixed top-1/2 right-6 transform -translate-y-1/2 z-50 space-y-4"
          >
            {/* Progress Percentage */}
            {showPercentage && (
              <motion.div
                className="bg-white/95 backdrop-blur-xl border border-emerald-200/80 rounded-2xl shadow-xl p-4 text-center min-w-[80px]"
                whileHover={{ scale: 1.05 }}
              >
                <BookOpen className="w-5 h-5 text-emerald-600 mx-auto mb-2" />
                <div className="text-lg font-bold text-slate-800">
                  {Math.round(scrollProgress)}%
                </div>
                <div className="text-xs text-slate-500 mt-1">read</div>
              </motion.div>
            )}

            {/* Section Navigation */}
            {sections.length > 0 && (
              <motion.div
                className="bg-white/95 backdrop-blur-xl border border-emerald-200/80 rounded-2xl shadow-xl p-3 max-w-xs"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="text-xs font-medium text-slate-500 mb-2 flex items-center">
                  <Star className="w-3 h-3 mr-1" />
                  Sections
                </div>
                <div className="space-y-1 max-h-48 overflow-y-auto">
                  {sections.slice(0, 6).map((section, index) => (
                    <button
                      key={section.id}
                      onClick={() => scrollToSection(section.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg text-xs transition-all duration-200 ${
                        activeSection === section.id
                          ? 'bg-emerald-100 text-emerald-800 font-medium'
                          : 'text-slate-600 hover:bg-slate-100 hover:text-slate-800'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          activeSection === section.id ? 'bg-emerald-600' : 'bg-slate-300'
                        }`} />
                        <span className="truncate">{section.title}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Back to Top Button */}
            {showBackToTop && scrollProgress > 20 && (
              <motion.button
                onClick={scrollToTop}
                className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white w-12 h-12 rounded-2xl shadow-xl flex items-center justify-center transition-all duration-300 hover:shadow-2xl"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.3 }}
                aria-label="Back to top"
              >
                <ChevronUp className="w-5 h-5" />
              </motion.button>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ReadingProgress;