/**
 * Animation Patterns Library
 * Consistent animation configurations for the Martyrs Archive application
 */

// Animation duration presets
export const animationDurations = {
  fast: 0.2,
  normal: 0.3,
  slow: 0.5,
  verySlow: 0.8,
  extra: 1.2
} as const;

// Animation easing patterns
export const animationEasings = {
  easeOut: [0.25, 0.46, 0.45, 0.94],
  easeIn: [0.55, 0.06, 0.68, 0.19],
  easeInOut: [0.65, 0, 0.35, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
  gentle: [0.25, 0.1, 0.25, 1],
  sharp: [0.4, 0, 0.6, 1]
} as const;

// Spring animation presets
export const springPresets = {
  gentle: {
    type: "spring" as const,
    stiffness: 120,
    damping: 20,
    mass: 1
  },
  bouncy: {
    type: "spring" as const,
    stiffness: 300,
    damping: 15,
    mass: 0.8
  },
  snappy: {
    type: "spring" as const,
    stiffness: 400,
    damping: 25,
    mass: 0.6
  },
  wobbly: {
    type: "spring" as const,
    stiffness: 180,
    damping: 12,
    mass: 1.2
  }
} as const;

// Fade animations
export const fadeAnimations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: animationDurations.normal, ease: animationEasings.easeOut }
  },
  fadeInUp: {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -30 },
    transition: { duration: animationDurations.normal, ease: animationEasings.easeOut }
  },
  fadeInDown: {
    initial: { opacity: 0, y: -30 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 30 },
    transition: { duration: animationDurations.normal, ease: animationEasings.easeOut }
  },
  fadeInLeft: {
    initial: { opacity: 0, x: -30 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 30 },
    transition: { duration: animationDurations.normal, ease: animationEasings.easeOut }
  },
  fadeInRight: {
    initial: { opacity: 0, x: 30 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -30 },
    transition: { duration: animationDurations.normal, ease: animationEasings.easeOut }
  }
} as const;

// Scale animations
export const scaleAnimations = {
  scaleIn: {
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.8, opacity: 0 },
    transition: springPresets.gentle
  },
  scaleInBounce: {
    initial: { scale: 0.3, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.3, opacity: 0 },
    transition: springPresets.bouncy
  },
  pulse: {
    animate: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 2,
        ease: "easeInOut",
        times: [0, 0.5, 1],
        repeat: Infinity
      }
    }
  }
} as const;

// Slide animations
export const slideAnimations = {
  slideInUp: {
    initial: { y: "100%", opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: "100%", opacity: 0 },
    transition: { duration: animationDurations.slow, ease: animationEasings.easeOut }
  },
  slideInDown: {
    initial: { y: "-100%", opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: "-100%", opacity: 0 },
    transition: { duration: animationDurations.slow, ease: animationEasings.easeOut }
  },
  slideInLeft: {
    initial: { x: "-100%", opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: "-100%", opacity: 0 },
    transition: { duration: animationDurations.slow, ease: animationEasings.easeOut }
  },
  slideInRight: {
    initial: { x: "100%", opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: "100%", opacity: 0 },
    transition: { duration: animationDurations.slow, ease: animationEasings.easeOut }
  }
} as const;

// Rotation animations
export const rotateAnimations = {
  rotateIn: {
    initial: { rotate: -180, opacity: 0 },
    animate: { rotate: 0, opacity: 1 },
    exit: { rotate: 180, opacity: 0 },
    transition: { duration: animationDurations.slow, ease: animationEasings.easeOut }
  },
  spin: {
    animate: {
      rotate: 360,
      transition: {
        duration: 1,
        ease: "linear",
        repeat: Infinity
      }
    }
  }
} as const;

// Stagger animation configurations
export const staggerConfigs = {
  container: {
    animate: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  },
  fast: {
    animate: {
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  },
  slow: {
    animate: {
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }
} as const;

// Page transition animations
export const pageTransitions = {
  fadeTransition: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: animationDurations.normal }
  },
  slideTransition: {
    initial: { x: 300, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: -300, opacity: 0 },
    transition: { duration: animationDurations.slow, ease: animationEasings.easeOut }
  },
  scaleTransition: {
    initial: { scale: 0.9, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 1.1, opacity: 0 },
    transition: springPresets.gentle
  }
} as const;

// Islamic-themed animations
export const islamicAnimations = {
  breathe: {
    animate: {
      scale: [1, 1.02, 1],
      opacity: [0.8, 1, 0.8],
      transition: {
        duration: 3,
        ease: "easeInOut",
        times: [0, 0.5, 1],
        repeat: Infinity
      }
    }
  },
  glow: {
    animate: {
      boxShadow: [
        "0 0 5px rgba(16, 185, 129, 0.3)",
        "0 0 20px rgba(16, 185, 129, 0.6)",
        "0 0 5px rgba(16, 185, 129, 0.3)"
      ],
      transition: {
        duration: 2,
        ease: "easeInOut",
        repeat: Infinity
      }
    }
  },
  float: {
    animate: {
      y: [-5, 5, -5],
      transition: {
        duration: 4,
        ease: "easeInOut",
        repeat: Infinity
      }
    }
  }
} as const;

// Hover animations
export const hoverAnimations = {
  lift: {
    whileHover: {
      y: -8,
      scale: 1.02,
      transition: springPresets.gentle
    }
  },
  scale: {
    whileHover: {
      scale: 1.05,
      transition: springPresets.snappy
    }
  },
  glow: {
    whileHover: {
      boxShadow: "0 10px 30px rgba(0, 0, 0, 0.2)",
      transition: { duration: animationDurations.fast }
    }
  },
  rotate: {
    whileHover: {
      rotate: 5,
      transition: springPresets.gentle
    }
  }
} as const;

// Button animations
export const buttonAnimations = {
  press: {
    whileTap: { scale: 0.95 },
    transition: springPresets.snappy
  },
  bounce: {
    whileTap: { scale: 0.9 },
    whileHover: { scale: 1.05 },
    transition: springPresets.bouncy
  },
  shimmer: {
    background: [
      "linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)"
    ],
    backgroundSize: ["200% 100%"],
    animate: {
      backgroundPosition: ["-200% 0", "200% 0"],
      transition: {
        duration: 1.5,
        ease: "linear",
        repeat: Infinity
      }
    }
  }
} as const;

// Loading animations
export const loadingAnimations = {
  pulse: {
    animate: {
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        ease: "easeInOut",
        repeat: Infinity
      }
    }
  },
  wave: {
    animate: {
      x: ["-100%", "100%"],
      transition: {
        duration: 1.5,
        ease: "linear",
        repeat: Infinity
      }
    }
  },
  dots: {
    animate: {
      scale: [1, 1.2, 1],
      transition: {
        duration: 0.6,
        ease: "easeInOut",
        repeat: Infinity
      }
    }
  }
} as const;

// Utility functions for creating custom animations
export const createStaggerAnimation = (delay: number = 0.1) => ({
  animate: {
    transition: {
      staggerChildren: delay,
      delayChildren: delay
    }
  }
});

export const createFadeAnimation = (
  direction: 'up' | 'down' | 'left' | 'right' | 'none' = 'none',
  distance: number = 30,
  duration: number = animationDurations.normal
) => {
  const getInitialPosition = () => {
    switch (direction) {
      case 'up': return { y: distance };
      case 'down': return { y: -distance };
      case 'left': return { x: distance };
      case 'right': return { x: -distance };
      default: return {};
    }
  };

  return {
    initial: { opacity: 0, ...getInitialPosition() },
    animate: { opacity: 1, x: 0, y: 0 },
    exit: { opacity: 0, ...getInitialPosition() },
    transition: { duration, ease: animationEasings.easeOut }
  };
};

export const createSpringAnimation = (
  stiffness: number = 300,
  damping: number = 20,
  mass: number = 1
) => ({
  type: "spring" as const,
  stiffness,
  damping,
  mass
});

// Pre-built component animation sets
export const componentAnimations = {
  card: {
    ...fadeAnimations.fadeInUp,
    ...hoverAnimations.lift,
    whileTap: { scale: 0.98 }
  },
  modal: {
    ...scaleAnimations.scaleIn,
    backdrop: fadeAnimations.fadeIn
  },
  dropdown: {
    ...slideAnimations.slideInDown,
    transition: springPresets.snappy
  },
  toast: {
    ...slideAnimations.slideInRight,
    exit: { ...slideAnimations.slideInRight.exit, x: "100%" }
  },
  sidebar: {
    ...slideAnimations.slideInLeft,
    transition: { duration: animationDurations.slow, ease: animationEasings.easeOut }
  }
} as const;

export default {
  durations: animationDurations,
  easings: animationEasings,
  springs: springPresets,
  fade: fadeAnimations,
  scale: scaleAnimations,
  slide: slideAnimations,
  rotate: rotateAnimations,
  stagger: staggerConfigs,
  page: pageTransitions,
  islamic: islamicAnimations,
  hover: hoverAnimations,
  button: buttonAnimations,
  loading: loadingAnimations,
  component: componentAnimations,
  utils: {
    createStagger: createStaggerAnimation,
    createFade: createFadeAnimation,
    createSpring: createSpringAnimation
  }
};