import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";
import type { MartyrImage } from "./types";

// Sets the specified image as the profile image for its martyr.
export const setProfileImage = api<{ id: number }, MartyrImage>(
  { auth: true, expose: true, method: "POST", path: "/admin/martyrs/images/:id/profile" },
  async ({ id }) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    const image = await martyrsDB.queryRow<{
      id: number;
      martyr_id: number;
      url: string;
      caption?: string;
      credit?: string;
      is_profile_image: boolean;
      created_at: string;
    }>`
      SELECT * FROM martyr_images WHERE id = ${id}
    `;
    if (!image) {
      throw APIError.notFound("Image not found");
    }

    // Unset existing profile image for this martyr and set the new one.
    await martyrsDB.exec`
      UPDATE martyr_images SET is_profile_image = false WHERE martyr_id = ${image.martyr_id}
    `;
    await martyrsDB.exec`
      UPDATE martyr_images SET is_profile_image = true WHERE id = ${id}
    `;

    const updated = await martyrsDB.queryRow<{
      id: number;
      martyr_id: number;
      url: string;
      caption?: string;
      credit?: string;
      is_profile_image: boolean;
      created_at: string;
    }>`
      SELECT * FROM martyr_images WHERE id = ${id}
    `;
    if (!updated) {
      throw APIError.internal("Failed to set profile image");
    }

    return {
      id: updated.id,
      martyrId: updated.martyr_id,
      url: updated.url,
      caption: updated.caption,
      credit: updated.credit,
      isProfileImage: updated.is_profile_image,
      createdAt: updated.created_at
    };
  }
);
