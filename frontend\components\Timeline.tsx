import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Star } from 'lucide-react';

interface TimelineEvent {
  id: number;
  eventDate: string;
  description: string;
  imageUrl?: string;
}

interface TimelineProps {
  events: TimelineEvent[];
  className?: string;
}

export default function Timeline({ events, className = '' }: TimelineProps) {
  if (events.length === 0) {
    return (
      <div className={`text-center py-16 ${className}`}>
        <div className="w-24 h-24 bg-gradient-to-br from-slate-200 to-slate-300 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <Calendar className="w-12 h-12 text-slate-400" />
        </div>
        <p className="text-slate-500 text-lg">No timeline events available.</p>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {events.map((event, index) => (
        <div key={event.id} className="relative">
          {/* Timeline Line */}
          {index < events.length - 1 && (
            <div className="absolute left-8 top-20 w-0.5 h-full bg-gradient-to-b from-emerald-300 to-teal-300 -z-10" />
          )}
          
          <div className="flex items-start space-x-6">
            {/* Timeline Dot */}
            <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-xl relative">
              <div className="text-center">
                <div className="text-xs leading-none">{new Date(event.eventDate).getFullYear()}</div>
                <Star className="w-3 h-3 mx-auto mt-1" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-amber-400 rounded-full animate-pulse"></div>
            </div>
            
            {/* Event Content */}
            <Card className="flex-1 bg-white/80 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group">
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row md:items-start md:space-x-6">
                  <div className="flex-1">
                    <Badge 
                      variant="outline" 
                      className="mb-4 bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-800 border-emerald-200 px-3 py-1 rounded-lg font-medium"
                    >
                      <Calendar className="w-3 h-3 mr-2" />
                      {formatDate(event.eventDate)}
                    </Badge>
                    <p className="text-slate-700 leading-relaxed text-lg group-hover:text-slate-800 transition-colors duration-300">
                      {event.description}
                    </p>
                  </div>
                  
                  {event.imageUrl && (
                    <div className="mt-4 md:mt-0 md:w-32 md:h-32 flex-shrink-0">
                      <img
                        src={event.imageUrl}
                        alt="Event"
                        className="w-full h-24 md:h-32 object-cover rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300"
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ))}
    </div>
  );
}
