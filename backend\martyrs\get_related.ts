import { api } from "encore.dev/api";
import { martyrsDB } from "./db";
import type { MartyrCard } from "./types";

interface GetRelatedParams {
  slug: string;
}

interface GetRelatedResponse {
  related: MartyrCard[];
}

// Retrieves related martyrs based on shared categories and region.
export const getRelated = api<GetRelatedParams, GetRelatedResponse>(
  { expose: true, method: "GET", path: "/martyrs/:slug/related" },
  async ({ slug }) => {
    const related = await martyrsDB.queryAll<{
      id: number;
      name: string;
      slug: string;
      bio: string;
      sub_categories: string[];
      region?: string;
      profile_image?: string;
      category_match: number;
      region_match: number;
    }>`
      WITH current_martyr AS (
        SELECT sub_categories, region FROM martyrs WHERE slug = ${slug}
      )
      SELECT DISTINCT
        m.id,
        m.name,
        m.slug,
        LEFT(m.bio, 150) as bio,
        m.sub_categories,
        m.region,
        mi.url as profile_image,
        CASE WHEN m.sub_categories && cm.sub_categories THEN 1 ELSE 2 END as category_match,
        CASE WHEN m.region = cm.region THEN 1 ELSE 2 END as region_match
      FROM martyrs m
      LEFT JOIN martyr_images mi ON m.id = mi.martyr_id AND mi.is_profile_image = true
      CROSS JOIN current_martyr cm
      WHERE m.slug != ${slug}
        AND (
          m.sub_categories && cm.sub_categories OR
          m.region = cm.region
        )
      ORDER BY 
        category_match,
        region_match,
        m.name
      LIMIT 5
    `;

    return {
      related: related.map(m => ({
        id: m.id,
        name: m.name,
        slug: m.slug,
        bio: m.bio,
        subCategories: m.sub_categories || [],
        region: m.region,
        profileImage: m.profile_image
      }))
    };
  }
);
