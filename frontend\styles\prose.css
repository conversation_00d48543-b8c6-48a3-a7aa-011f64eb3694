
.prose {
  color: var(--color-slate-700);
  line-height: 1.625;
  font-size: 1.125rem;
}

.prose h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: var(--color-slate-800);
}

.prose h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: var(--color-slate-800);
}

.prose p {
  margin-bottom: 1rem;
}

.prose ul {
  list-style-type: disc;
  list-style-position: inside;
  margin-bottom: 1rem;
  padding-left: 1rem;
}

.prose ol {
  list-style-type: decimal;
  list-style-position: inside;
  margin-bottom: 1rem;
  padding-left: 1rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose blockquote {
  border-left-width: 4px;
  border-color: var(--color-emerald-500);
  padding-left: 1rem;
  font-style: italic;
  color: var(--color-slate-600);
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.prose a {
  color: var(--color-emerald-600);
}

.prose a:hover {
  text-decoration: underline;
}

.prose code {
  background-color: var(--color-slate-100);
  color: var(--color-slate-800);
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.prose pre {
  background-color: var(--color-slate-800);
  color: var(--color-white);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  overflow-x: auto;
}

.prose pre code {
  background-color: transparent;
  color: var(--color-white);
  padding: 0;
}
