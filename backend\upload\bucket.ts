import { Bucket } from "encore.dev/storage/objects";

// Create a public bucket for storing martyr images
// This will automatically work with local development, and when deployed to Encore Cloud
// it will provision the appropriate cloud storage (S3, GCS, etc.)
export const martyrImages = new Bucket("martyr-images", {
  public: true,  // Make images publicly accessible
  versioned: false  // We don't need versioning for this use case
});