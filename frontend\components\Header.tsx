import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Search, Menu, X, Settings, Star, Moon, Clock, User, MapPin, ChevronRight, Home, Heart, BookOpen, Globe, Loader2 } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import backend from '~backend/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import animationPatterns from '../lib/animationPatterns';
import { colors, shadows, borders, animations, focusRing, getButtonClasses, getInputClasses } from '../lib/designTokens';

export default function Header() {
  const location = useLocation();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(-1);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [showMegaMenu, setShowMegaMenu] = useState(false);
  const [megaMenuTimer, setMegaMenuTimer] = useState<number | null>(null);
  const [showRecentSearches, setShowRecentSearches] = useState(false);
  // Generate breadcrumbs based on current location
  const generateBreadcrumbs = useCallback(() => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [{ label: 'Home', path: '/' }];
    
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Decode URI components and format labels
      const decodedSegment = decodeURIComponent(segment);
      let label = decodedSegment;
      
      // Custom labels for known routes
      switch (segment) {
        case 'categories':
          label = 'Categories';
          break;
        case 'timeline':
          label = 'Timeline';
          break;
        case 'about':
          label = 'About';
          break;
        case 'contact':
          label = 'Contact';
          break;
        case 'search':
          label = 'Search';
          break;
        case 'martyrs':
          label = 'Martyrs';
          break;
        case 'bookmarks':
          label = 'Bookmarks';
          break;
        default:
          // Format category names and other segments
          if (decodedSegment.includes('IMN')) {
            label = decodedSegment;
          } else if (decodedSegment.includes(' ')) {
            label = decodedSegment;
          } else {
            // Capitalize first letter and replace hyphens/underscores
            label = decodedSegment.charAt(0).toUpperCase() + 
                   decodedSegment.slice(1).replace(/[-_]/g, ' ');
          }
      }
      
      breadcrumbs.push({ label, path: currentPath });
    });
    
    return breadcrumbs;
  }, [location.pathname]);
  
  const breadcrumbs = generateBreadcrumbs();
  
  // Mega menu categories data
  const megaMenuCategories = [
    {
      name: "IMN Martyrs",
      description: "Martyrs from the Islamic Movement of Nigeria",
      arabicName: "شهداء الحركة الإسلامية في نيجيريا",
      icon: Heart,
      color: "from-emerald-600 to-teal-600",
      link: "/categories/IMN Martyrs",
      count: "500+",
      featured: [
        { name: "Sheikh Ibrahim Zakzaky", link: "/martyrs/sheikh-ibrahim-zakzaky", status: "Leader" },
        { name: "Zaria Massacre 2015", link: "/timeline/zaria-2015", status: "Historical Event" }
      ]
    },
    {
      name: "Marhum",
      description: "Spiritual leaders and clerics",
      arabicName: "المرحوم",
      icon: BookOpen,
      color: "from-blue-600 to-indigo-600",
      link: "/categories/Marhum",
      count: "150+",
      featured: [
        { name: "Imam Hussein", link: "/martyrs/imam-hussein", status: "Sacred Martyr" },
        { name: "Islamic Scholars", link: "/categories/Marhum?type=scholars", status: "Category" }
      ]
    }
  ];
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const recentSearchesRef = useRef<HTMLDivElement>(null);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (e) {
        console.warn('Failed to parse recent searches:', e);
      }
    }
  }, []);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Debounce search query for suggestions
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Handle click outside to close suggestions and recent searches
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
        setSelectedSuggestion(-1);
      }
      if (recentSearchesRef.current && !recentSearchesRef.current.contains(event.target as Node)) {
        setShowRecentSearches(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get search suggestions
  const { data: suggestions } = useQuery({
    queryKey: ['search-suggestions', debouncedQuery],
    queryFn: () => backend.martyrs.search({
      query: debouncedQuery,
      limit: 5
    }),
    enabled: debouncedQuery.length >= 2 && showSuggestions,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const saveRecentSearch = useCallback((query: string) => {
    const updated = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  }, [recentSearches]);

  const handleSearch = (e: React.FormEvent, query?: string) => {
    e.preventDefault();
    const searchTerm = (query || searchQuery).trim();
    if (searchTerm) {
      saveRecentSearch(searchTerm);
      navigate(`/search?q=${encodeURIComponent(searchTerm)}`);
      setSearchQuery('');
      setShowSuggestions(false);
      setSelectedSuggestion(-1);
      setIsMenuOpen(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setShowSuggestions(value.length >= 2);
    setSelectedSuggestion(-1);
  };

  const handleInputFocus = () => {
    if (searchQuery.length >= 2) {
      setShowSuggestions(true);
    }
  };

  // Handle mega menu interactions
  const handleMegaMenuEnter = () => {
    if (megaMenuTimer) {
      clearTimeout(megaMenuTimer);
      setMegaMenuTimer(null);
    }
    setShowMegaMenu(true);
  };
  
  const handleMegaMenuLeave = () => {
    const timer = setTimeout(() => {
      setShowMegaMenu(false);
    }, 200); // Small delay for better UX
    setMegaMenuTimer(timer);
  };
  
  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (megaMenuTimer) {
        clearTimeout(megaMenuTimer);
      }
    };
  }, [megaMenuTimer]);
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    const totalSuggestions = (suggestions?.martyrs?.length || 0) + recentSearches.length;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestion(prev => 
          prev < totalSuggestions - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestion(prev => prev > -1 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestion >= 0) {
          if (selectedSuggestion < (suggestions?.martyrs?.length || 0)) {
            const martyr = suggestions?.martyrs[selectedSuggestion];
            if (martyr) {
              navigate(`/martyrs/${martyr.slug}`);
              setSearchQuery('');
              setShowSuggestions(false);
              setSelectedSuggestion(-1);
            }
          } else {
            const recentIndex = selectedSuggestion - (suggestions?.martyrs?.length || 0);
            const recentSearch = recentSearches[recentIndex];
            if (recentSearch) {
              handleSearch(e, recentSearch);
            }
          }
        } else {
          handleSearch(e);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestion(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const navLinks = [
    { to: '/', label: 'Home' },
    { 
      to: '/categories', 
      label: 'Categories', 
      hasMegaMenu: true,
      onMouseEnter: handleMegaMenuEnter,
      onMouseLeave: handleMegaMenuLeave
    },
    { to: '/timeline', label: 'Timeline' },
    { to: '/about', label: 'About' },
    { to: '/contact', label: 'Contact' },
  ];

  return (
    <header className={`sticky top-0 z-50 transition-all duration-500 ${isScrolled ? 'bg-white/98 backdrop-blur-xl shadow-xl border-b border-emerald-200/50' : 'bg-white/96 backdrop-blur-lg shadow-lg border-b border-emerald-100/40'}`}>
      {/* Main Header Bar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`flex justify-between items-center transition-all duration-500 ease-out ${
          isScrolled ? 'h-16' : 'h-20'
        }`}>
          
          {/* Logo Section - Enhanced with better animations */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            transition={animationPatterns.springs.gentle}
          >
            <Link
              to="/"
              className="flex items-center space-x-3 group shrink-0 transition-all duration-500 ease-out"
              aria-label="Martyrs Archive - Go to homepage"
            >
              <div className="relative">
                {/* Enhanced logo container with improved gradients */}
                <motion.div
                  className={`bg-gradient-to-br from-emerald-500 via-emerald-600 to-teal-700 rounded-xl flex items-center justify-center shadow-lg transition-all duration-500 ease-out ${
                    isScrolled ? 'w-10 h-10' : 'w-12 h-12'
                  }`}
                  whileHover={{
                    scale: 1.05,
                    rotate: 2,
                    boxShadow: "0 20px 40px rgba(16, 185, 129, 0.3)"
                  }}
                  transition={animationPatterns.springs.gentle}
                >
                  {/* Enhanced inner glow effect */}
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/30 via-white/15 to-transparent opacity-80"></div>

                  {/* Logo icons with improved positioning */}
                  <div className="relative z-10 flex items-center justify-center">
                    <div className="relative">
                      <motion.div
                        animate={{ rotate: [0, 5, 0] }}
                        transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                      >
                        <Star className={`text-amber-200 absolute drop-shadow-2xl ${isScrolled ? 'w-3 h-3 -top-0.5 -left-0.5' : 'w-3.5 h-3.5 -top-0.5 -left-0.5'}`} fill="currentColor" />
                      </motion.div>
                      <Moon className={`text-white drop-shadow-2xl ${isScrolled ? 'w-4 h-4' : 'w-5 h-5'}`} fill="currentColor" />
                    </div>
                  </div>

                  {/* Enhanced border with subtle animation */}
                  <motion.div
                    className="absolute inset-0.5 rounded-lg border border-white/30"
                    whileHover={{ borderColor: "rgba(255, 255, 255, 0.5)" }}
                    transition={{ duration: 0.3 }}
                  ></motion.div>
                </motion.div>
              </div>

              {/* Text Section - Enhanced typography */}
              <div className="flex flex-col justify-center min-w-0">
                <motion.div
                  className={`font-bold bg-gradient-to-r from-emerald-800 via-emerald-700 to-teal-700 bg-clip-text text-transparent transition-all duration-500 leading-tight ${
                    isScrolled ? 'text-lg' : 'text-xl'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  transition={animationPatterns.springs.gentle}
                >
                  Martyrs Archive
                </motion.div>
                <div className={`text-emerald-600 font-medium transition-all duration-500 leading-tight whitespace-nowrap ${
                  isScrolled ? 'text-xs' : 'text-sm'
                }`} dir="rtl">
                  أرشيف الشهداء
                </div>
              </div>
            </Link>
          </motion.div>

          {/* Desktop Navigation - Enhanced with better animations and accessibility */}
          <nav className="hidden lg:flex items-center relative" role="navigation" aria-label="Main navigation">
            <div className="flex items-center space-x-1">
              {navLinks.map((link) => (
                <div key={link.to} className="relative">
                  {link.hasMegaMenu ? (
                    <div
                      className="relative"
                      onMouseEnter={link.onMouseEnter}
                      onMouseLeave={link.onMouseLeave}
                    >
                      <motion.button
                        className={`relative px-4 py-2 text-sm font-medium text-slate-700 hover:text-emerald-700 transition-all duration-300 rounded-lg hover:bg-emerald-50/80 group whitespace-nowrap flex items-center ${focusRing.emerald}`}
                        onClick={() => setShowMegaMenu(!showMegaMenu)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        transition={animationPatterns.springs.snappy}
                        aria-expanded={showMegaMenu}
                        aria-haspopup="true"
                        aria-label={`${link.label} menu`}
                      >
                        {link.label}
                        <motion.div
                          animate={{ rotate: showMegaMenu ? 90 : 0 }}
                          transition={animationPatterns.springs.gentle}
                        >
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </motion.div>
                        <motion.div
                          className="absolute bottom-1 left-1/2 transform -translate-x-1/2 h-0.5 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-full"
                          initial={{ width: 0 }}
                          whileHover={{ width: "75%" }}
                          transition={animationPatterns.springs.gentle}
                        />
                      </motion.button>
                    </div>
                  ) : (
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      transition={animationPatterns.springs.snappy}
                    >
                      <Link
                        to={link.to}
                        className={`relative px-4 py-2 text-sm font-medium text-slate-700 hover:text-emerald-700 transition-all duration-300 rounded-lg hover:bg-emerald-50/80 group whitespace-nowrap block ${focusRing.emerald}`}
                      >
                        {link.label}
                        <motion.div
                          className="absolute bottom-1 left-1/2 transform -translate-x-1/2 h-0.5 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-full"
                          initial={{ width: 0 }}
                          whileHover={{ width: "75%" }}
                          transition={animationPatterns.springs.gentle}
                        />
                      </Link>
                    </motion.div>
                  )}
                </div>
              ))}
            </div>
            
            {/* Enhanced Mega Menu Dropdown */}
            <AnimatePresence>
              {showMegaMenu && (
                <motion.div
                  {...animationPatterns.fade.fadeInDown}
                  className="absolute top-full left-1/2 transform -translate-x-1/2 mt-3 w-screen max-w-4xl bg-white/98 backdrop-blur-xl border border-emerald-200/60 rounded-3xl shadow-2xl z-50 overflow-hidden"
                  onMouseEnter={handleMegaMenuEnter}
                  onMouseLeave={handleMegaMenuLeave}
                  role="menu"
                  aria-label="Categories menu"
                >
                  {/* Subtle background pattern */}
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/30 via-transparent to-teal-50/30"></div>

                  <div className="relative p-8">
                    {/* Enhanced Mega Menu Header */}
                    <motion.div
                      className="text-center mb-8"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      <h3 className="text-2xl font-bold text-slate-800 mb-2">Browse Categories</h3>
                      <p className="text-slate-600">Explore Nigerian martyrs from the Islamic Movement of Nigeria (IMN) and their stories</p>
                    </motion.div>

                    {/* Enhanced Categories Grid */}
                    <motion.div
                      className="grid grid-cols-1 md:grid-cols-2 gap-6"
                      variants={animationPatterns.stagger.container}
                      initial="initial"
                      animate="animate"
                    >
                      {megaMenuCategories.map((category, index) => {
                        const IconComponent = category.icon;
                        return (
                          <motion.div
                            key={category.name}
                            variants={animationPatterns.fade.fadeInUp}
                            whileHover={{
                              scale: 1.02,
                              y: -4,
                              boxShadow: "0 20px 40px rgba(0, 0, 0, 0.1)"
                            }}
                            whileTap={{ scale: 0.98 }}
                            transition={animationPatterns.springs.gentle}
                          >
                            <Link
                              to={category.link}
                              className={`group bg-gradient-to-br from-white to-slate-50/50 border border-slate-200/60 rounded-2xl p-6 transition-all duration-300 block ${focusRing.emerald}`}
                              onClick={() => setShowMegaMenu(false)}
                              role="menuitem"
                            >
                              {/* Enhanced Category Header */}
                              <div className="flex items-center mb-4">
                                <motion.div
                                  className={`w-12 h-12 bg-gradient-to-br ${category.color} rounded-xl flex items-center justify-center shadow-lg transition-all duration-300`}
                                  whileHover={{
                                    scale: 1.1,
                                    rotate: 5,
                                    boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)"
                                  }}
                                  transition={animationPatterns.springs.bouncy}
                                >
                                  <IconComponent className="w-6 h-6 text-white" />
                                </motion.div>
                                <div className="ml-4 flex-1">
                                  <h4 className="font-bold text-slate-800 group-hover:text-emerald-700 transition-colors duration-300">
                                    {category.name}
                                  </h4>
                                  <div className="text-xs text-slate-500 font-medium" dir="rtl">
                                    {category.arabicName}
                                  </div>
                                </div>
                                <motion.div
                                  className="bg-emerald-600 text-white px-3 py-1 rounded-full text-xs font-bold"
                                  whileHover={{ scale: 1.1 }}
                                  transition={animationPatterns.springs.snappy}
                                >
                                  {category.count}
                                </motion.div>
                              </div>

                              {/* Enhanced Description */}
                              <p className="text-slate-600 text-sm mb-4 leading-relaxed">
                                {category.description}
                              </p>

                              {/* Enhanced Featured Items */}
                              <div className="space-y-2">
                                {category.featured.map((item, itemIndex) => (
                                  <motion.div
                                    key={itemIndex}
                                    className="flex items-center justify-between text-xs"
                                    whileHover={{ x: 4 }}
                                    transition={animationPatterns.springs.gentle}
                                  >
                                    <span className="text-emerald-700 font-medium">{item.name}</span>
                                    <span className="text-slate-500 bg-slate-100 px-2 py-1 rounded-full">{item.status}</span>
                                  </motion.div>
                                ))}
                              </div>
                            </Link>
                          </motion.div>
                        );
                      })}
                    </motion.div>

                    {/* Enhanced Mega Menu Footer */}
                    <motion.div
                      className="mt-8 pt-6 border-t border-slate-200/60"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      <div className="flex justify-center space-x-6">
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Link
                            to="/search"
                            className={`text-emerald-600 hover:text-emerald-800 font-medium text-sm transition-colors duration-200 ${focusRing.emerald} px-3 py-2 rounded-lg`}
                            onClick={() => setShowMegaMenu(false)}
                          >
                            Search All Martyrs →
                          </Link>
                        </motion.div>
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Link
                            to="/timeline"
                            className={`text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors duration-200 ${focusRing.blue} px-3 py-2 rounded-lg`}
                            onClick={() => setShowMegaMenu(false)}
                          >
                            View Timeline →
                          </Link>
                        </motion.div>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </nav>

          {/* Enhanced Search and Actions */}
          <div className="hidden md:flex items-center space-x-3">
            <form onSubmit={handleSearch} className="flex items-center space-x-2">
              <div className="relative" ref={searchRef}>
                <motion.div
                  whileFocus={{ scale: 1.02 }}
                  transition={animationPatterns.springs.gentle}
                >
                  <Input
                    ref={inputRef}
                    type="text"
                    placeholder="Search martyrs..."
                    value={searchQuery}
                    onChange={handleInputChange}
                    onFocus={handleInputFocus}
                    onKeyDown={handleKeyDown}
                    className={`pl-10 pr-4 border border-emerald-200/80 rounded-xl focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500 bg-white/95 backdrop-blur-sm transition-all duration-500 placeholder:text-slate-400 ${isScrolled ? 'w-48 py-2 text-sm' : 'w-56 py-2.5 text-sm'} ${focusRing.emerald}`}
                    autoComplete="off"
                    aria-label="Search martyrs"
                    aria-describedby="search-help"
                  />
                </motion.div>
                <motion.div
                  animate={debouncedQuery.length >= 2 && showSuggestions ? { rotate: 360 } : { rotate: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  {debouncedQuery.length >= 2 && showSuggestions ? (
                    <Loader2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-emerald-500 w-4 h-4 animate-spin" />
                  ) : (
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-emerald-400 w-4 h-4" />
                  )}
                </motion.div>
                <span id="search-help" className="sr-only">
                  Type at least 2 characters to see suggestions. Use arrow keys to navigate, Enter to select.
                </span>
                
                {/* Enhanced Search Suggestions Dropdown */}
                <AnimatePresence>
                  {showSuggestions && (debouncedQuery.length >= 2 || recentSearches.length > 0) && (
                    <motion.div
                      {...animationPatterns.fade.fadeInDown}
                      className="absolute top-full left-0 right-0 mt-2 bg-white/98 backdrop-blur-xl border border-emerald-200/60 rounded-xl shadow-2xl z-50 max-h-96 overflow-hidden"
                      role="listbox"
                      aria-label="Search suggestions"
                    >
                      {/* Recent Searches */}
                      {debouncedQuery.length < 2 && recentSearches.length > 0 && (
                        <div className="p-3 border-b border-emerald-100/60">
                          <div className="text-xs font-medium text-slate-500 mb-2 flex items-center">
                            <Clock className="w-3 h-3 mr-1" />
                            Recent Searches
                          </div>
                          <div className="space-y-1">
                            {recentSearches.map((search, index) => (
                              <button
                                key={index}
                                onClick={(e) => handleSearch(e, search)}
                                className={`w-full text-left px-3 py-2 rounded-lg hover:bg-emerald-50 transition-colors duration-200 text-sm text-slate-700 ${selectedSuggestion === index ? 'bg-emerald-50' : ''}`}
                              >
                                {search}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {/* Live Search Results */}
                      {suggestions && suggestions.martyrs && suggestions.martyrs.length > 0 && (
                        <div className="p-3">
                          <div className="text-xs font-medium text-slate-500 mb-2 flex items-center">
                            <User className="w-3 h-3 mr-1" />
                            Martyrs ({suggestions.martyrs.length})
                          </div>
                          <div className="space-y-1">
                            {suggestions.martyrs.map((martyr, index) => {
                              const suggestionIndex = debouncedQuery.length >= 2 ? index : recentSearches.length + index;
                              return (
                                <Link
                                  key={martyr.id}
                                  to={`/martyrs/${martyr.slug}`}
                                  onClick={() => {
                                    setShowSuggestions(false);
                                    setSearchQuery('');
                                    setSelectedSuggestion(-1);
                                  }}
                                  className={`block px-3 py-3 rounded-lg hover:bg-emerald-50 transition-colors duration-200 group ${selectedSuggestion === suggestionIndex ? 'bg-emerald-50' : ''}`}
                                >
                                  <div className="flex items-start space-x-3">
                                    <div className="w-8 h-8 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-full flex items-center justify-center flex-shrink-0">
                                      <User className="w-4 h-4 text-emerald-600" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="font-medium text-slate-800 text-sm group-hover:text-emerald-700 transition-colors">
                                        {martyr.name}
                                      </div>
                                      {martyr.region && (
                                        <div className="text-xs text-slate-500 flex items-center mt-1">
                                          <MapPin className="w-3 h-3 mr-1" />
                                          {martyr.region}
                                        </div>
                                      )}
                                      {martyr.subCategories && martyr.subCategories.length > 0 && (
                                        <div className="flex items-center space-x-1 mt-1">
                                          <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-0.5 rounded-full">
                                            {martyr.subCategories[0]}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </Link>
                              );
                            })}
                          </div>
                        </div>
                      )}
                      
                      {/* View All Results */}
                      {debouncedQuery.length >= 2 && (
                        <div className="p-3 border-t border-emerald-100/60">
                          <button
                            onClick={(e) => handleSearch(e)}
                            className="w-full text-left px-3 py-2 rounded-lg hover:bg-emerald-50 transition-colors duration-200 text-sm text-emerald-600 font-medium"
                          >
                            View all results for "{debouncedQuery}"
                          </button>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={animationPatterns.springs.snappy}
              >
                <Button
                  type="submit"
                  size="sm"
                  className={`bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 font-medium px-4 py-2 ${focusRing.white} disabled:opacity-50 disabled:cursor-not-allowed`}
                  disabled={!searchQuery.trim()}
                  aria-label="Submit search"
                >
                  Search
                </Button>
              </motion.div>
            </form>
            
            {/* Quick Recent Searches Access */}
            {recentSearches.length > 0 && (
              <div className="relative" ref={recentSearchesRef}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-600 hover:text-emerald-700 hover:bg-emerald-50/80 rounded-xl transition-all duration-300 font-medium p-2"
                  onClick={() => setShowRecentSearches(!showRecentSearches)}
                  aria-label="Quick access to recent searches"
                >
                  <Clock className="w-4 h-4" />
                </Button>
                
                {/* Recent Searches Quick Dropdown */}
                <AnimatePresence>
                  {showRecentSearches && (
                    <motion.div
                      {...animationPatterns.fade.fadeInDown}
                      className="absolute top-full right-0 mt-2 w-64 bg-white/98 backdrop-blur-xl border border-emerald-200/80 rounded-xl shadow-xl z-50"
                    >
                      <div className="p-3">
                        <div className="text-xs font-medium text-slate-500 mb-2 flex items-center justify-between">
                          <span className="flex items-center">
                            <Clock className="w-3 h-3 mr-1" />
                            Recent Searches
                          </span>
                          <button
                            onClick={() => {
                              setRecentSearches([]);
                              localStorage.removeItem('recentSearches');
                              setShowRecentSearches(false);
                            }}
                            className="text-slate-400 hover:text-slate-600 transition-colors duration-200"
                            aria-label="Clear recent searches"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                        <div className="space-y-1 max-h-48 overflow-y-auto">
                          {recentSearches.map((search, index) => (
                            <button
                              key={index}
                              onClick={(e) => {
                                handleSearch(e, search);
                                setShowRecentSearches(false);
                              }}
                              className="w-full text-left px-3 py-2 rounded-lg hover:bg-emerald-50 transition-colors duration-200 text-sm text-slate-700 flex items-center justify-between group"
                            >
                              <span className="truncate">{search}</span>
                              <Search className="w-3 h-3 text-slate-400 group-hover:text-emerald-600 transition-colors duration-200" />
                            </button>
                          ))}
                        </div>
                        <div className="mt-2 pt-2 border-t border-slate-200">
                          <Link
                            to="/search"
                            className="block text-center text-xs text-emerald-600 hover:text-emerald-800 font-medium transition-colors duration-200"
                            onClick={() => setShowRecentSearches(false)}
                          >
                            Advanced Search →
                          </Link>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
            
            {/* Divider */}
            <div className="w-px h-6 bg-emerald-200/80"></div>
            
            {/* Enhanced Admin Button */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={animationPatterns.springs.snappy}
            >
              <Link to="/admin/login">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`text-slate-600 hover:text-emerald-700 hover:bg-emerald-50/80 rounded-xl transition-all duration-300 font-medium ${focusRing.emerald}`}
                  aria-label="Admin panel access"
                >
                  <motion.div
                    whileHover={{ rotate: 90 }}
                    transition={animationPatterns.springs.gentle}
                  >
                    <Settings className="w-4 h-4 mr-2" />
                  </motion.div>
                  Admin
                </Button>
              </Link>
            </motion.div>
          </div>

          {/* Enhanced Mobile Menu Button */}
          <div className="flex items-center md:hidden">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={animationPatterns.springs.snappy}
            >
              <Button
                variant="ghost"
                size="sm"
                className={`text-slate-700 hover:text-emerald-700 hover:bg-emerald-50/80 rounded-xl p-2 ${focusRing.emerald}`}
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                aria-expanded={isMenuOpen}
                aria-label={isMenuOpen ? "Close menu" : "Open menu"}
              >
                <motion.div
                  animate={{ rotate: isMenuOpen ? 180 : 0 }}
                  transition={animationPatterns.springs.gentle}
                >
                  {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </div>
        
        {/* Enhanced Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              className="md:hidden border-t border-emerald-100/80 bg-white/98 backdrop-blur-xl overflow-hidden"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={animationPatterns.springs.gentle}
            >
              <motion.div
                className="px-4 py-6 space-y-6"
                initial={{ y: -20 }}
                animate={{ y: 0 }}
                exit={{ y: -20 }}
                transition={{ delay: 0.1 }}
              >
              
              {/* Mobile Search */}
              <form onSubmit={handleSearch} className="space-y-3">
                <div className="relative">
                  <Input
                    type="text"
                    placeholder="Search martyrs..."
                    value={searchQuery}
                    onChange={handleInputChange}
                    onFocus={handleInputFocus}
                    onKeyDown={handleKeyDown}
                    className="w-full pl-10 pr-4 py-3 border border-emerald-200/80 rounded-xl focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500 bg-white/95 backdrop-blur-sm placeholder:text-slate-400"
                    autoComplete="off"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-emerald-400 w-5 h-5" />
                  
                  {/* Mobile Suggestions */}
                  <AnimatePresence>
                    {showSuggestions && (debouncedQuery.length >= 2 || recentSearches.length > 0) && (
                      <motion.div
                        {...animationPatterns.fade.fadeInDown}
                        className="absolute top-full left-0 right-0 mt-2 bg-white/98 backdrop-blur-xl border border-emerald-200/80 rounded-xl shadow-xl z-50 max-h-80 overflow-y-auto"
                      >
                        {/* Recent Searches */}
                        {debouncedQuery.length < 2 && recentSearches.length > 0 && (
                          <div className="p-3 border-b border-emerald-100/60">
                            <div className="text-xs font-medium text-slate-500 mb-2 flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              Recent Searches
                            </div>
                            <div className="space-y-1">
                              {recentSearches.map((search, index) => (
                                <button
                                  key={index}
                                  onClick={(e) => handleSearch(e, search)}
                                  className="w-full text-left px-3 py-2 rounded-lg hover:bg-emerald-50 transition-colors duration-200 text-sm text-slate-700"
                                >
                                  {search}
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {/* Live Search Results */}
                        {suggestions && suggestions.martyrs && suggestions.martyrs.length > 0 && (
                          <div className="p-3">
                            <div className="text-xs font-medium text-slate-500 mb-2 flex items-center">
                              <User className="w-3 h-3 mr-1" />
                              Martyrs ({suggestions.martyrs.length})
                            </div>
                            <div className="space-y-1">
                              {suggestions.martyrs.slice(0, 3).map((martyr, index) => (
                                <Link
                                  key={martyr.id}
                                  to={`/martyrs/${martyr.slug}`}
                                  onClick={() => {
                                    setShowSuggestions(false);
                                    setSearchQuery('');
                                    setIsMenuOpen(false);
                                  }}
                                  className="block px-3 py-3 rounded-lg hover:bg-emerald-50 transition-colors duration-200 group"
                                >
                                  <div className="flex items-start space-x-3">
                                    <div className="w-8 h-8 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-full flex items-center justify-center flex-shrink-0">
                                      <User className="w-4 h-4 text-emerald-600" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="font-medium text-slate-800 text-sm group-hover:text-emerald-700 transition-colors">
                                        {martyr.name}
                                      </div>
                                      {martyr.region && (
                                        <div className="text-xs text-slate-500 flex items-center mt-1">
                                          <MapPin className="w-3 h-3 mr-1" />
                                          {martyr.region}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          </div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
                <Button 
                  type="submit" 
                  className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white py-3 rounded-xl font-medium shadow-md hover:shadow-lg transition-all duration-300"
                  disabled={!searchQuery.trim()}
                >
                  Search Archive
                </Button>
              </form>

              {/* Enhanced Mobile Navigation */}
              <nav className="space-y-1" role="navigation" aria-label="Mobile navigation">
                {navLinks.map((link, index) => (
                  <motion.div
                    key={link.to}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 + 0.2 }}
                    whileHover={{ scale: 1.02, x: 4 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Link
                      to={link.to}
                      className={`block px-4 py-3 text-base font-medium text-slate-700 hover:text-emerald-700 hover:bg-emerald-50/80 rounded-xl transition-all duration-300 ${focusRing.emerald}`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {link.label}
                    </Link>
                  </motion.div>
                ))}
              </nav>

              {/* Enhanced Mobile Admin Access */}
              <motion.div
                className="pt-4 border-t border-emerald-100/80"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                <motion.div
                  whileHover={{ scale: 1.02, x: 4 }}
                  whileTap={{ scale: 0.98 }}
                  transition={animationPatterns.springs.gentle}
                >
                  <Link
                    to="/admin/login"
                    className={`flex items-center px-4 py-3 text-base font-medium text-slate-700 hover:text-emerald-700 hover:bg-emerald-50/80 rounded-xl transition-all duration-300 ${focusRing.emerald}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <motion.div
                      whileHover={{ rotate: 90 }}
                      transition={animationPatterns.springs.gentle}
                    >
                      <Settings className="w-5 h-5 mr-3" />
                    </motion.div>
                    Admin Panel
                  </Link>
                </motion.div>
              </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* Enhanced Breadcrumbs Navigation */}
      <AnimatePresence>
        {location.pathname !== '/' && breadcrumbs.length > 1 && (
          <motion.div
            className="bg-emerald-50/40 backdrop-blur-sm border-b border-emerald-100/40"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={animationPatterns.springs.gentle}
          >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <nav className="py-3" aria-label="Breadcrumb navigation">
                <ol className="flex items-center space-x-1 text-sm" role="list">
                  {breadcrumbs.map((crumb, index) => {
                    const isLast = index === breadcrumbs.length - 1;
                    return (
                      <motion.li
                        key={crumb.path}
                        className="flex items-center"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        {index === 0 ? (
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            transition={animationPatterns.springs.snappy}
                          >
                            <Link
                              to={crumb.path}
                              className={`flex items-center text-emerald-600 hover:text-emerald-800 transition-colors duration-200 font-medium px-2 py-1 rounded-md hover:bg-emerald-100/50 ${focusRing.emerald}`}
                              aria-label="Go to home page"
                            >
                              <Home className="w-4 h-4" />
                              <span className="sr-only">{crumb.label}</span>
                            </Link>
                          </motion.div>
                        ) : isLast ? (
                          <motion.span
                            className="text-slate-700 font-medium px-2 py-1 bg-white/60 rounded-md border border-emerald-200/40"
                            aria-current="page"
                            initial={{ scale: 0.9 }}
                            animate={{ scale: 1 }}
                            transition={animationPatterns.springs.gentle}
                          >
                            {crumb.label}
                          </motion.span>
                        ) : (
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            transition={animationPatterns.springs.snappy}
                          >
                            <Link
                              to={crumb.path}
                              className={`text-emerald-600 hover:text-emerald-800 transition-colors duration-200 font-medium px-2 py-1 rounded-md hover:bg-emerald-100/50 ${focusRing.emerald}`}
                            >
                              {crumb.label}
                            </Link>
                          </motion.div>
                        )}

                        {!isLast && (
                          <motion.div
                            animate={{ x: [0, 2, 0] }}
                            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                          >
                            <ChevronRight className="w-4 h-4 text-slate-400 mx-1 flex-shrink-0" aria-hidden="true" />
                          </motion.div>
                        )}
                      </motion.li>
                    );
                  })}
                </ol>
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
