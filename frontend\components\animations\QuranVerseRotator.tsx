import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Pause, SkipForward, SkipBack } from 'lucide-react';
import { QuranVerse } from '../../lib/quranVerses';
import TypewriterText from './TypewriterText';
import LazyAnimation from './LazyAnimation';
import QuranVerseSkeleton from './QuranVerseSkeleton';

interface QuranVerseRotatorProps {
  verses: QuranVerse[];
  interval?: number;
  showTypewriter?: boolean;
  typewriterSpeed?: number;
  className?: string;
  verseClassName?: string;
  sourceClassName?: string;
  containerClassName?: string;
  pauseOnHover?: boolean;
  direction?: 'ltr' | 'rtl';
  showControls?: boolean;
  lazyLoad?: boolean;
  lazyLoadDelay?: number;
}

const QuranVerseRotator: React.FC<QuranVerseRotatorProps> = ({
  verses = [],
  interval = 8000,
  showTypewriter = true,
  typewriterSpeed = 30,
  className = '',
  verseClassName = '',
  sourceClassName = '',
  containerClassName = '',
  pauseOnHover = true,
  direction = 'rtl',
  showControls = false,
  lazyLoad = true,
  lazyLoadDelay = 500
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [isManuallyPaused, setIsManuallyPaused] = useState(false);
  const [isTypingComplete, setIsTypingComplete] = useState(false);

  const nextVerse = useCallback(() => {
    if (!isPaused && !isManuallyPaused && isTypingComplete && verses.length > 0) {
      setCurrentIndex(prev => (prev + 1) % verses.length);
      setIsTypingComplete(false);
    }
  }, [verses.length, isPaused, isManuallyPaused, isTypingComplete]);

  const previousVerse = useCallback(() => {
    if (verses.length > 0) {
      setCurrentIndex(prev => (prev - 1 + verses.length) % verses.length);
      setIsTypingComplete(false);
    }
  }, [verses.length]);

  const goToVerse = useCallback((index: number) => {
    if (verses.length > 0 && index >= 0 && index < verses.length) {
      setCurrentIndex(index);
      setIsTypingComplete(false);
    }
  }, [verses.length]);

  const togglePause = useCallback(() => {
    setIsManuallyPaused(prev => !prev);
  }, []);

  useEffect(() => {
    if (verses.length === 0) return;

    const timer = setInterval(nextVerse, interval);
    return () => clearInterval(timer);
  }, [nextVerse, interval]);

  const handleMouseEnter = () => {
    if (pauseOnHover) {
      setIsPaused(true);
    }
  };

  const handleMouseLeave = () => {
    if (pauseOnHover) {
      setIsPaused(false);
    }
  };

  const handleTypingComplete = () => {
    setIsTypingComplete(true);
  };

  if (verses.length === 0) {
    return null;
  }

  const currentVerse = verses[currentIndex];

  if (!currentVerse) {
    return null;
  }

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    })
  };

  const fadeVariants = {
    enter: {
      opacity: 0,
      y: 20
    },
    center: {
      opacity: 1,
      y: 0
    },
    exit: {
      opacity: 0,
      y: -20
    }
  };

  const verseContent = (
    <div
      className={`${containerClassName} relative overflow-hidden`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <AnimatePresence mode="wait" custom={1}>
        <motion.div
          key={currentIndex}
          custom={1}
          variants={showTypewriter ? fadeVariants : slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            x: { type: "spring", stiffness: 300, damping: 30 },
            opacity: { duration: 0.3 }
          }}
          className={`${className} text-center`}
          dir={direction}
        >
          <div className={`${verseClassName} mb-2`}>
            {showTypewriter ? (
              <TypewriterText
                text={currentVerse.arabicText || ''}
                speed={typewriterSpeed}
                onComplete={handleTypingComplete}
                direction="rtl"
                className="block"
              />
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="text-right"
                dir="rtl"
              >
                {currentVerse.arabicText || ''}
              </motion.div>
            )}
          </div>

          <motion.div
            className={`${sourceClassName} text-sm opacity-80`}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 0.8, y: 0 }}
            transition={{ duration: 0.5, delay: showTypewriter ? 0 : 0.2 }}
          >
            {currentVerse.reference || ''}
          </motion.div>

          {currentVerse.englishTranslation && (
            <motion.div
              className="mt-2 text-sm opacity-70 italic"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 0.7, y: 0 }}
              transition={{ duration: 0.5, delay: showTypewriter ? 0.2 : 0.4 }}
              dir="ltr"
            >
              "{currentVerse.englishTranslation}"
            </motion.div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Control buttons */}
      {showControls && verses.length > 1 && (
        <motion.div
          className="flex justify-center items-center mt-6 space-x-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 1 }}
        >
          <button
            onClick={previousVerse}
            className="w-10 h-10 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-300 focus:ring-4 focus:ring-white/50 focus:outline-none"
            aria-label="Previous verse"
          >
            <SkipBack className="w-4 h-4 text-white" />
          </button>
          
          <button
            onClick={togglePause}
            className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-300 focus:ring-4 focus:ring-white/50 focus:outline-none"
            aria-label={isManuallyPaused ? 'Resume rotation' : 'Pause rotation'}
          >
            {isManuallyPaused ? (
              <Play className="w-5 h-5 text-white ml-0.5" />
            ) : (
              <Pause className="w-5 h-5 text-white" />
            )}
          </button>
          
          <button
            onClick={nextVerse}
            className="w-10 h-10 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-300 focus:ring-4 focus:ring-white/50 focus:outline-none"
            aria-label="Next verse"
          >
            <SkipForward className="w-4 h-4 text-white" />
          </button>
        </motion.div>
      )}

      {/* Progress indicators */}
      {verses.length > 1 && (
        <motion.div
          className="flex justify-center mt-4 space-x-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 1 }}
        >
          {verses.map((_, index) => (
            <button
              key={index}
              onClick={() => goToVerse(index)}
              className={`transition-all duration-300 focus:ring-2 focus:ring-white/50 focus:outline-none rounded-full ${
                index === currentIndex
                  ? 'w-3 h-3 bg-white bg-opacity-80 scale-125'
                  : 'w-2 h-2 bg-white bg-opacity-30 hover:bg-opacity-50'
              }`}
              aria-label={`Go to verse ${index + 1}`}
            />
          ))}
        </motion.div>
      )}
    </div>
  );

  // Return with lazy loading wrapper if enabled
  if (lazyLoad) {
    return (
      <LazyAnimation
        delay={lazyLoadDelay}
        fallback={
          <QuranVerseSkeleton
            className={className}
            verseClassName={verseClassName}
            sourceClassName={sourceClassName}
            direction={direction}
          />
        }
        triggerOnce={true}
        threshold={0.1}
      >
        {verseContent}
      </LazyAnimation>
    );
  }

  return verseContent;
};

export default QuranVerseRotator;