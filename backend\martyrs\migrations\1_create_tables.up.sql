CREATE TABLE martyrs (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  birth_date DATE,
  birth_place TEXT,
  death_date DATE,
  death_place TEXT,
  martyrdom_cause TEXT,
  martyrdom_context TEXT,
  bio TEXT NOT NULL,
  sub_categories TEXT[] DEFAULT '{}',
  region TEXT,
  period TEXT,
  quotes TEXT[] DEFAULT '{}',
  family_info TEXT,
  view_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE martyr_images (
  id BIGSERIAL PRIMARY KEY,
  martyr_id BIGINT REFERENCES martyrs(id) ON DELETE CASCADE,
  url TEXT NOT NULL,
  caption TEXT,
  credit TEXT,
  is_profile_image BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE timeline_events (
  id BIGSERIAL PRIMARY KEY,
  martyr_id BIGINT REFERENCES martyrs(id) ON DELETE CASCADE,
  event_date DATE NOT NULL,
  description TEXT NOT NULL,
  image_url TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_martyrs_slug ON martyrs(slug);
CREATE INDEX idx_martyrs_sub_categories ON martyrs USING GIN(sub_categories);
CREATE INDEX idx_martyrs_region ON martyrs(region);
CREATE INDEX idx_martyrs_period ON martyrs(period);
CREATE INDEX idx_martyrs_name ON martyrs(name);
CREATE INDEX idx_timeline_events_martyr_id ON timeline_events(martyr_id);
CREATE INDEX idx_timeline_events_date ON timeline_events(event_date);
