import { api, APIError } from "encore.dev/api";
import { secret } from "encore.dev/config";
import { createJWT } from "./jwt";
import { logAdminLogin } from "./auditLog";

const adminSecret = secret("AdminSecret");

// Use the same fallback logic as auth handler so tokens match when secrets are undefined
function getAdminToken() {
  const token = adminSecret();
  if (token) return token;
  return (globalThis as any).process?.env?.DEV_ADMIN_SECRET || 'dev-local-admin-secret';
}

// Dev-admin password fallback: read from DEV_ADMIN_PASSWORD for local development
function getAdminPassword(): string {
  const envPw = (globalThis as any).process?.env?.DEV_ADMIN_PASSWORD;
  if (envPw) return envPw;
  (globalThis as any).console?.warn('DEV_ADMIN_PASSWORD not set; using default dev password for local development');
  return 'Admin@2025!';
}

interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  user: {
    id: string;
    email: string;
    role: string;
  };
}

// Admin login endpoint.
export const login = api<LoginRequest, LoginResponse>(
  { expose: true, method: "POST", path: "/admin/login", tags: ["login"] },
  async ({ email, password }) => {
    // Simple hardcoded admin credentials - in production, use proper user management
    // For local development the password can be configured via DEV_ADMIN_PASSWORD env var.
    const expectedPassword = getAdminPassword();
    
    // Debug: log attempt details (masked) to help diagnose mismatches
    try {
      const maskedExpected = expectedPassword ? expectedPassword[0] + '*'.repeat(Math.max(0, expectedPassword.length - 1)) : '(none)';
      const maskedAttempt = password ? password[0] + '*'.repeat(Math.max(0, password.length - 1)) : '(none)';
      const expectedLen = expectedPassword ? expectedPassword.length : 0;
      const attemptLen = password ? password.length : 0;
      (globalThis as any).console?.info(
        `Admin login attempt for email=${email}; expectedPassword=${maskedExpected} (len=${expectedLen}); attemptedPassword=${maskedAttempt} (len=${attemptLen}); envOverride=${Boolean((globalThis as any).process?.env?.DEV_ADMIN_PASSWORD)}`
      );
    } catch (e) {
      (globalThis as any).console?.info('Admin login attempt (logging masked info failed)');
    }
    
    // Normalize passwords (trim) to avoid failing on stray whitespace/newlines from env or client
    const normalizedExpected = expectedPassword ? expectedPassword.trim() : expectedPassword;
    const normalizedAttempt = password ? password.trim() : password;
    
    try {
      const match = normalizedAttempt === normalizedExpected;
      (globalThis as any).console?.info(`Admin login password match after trim: ${match}`);
    } catch (e) {
      /* ignore logging errors */
    }

    if (email !== "<EMAIL>" || normalizedAttempt !== normalizedExpected) {
      throw APIError.unauthenticated("Invalid credentials");
    }

    // Generate JWT tokens
    const { accessToken, refreshToken } = createJWT({
      userId: "admin",
      email: "<EMAIL>",
      role: "admin"
    });

    // Set HttpOnly cookies
    const cookieOptions = "Path=/; HttpOnly; SameSite=Strict";
    const secureCookieOptions = `${cookieOptions}; Secure`;
    
    // In development, we might not be using HTTPS, so we don't set Secure flag
    const isDevelopment = process.env.NODE_ENV === 'development';
    const options = isDevelopment ? cookieOptions : secureCookieOptions;
    
    // Set access token cookie (15 minutes)
    const accessTokenCookie = `admin_access_token=${accessToken}; Max-Age=900; ${options}`;
    
    // Set refresh token cookie (30 days) with more restricted path
    const refreshTokenCookie = `admin_refresh_token=${refreshToken}; Max-Age=2592000; Path=/admin/refresh; ${options}`;

    // Set cookies in response header
    (globalThis as any).setResponseHeader?.("Set-Cookie", [accessTokenCookie, refreshTokenCookie]);

    // Log the successful login
    await logAdminLogin(email);

    return {
      user: {
        id: "admin",
        email: "<EMAIL>",
        role: "admin"
      }
    };
  }
);