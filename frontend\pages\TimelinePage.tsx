import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON>, <PERSON>, Moon, Sc<PERSON>, BookO<PERSON>, Search, Filter } from 'lucide-react';
import { IslamicGeometricPattern, IslamicCalligraphyBorder, MosqueDecoration, IslamicFrameBorder } from '@/components/IslamicPatterns';
import { TypewriterText, QuranVerseRotator } from '../components/animations';
import { heroVerses } from '../lib/quranVerses';
import ReadingProgress from '../components/ReadingProgress';
import { getTimelinePageMeta } from '../lib/seoMeta';
import Timeline1996 from '../components/timeline/Timeline1996';
import Timeline2014 from '../components/timeline/Timeline2014';
import Timeline2015 from '../components/timeline/Timeline2015';
import TimelineNavigation from '../components/timeline/TimelineNavigation';
import { timelineData, getTimelineStats } from '../components/timeline/timelineData';

export default function TimelinePage() {
  const [activeYear, setActiveYear] = useState<number>(2015); // Start with most significant year
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [showNavigation, setShowNavigation] = useState<boolean>(true);

  // Set SEO metadata
  React.useEffect(() => {
    const meta = getTimelinePageMeta();
    document.title = meta.title;

    // Update meta tags
    const updateMetaTag = (name: string, content: string) => {
      let element = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
      if (!element) {
        element = document.createElement('meta');
        element.name = name;
        document.head.appendChild(element);
      }
      element.content = content;
    };

    updateMetaTag('description', meta.description);
    updateMetaTag('keywords', meta.keywords);
  }, []);

  const stats = getTimelineStats();

  const renderTimelineComponent = () => {
    switch (activeYear) {
      case 1996:
        return <Timeline1996 className="mb-12" />;
      case 2014:
        return <Timeline2014 className="mb-12" />;
      case 2015:
        return <Timeline2015 className="mb-12" />;
      default:
        return (
          <div className="text-center py-20">
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl p-8 max-w-2xl mx-auto">
              <Calendar className="w-20 h-20 text-slate-400 mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-slate-800 mb-4">Timeline Under Development</h3>
              <p className="text-slate-600 text-lg leading-relaxed">
                The timeline for {activeYear} is currently being researched and documented.
                Please check back soon for comprehensive historical information.
              </p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden">
      {/* Reading Progress Indicator */}
      <ReadingProgress 
        showPercentage={true}
        showBackToTop={true}
        position="top"
        color="from-emerald-600 to-teal-600"
        thickness={3}
        showOnScroll={true}
      />
      {/* Enhanced Islamic Background Pattern */}
      <IslamicGeometricPattern className="fixed inset-0 z-0" opacity={0.03} color="#059669" />
      
      {/* Floating decorative elements */}
      <div className="fixed top-20 left-10 w-8 h-8 bg-emerald-400/20 rounded-full animate-pulse"></div>
      <div className="fixed top-40 right-20 w-12 h-12 bg-amber-400/15 rounded-full animate-bounce"></div>
      <div className="fixed bottom-20 left-20 w-6 h-6 bg-teal-400/25 rounded-full animate-ping"></div>

      {/* Header Section */}
      <section className="relative bg-gradient-to-br from-emerald-900 via-emerald-800 to-teal-800 text-white py-20 overflow-hidden">
        <IslamicGeometricPattern opacity={0.1} color="#ffffff" />
        
        {/* Floating Islamic elements */}
        <div className="absolute top-10 right-10 opacity-20">
          <MosqueDecoration color="#ffffff" />
        </div>
        <div className="absolute bottom-10 left-10 opacity-15">
          <Star className="w-16 h-16 text-amber-300" fill="currentColor" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center mb-8">
              <div className="relative group">
                <div className="w-24 h-24 bg-gradient-to-br from-emerald-500 via-emerald-600 to-teal-600 rounded-2xl flex items-center justify-center shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:scale-105">
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
                  <div className="relative">
                    <Clock className="w-12 h-12 text-white drop-shadow-xl" />
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-amber-400 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            <TypewriterText
              text="IMN Historical Timeline"
              className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-emerald-100 to-teal-100 bg-clip-text text-transparent leading-tight block"
              speed={80}
              startDelay={500}
            />

            <h2 className="text-2xl md:text-3xl text-emerald-200 font-semibold mb-6" dir="rtl">
              الجدول الزمني للحركة الإسلامية في نيجيريا
            </h2>

            <IslamicCalligraphyBorder className="max-w-md mx-auto mb-6" color="#10b981" />

            <div className="min-h-[120px] mb-8">
              <QuranVerseRotator
                verses={heroVerses.slice(2, 5)} // Different subset for timeline
                interval={12000}
                showTypewriter={true}
                typewriterSpeed={30}
                verseClassName="text-xl text-emerald-200 font-medium tracking-wide leading-relaxed"
                sourceClassName="text-lg text-amber-200 font-medium"
                containerClassName="max-w-4xl mx-auto"
                pauseOnHover={true}
              />
            </div>

            <TypewriterText
              text="Comprehensive historical documentation of the Islamic Movement of Nigeria from 1996 to present, chronicling peaceful beginnings, tragic persecutions, and the ongoing struggle for justice and religious freedom."
              className="text-xl md:text-2xl mb-8 text-emerald-100 max-w-5xl mx-auto leading-relaxed block"
              speed={25}
              startDelay={4000}
              cursor={false}
            />

            {/* Timeline Statistics */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 6 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto mt-8"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                <div className="text-2xl font-bold text-white">{stats.killed}</div>
                <div className="text-sm text-emerald-200">Total Martyrs</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                <div className="text-2xl font-bold text-white">{stats.injured}</div>
                <div className="text-sm text-emerald-200">Total Injured</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                <div className="text-2xl font-bold text-white">{stats.events}</div>
                <div className="text-sm text-emerald-200">Documented Events</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                <div className="text-2xl font-bold text-white">{stats.yearsSpanned}</div>
                <div className="text-sm text-emerald-200">Years Covered</div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Header */}
        <div className="mb-12">
          <IslamicFrameBorder className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-emerald-100" borderColor="#059669">
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Scroll className="w-8 h-8 text-white" />
                </div>
              </div>
              <h2 className="text-3xl font-bold text-slate-800 mb-4">IMN Historical Timeline</h2>
              <p className="text-lg text-slate-600 max-w-4xl mx-auto leading-relaxed">
                Comprehensive documentation of the Islamic Movement of Nigeria's history from peaceful
                beginnings in 1996 through tragic persecutions and ongoing struggles for justice.
                This timeline preserves the memory of martyrs and chronicles the movement's resilience.
              </p>
            </div>
          </IslamicFrameBorder>
        </div>

        {/* Navigation Toggle */}
        <div className="flex justify-center mb-8">
          <button
            onClick={() => setShowNavigation(!showNavigation)}
            className="bg-white/90 backdrop-blur-sm border-2 border-emerald-300 text-emerald-700 hover:bg-emerald-50 hover:border-emerald-400 px-8 py-4 rounded-2xl text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center space-x-3"
          >
            <Calendar className="w-5 h-5" />
            <span>{showNavigation ? 'Hide Timeline Navigation' : 'Show Timeline Navigation'}</span>
          </button>
        </div>

        {/* Timeline Navigation and Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Navigation Sidebar */}
          {showNavigation && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5 }}
              className="lg:col-span-1"
            >
              <div className="sticky top-8">
                <TimelineNavigation
                  activeYear={activeYear}
                  onYearSelect={setActiveYear}
                />
              </div>
            </motion.div>
          )}

          {/* Timeline Content */}
          <div className={showNavigation ? "lg:col-span-3" : "lg:col-span-4"}>
            <motion.div
              key={activeYear}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {renderTimelineComponent()}
            </motion.div>
          </div>
        </div>

        {/* Memorial Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="mt-16 bg-gradient-to-r from-slate-800 to-slate-900 rounded-xl p-8 text-white text-center"
        >
          <BookOpen className="w-12 h-12 text-emerald-400 mx-auto mb-4" />
          <h3 className="text-2xl font-bold mb-2">Preserving Sacred Memory</h3>
          <p className="text-slate-300 mb-4 max-w-3xl mx-auto leading-relaxed">
            This timeline serves as a digital memorial to preserve the history and memory of the Islamic Movement of Nigeria.
            From peaceful beginnings to tragic persecutions, we document these events to honor the martyrs and
            ensure their sacrifices are never forgotten.
          </p>
          <p className="text-lg text-emerald-400 font-arabic" dir="rtl">
            وَلَا تَحْسَبَنَّ الَّذِينَ قُتِلُوا فِي سَبِيلِ اللَّهِ أَمْوَاتًا ۚ بَلْ أَحْيَاءٌ عِندَ رَبِّهِمْ يُرْزَقُونَ
          </p>
          <p className="text-sm text-slate-400 mt-2">
            "And never think of those who have been killed in the cause of Allah as dead. Rather, they are alive with their Lord, receiving provision" - Quran 3:169
          </p>
        </motion.div>
      </div>
    </div>
  );
}
