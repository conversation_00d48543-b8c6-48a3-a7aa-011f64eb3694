import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, MapPin, Users, BookOpen, ChevronDown, ChevronUp, ExternalLink, Star } from 'lucide-react';
import { getEventsByYear, getTotalCasualtiesByYear } from './timelineData';
import { IslamicGeometricPattern, IslamicCalligraphyBorder } from '../IslamicPatterns';

interface Timeline1996Props {
  className?: string;
}

export default function Timeline1996({ className = '' }: Timeline1996Props) {
  const [expandedEvent, setExpandedEvent] = useState<string | null>(null);
  const events = getEventsByYear(1996);
  const yearStats = getTotalCasualtiesByYear(1996);

  const toggleEventExpansion = (eventId: string) => {
    setExpandedEvent(expandedEvent === eventId ? null : eventId);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <IslamicGeometricPattern />
      </div>

      <div className="relative">
        {/* Year Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-emerald-600 to-teal-700 rounded-full mb-4 shadow-lg">
            <span className="text-3xl font-bold text-white">1996</span>
          </div>
          <h2 className="text-3xl font-bold text-slate-800 mb-2">
            Foundation Years
          </h2>
          <h3 className="text-xl text-emerald-700 font-semibold mb-3" dir="rtl">
            سنوات التأسيس
          </h3>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
            The early period of Islamic Movement establishment in Nigeria, marked by peaceful 
            organizational activities and the formal structuring of Shia Islamic education and community building.
          </p>
        </motion.div>

        {/* Peaceful Period Indicator */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-gradient-to-r from-emerald-100 to-teal-100 border-l-4 border-emerald-600 rounded-lg p-4 mb-6"
        >
          <div className="flex items-center space-x-3">
            <Star className="w-6 h-6 text-emerald-600 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-emerald-800">Peaceful Foundation Period</h4>
              <p className="text-emerald-700 text-sm">
                This period represents the peaceful establishment and organization of the Islamic Movement in Nigeria, 
                focusing on education, community building, and religious activities.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Year Statistics */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-6 mb-8 border border-emerald-200"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center mb-2">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <span className="text-3xl font-bold text-emerald-800">{events.length}</span>
              <span className="text-sm text-emerald-600">Foundation Events</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-white" />
              </div>
              <span className="text-3xl font-bold text-teal-800">0</span>
              <span className="text-sm text-teal-600">Casualties</span>
              <span className="text-xs text-teal-500 mt-1">Peaceful period</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-slate-600 rounded-full flex items-center justify-center mb-2">
                <Star className="w-6 h-6 text-white" />
              </div>
              <span className="text-3xl font-bold text-slate-800">1</span>
              <span className="text-sm text-slate-600">Movement Established</span>
            </div>
          </div>
        </motion.div>

        {/* Timeline Events */}
        <div className="space-y-6">
          {events.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              className="relative"
            >
              {/* Timeline Line */}
              <div className="absolute left-6 top-16 bottom-0 w-0.5 bg-gradient-to-b from-emerald-400 to-teal-600"></div>
              
              {/* Event Card */}
              <div className="relative bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden">
                <IslamicCalligraphyBorder />
                
                {/* Peaceful Indicator */}
                <div className="absolute top-0 right-0 w-2 h-full bg-gradient-to-b from-emerald-500 to-teal-600"></div>
                
                {/* Event Header */}
                <div className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* Date Circle */}
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-emerald-600 to-teal-700 rounded-full flex items-center justify-center shadow-lg">
                      <Calendar className="w-6 h-6 text-white" />
                    </div>
                    
                    {/* Event Info */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-xl font-bold text-slate-800">{event.title}</h3>
                        <button
                          onClick={() => toggleEventExpansion(event.id)}
                          className="p-2 hover:bg-slate-100 rounded-lg transition-colors duration-200"
                          aria-label={expandedEvent === event.id ? "Collapse details" : "Expand details"}
                        >
                          {expandedEvent === event.id ? (
                            <ChevronUp className="w-5 h-5 text-slate-600" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-slate-600" />
                          )}
                        </button>
                      </div>
                      
                      {event.arabicTitle && (
                        <p className="text-lg text-emerald-700 font-arabic mb-2" dir="rtl">
                          {event.arabicTitle}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4 text-sm text-slate-600 mb-3">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(event.date).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{event.location}</span>
                        </div>
                      </div>
                      
                      {/* Peaceful Status */}
                      <div className="flex items-center space-x-2 text-sm mb-3">
                        <div className="flex items-center space-x-1 text-emerald-600">
                          <Star className="w-4 h-4" />
                          <span className="font-semibold">Peaceful Establishment</span>
                        </div>
                        <div className="flex items-center space-x-1 text-teal-600">
                          <BookOpen className="w-4 h-4" />
                          <span className="font-semibold">Educational Focus</span>
                        </div>
                      </div>
                      
                      <p className="text-slate-700 leading-relaxed">
                        {event.description}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                <AnimatePresence>
                  {expandedEvent === event.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-slate-200 bg-slate-50"
                    >
                      <div className="p-6 space-y-4">
                        {/* Context */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Historical Context</h4>
                          <p className="text-slate-700 leading-relaxed">{event.context}</p>
                        </div>
                        
                        {/* Significance */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Historical Significance</h4>
                          <p className="text-slate-700 leading-relaxed">{event.significance}</p>
                        </div>
                        
                        {/* Sources */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Sources</h4>
                          <ul className="space-y-1">
                            {event.sources.map((source, idx) => (
                              <li key={idx} className="flex items-center space-x-2 text-sm text-slate-600">
                                <ExternalLink className="w-3 h-3 flex-shrink-0" />
                                <span>{source}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Foundation Legacy Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mt-12 bg-gradient-to-r from-emerald-800 to-teal-900 rounded-xl p-8 text-white text-center"
        >
          <BookOpen className="w-12 h-12 text-emerald-400 mx-auto mb-4" />
          <h3 className="text-2xl font-bold mb-2">Foundation of Faith</h3>
          <p className="text-slate-300 mb-4">
            The year 1996 marked the formal establishment of the Islamic Movement in Nigeria, 
            laying the groundwork for decades of Islamic education, community building, and spiritual guidance 
            that would touch the lives of countless Nigerian Muslims.
          </p>
          <p className="text-lg text-emerald-400 font-arabic" dir="rtl">
            وَمَنْ أَحْيَاهَا فَكَأَنَّمَا أَحْيَا النَّاسَ جَمِيعًا
          </p>
          <p className="text-sm text-slate-400 mt-2">
            "And whoever saves a life, it is as if he has saved all of mankind" - Quran 5:32
          </p>
        </motion.div>
      </div>
    </div>
  );
}
