import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import animationPatterns from '../lib/animationPatterns';

interface MartyrCardSkeletonProps {
  className?: string;
  showImage?: boolean;
  compact?: boolean;
}

const MartyrCardSkeleton: React.FC<MartyrCardSkeletonProps> = ({
  className = '',
  showImage = true,
  compact = false
}) => {
  return (
    <motion.article 
      className={`group ${className}`}
      {...animationPatterns.fade.fadeIn}
      animate={{
        opacity: [0.5, 1, 0.5],
        transition: {
          duration: 1.5,
          ease: "easeInOut",
          repeat: Infinity
        }
      }}
    >
      <Card className="bg-white/90 backdrop-blur-sm border border-slate-200 rounded-2xl shadow-lg overflow-hidden">
        {/* Header Skeleton */}
        <div className="bg-gradient-to-r from-emerald-100 to-teal-100 p-3 relative">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-emerald-200 rounded-full animate-pulse"></div>
              <div className="h-3 bg-emerald-200 rounded w-16 animate-pulse"></div>
            </div>
            <div className="h-3 bg-emerald-200 rounded w-20 animate-pulse"></div>
          </div>
        </div>

        <CardContent className="p-0">
          {/* Image Skeleton */}
          {showImage && (
            <div className={`relative ${compact ? 'h-40' : 'h-48 md:h-56'} bg-gradient-to-br from-slate-100 via-emerald-50 to-teal-50 overflow-hidden`}>
              <motion.div 
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
                animate={{
                  x: ['-100%', '100%']
                }}
                transition={{
                  duration: 2,
                  ease: 'linear',
                  repeat: Infinity
                }}
              />
              
              {/* Placeholder icon */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-16 h-16 bg-gradient-to-br from-slate-200 to-emerald-200 rounded-full flex items-center justify-center animate-pulse">
                  <div className="w-8 h-8 bg-slate-300 rounded opacity-60"></div>
                </div>
              </div>
            </div>
          )}

          {/* Content Skeleton */}
          <div className="p-5 space-y-4">
            {/* Name and Badge */}
            <div className="space-y-3">
              <div className="space-y-2">
                <div className="h-5 md:h-6 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded-lg w-4/5 animate-pulse"></div>
                {!compact && (
                  <div className="h-5 md:h-6 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded-lg w-3/5 animate-pulse"></div>
                )}
              </div>
              <div className="w-24 h-6 bg-gradient-to-r from-emerald-100 via-emerald-50 to-emerald-100 rounded-full animate-pulse"></div>
            </div>
            
            {/* Bio Lines */}
            {!compact && (
              <div className="space-y-2">
                <div className="h-4 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded w-full animate-pulse"></div>
                <div className="h-4 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded w-3/4 animate-pulse"></div>
                <div className="h-4 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded w-5/6 animate-pulse"></div>
              </div>
            )}
            
            {/* Location */}
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-emerald-200 rounded-full animate-pulse"></div>
              <div className="h-3 bg-slate-200 rounded w-24 animate-pulse"></div>
            </div>
            
            {/* Tags */}
            <div className="flex space-x-2">
              <div className="w-16 h-5 bg-emerald-100 rounded animate-pulse"></div>
              <div className="w-20 h-5 bg-emerald-100 rounded animate-pulse"></div>
              {!compact && (
                <div className="w-12 h-5 bg-emerald-100 rounded animate-pulse"></div>
              )}
            </div>
            
            {/* Footer */}
            <div className="border-t border-slate-100 pt-4">
              <div className="flex items-center justify-between">
                <div className="h-4 bg-emerald-100 rounded w-28 animate-pulse"></div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-slate-200 rounded-full animate-pulse"></div>
                  <div className="flex items-center space-x-1">
                    <div className="w-4 h-4 bg-emerald-200 rounded animate-pulse"></div>
                    <div className="h-4 bg-slate-200 rounded w-20 animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.article>
  );
};

export default MartyrCardSkeleton;