import { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Calendar, Star, Heart, Users, ImageIcon, Eye, BookOpen, Clock, RefreshCw, AlertCircle } from 'lucide-react';
import { getCardClasses, colors, iconSizes, animations } from '../lib/designTokens';
import SocialShare from './SocialShare';
import { generateMartyrShareData } from '../lib/shareUtils';
import animationPatterns from '../lib/animationPatterns';

interface MartyrCardProps {
  id: number;
  name: string;
  slug: string;
  bio: string;
  subCategories: string[];
  region?: string;
  profileImage?: string;
  deathDate?: string;
  birthDate?: string;
}

export default function MartyrCard({ 
  id,
  name, 
  slug, 
  bio, 
  subCategories, 
  region, 
  profileImage, 
  deathDate,
  birthDate 
}: MartyrCardProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [retrying, setRetrying] = useState(false);
  
  const MAX_RETRIES = 2;
  
  // Generate share data
  const shareData = generateMartyrShareData({
    name,
    slug,
    bio,
    region,
    profileImage
  });
  
  const formatYear = (dateString?: string) => {
    if (!dateString) return null;
    return new Date(dateString).getFullYear();
  };

  const birthYear = formatYear(birthDate);
  const deathYear = formatYear(deathDate);

  const handleImageError = () => {
    if (retryCount < MAX_RETRIES && !retrying) {
      setRetrying(true);
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        setImageError(false);
        setImageLoading(true);
        setRetrying(false);
      }, 1000 * (retryCount + 1)); // Exponential backoff
    } else {
      setImageError(true);
      setImageLoading(false);
      setRetrying(false);
    }
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
    setRetryCount(0);
  };
  
  const handleRetryImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setImageError(false);
    setImageLoading(true);
    setRetryCount(0);
    setRetrying(false);
  };

  return (
    <motion.article 
      className="group relative"
      {...animationPatterns.component.card}
      layout
      onMouseEnter={() => setShowPreview(true)}
      onMouseLeave={() => setShowPreview(false)}
    >
      <Link 
        to={`/martyrs/${slug}`} 
        className={`block ${animations.transition.all} focus:ring-4 focus:ring-emerald-500/50 focus:outline-none rounded-2xl`}
        aria-label={`Read about ${name}, a martyr who sacrificed for justice and faith`}
      >
        <Card className={getCardClasses('martyr', true)}>
        {/* Simplified Header with Essential Info */}
        <motion.div 
          className="bg-gradient-to-r from-emerald-700/90 to-teal-700/90 p-3 relative"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={animationPatterns.utils.createSpring(200, 25, 0.8)}
        >
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-amber-400/90 rounded-full flex items-center justify-center">
                <Star className={`${iconSizes.xs} text-amber-900`} fill="currentColor" aria-hidden="true" />
              </div>
              <span className="text-xs font-semibold tracking-wider" aria-label="Martyr status">SHAHID</span>
            </div>
            {(birthYear || deathYear) && (
              <time className="text-xs font-medium opacity-90">
                {birthYear && deathYear ? `${birthYear} - ${deathYear}` : deathYear ? `† ${deathYear}` : birthYear}
              </time>
            )}
          </div>
        </motion.div>

        <CardContent className="p-0">
          {/* Enhanced Image Section with Better Error Handling */}
          <div className="relative h-48 md:h-56 overflow-hidden bg-gradient-to-br from-slate-100 to-emerald-50">
            {profileImage && !imageError ? (
              <>
                {/* Loading State */}
                {imageLoading && (
                  <div className="absolute inset-0 bg-gradient-to-br from-slate-200 to-emerald-100 animate-pulse flex items-center justify-center" aria-hidden="true">
                    <div className="text-center">
                      <div className="w-8 h-8 border-2 border-emerald-600 border-t-transparent rounded-full animate-spin mb-2"></div>
                      <p className="text-xs text-slate-500">
                        {retrying ? `Retrying... (${retryCount + 1}/${MAX_RETRIES + 1})` : 'Loading image...'}
                      </p>
                    </div>
                  </div>
                )}
                
                {/* Main Image */}
                <img
                  key={`${profileImage}-${retryCount}`} // Force re-render on retry
                  src={profileImage}
                  alt={`Portrait of ${name}`}
                  className={`w-full h-full object-cover transition-transform duration-500 group-hover:scale-105 ${
                    imageLoading ? 'opacity-0' : 'opacity-100'
                  }`}
                  onError={handleImageError}
                  onLoad={handleImageLoad}
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" aria-hidden="true" />
                
                {/* Hover Preview Overlay */}
                <AnimatePresence>
                  {showPreview && (
                    <motion.div
                      className="absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="text-center text-white max-w-full">
                        {/* Quick Preview Icon */}
                        <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                          <Eye className="w-6 h-6 text-white" />
                        </div>
                        
                        {/* Preview Content */}
                        <h4 className="font-bold text-lg mb-2 line-clamp-2">{name}</h4>
                        
                        {/* Quick Stats */}
                        <div className="space-y-2 text-sm">
                          {region && (
                            <div className="flex items-center justify-center text-emerald-200">
                              <MapPin className="w-4 h-4 mr-1" />
                              <span>{region}</span>
                            </div>
                          )}
                          
                          {(birthYear || deathYear) && (
                            <div className="flex items-center justify-center text-amber-200">
                              <Calendar className="w-4 h-4 mr-1" />
                              <span>
                                {birthYear && deathYear ? `${birthYear} - ${deathYear}` : deathYear ? `† ${deathYear}` : birthYear}
                              </span>
                            </div>
                          )}
                          
                          {subCategories.length > 0 && (
                            <div className="flex items-center justify-center text-blue-200">
                              <Users className="w-4 h-4 mr-1" />
                              <span>{subCategories.length} Categories</span>
                            </div>
                          )}
                        </div>
                        
                        {/* Bio Preview */}
                        <p className="text-slate-200 text-xs mt-3 line-clamp-3 leading-relaxed">
                          {bio}
                        </p>
                        
                        {/* Call to Action */}
                        <div className="mt-4 flex items-center justify-center text-emerald-300 font-medium text-sm">
                          <BookOpen className="w-4 h-4 mr-1" />
                          <span>Click to Read Full Story</span>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </>
            ) : (
              /* Enhanced Error State and Fallback */
              <div className="h-full bg-gradient-to-br from-emerald-100 via-teal-50 to-slate-50 flex items-center justify-center relative">
                <div className="text-center">
                  {/* Error State */}
                  {profileImage && imageError ? (
                    <>
                      <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 rounded-full flex items-center justify-center mb-3 mx-auto shadow-lg border-2 border-red-200">
                        <AlertCircle className="w-8 h-8 text-red-600" aria-hidden="true" />
                      </div>
                      <p className="text-red-600 text-xs mb-3 font-medium">Image failed to load</p>
                      
                      {/* Retry Button */}
                      {retryCount < MAX_RETRIES && (
                        <button
                          onClick={handleRetryImage}
                          className="inline-flex items-center px-3 py-1.5 bg-emerald-600 hover:bg-emerald-700 text-white text-xs rounded-lg transition-colors duration-200 mb-3"
                          disabled={retrying}
                        >
                          <RefreshCw className={`w-3 h-3 mr-1 ${retrying ? 'animate-spin' : ''}`} />
                          {retrying ? 'Retrying...' : 'Retry'}
                        </button>
                      )}
                      
                      {/* Fallback Content */}
                      <div className="mt-2">
                        <span className="inline-block w-12 h-12 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full text-white text-xl font-bold flex items-center justify-center">
                          {name.charAt(0)}
                        </span>
                      </div>
                    </>
                  ) : (
                    /* No Image State */
                    <>
                      <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full flex items-center justify-center mb-3 mx-auto shadow-lg">
                        <span className="text-xl font-bold text-white" aria-hidden="true">
                          {name.charAt(0)}
                        </span>
                      </div>
                      <div className="flex justify-center items-center space-x-2">
                        <Star className={`${iconSizes.sm} text-amber-500`} fill="currentColor" aria-hidden="true" />
                        <Heart className={`${iconSizes.sm} text-red-500`} fill="currentColor" aria-hidden="true" />
                      </div>
                    </>
                  )}
                </div>
                
                {/* Hover Preview for No Image/Error */}
                <AnimatePresence>
                  {showPreview && (
                    <motion.div
                      className="absolute inset-0 bg-emerald-600/90 backdrop-blur-sm flex items-center justify-center p-4"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="text-center text-white max-w-full">
                        <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Eye className="w-6 h-6 text-white" />
                        </div>
                        <h4 className="font-bold text-lg mb-2 line-clamp-2">{name}</h4>
                        
                        {/* Show error status in preview if applicable */}
                        {profileImage && imageError && (
                          <p className="text-red-200 text-xs mb-2 flex items-center justify-center">
                            <AlertCircle className="w-3 h-3 mr-1" />
                            Image unavailable
                          </p>
                        )}
                        
                        <p className="text-emerald-100 text-xs line-clamp-2">{bio}</p>
                        <div className="mt-3 flex items-center justify-center text-white font-medium text-sm">
                          <BookOpen className="w-4 h-4 mr-1" />
                          <span>Read More</span>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>

          {/* Simplified Content Section */}
          <motion.div 
            className="p-5"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: animationPatterns.durations.normal }}
          >
            {/* Name and Primary Category */}
            <header className="mb-4">
              <h3 className="text-lg md:text-xl font-bold text-slate-800 mb-2 line-clamp-2 group-hover:text-emerald-700 transition-colors duration-300">
                {name}
              </h3>
              
              {subCategories[0] && (
                <Badge className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white border-0 text-xs font-semibold">
                  <Users className={`${iconSizes.xs} mr-1`} aria-hidden="true" />
                  {subCategories[0]}
                </Badge>
              )}
            </header>

            {/* Bio Preview */}
            <p className="text-slate-600 text-sm leading-relaxed mb-4 line-clamp-2">
              {bio}
            </p>

            {/* Location and Additional Info */}
            <div className="space-y-2 mb-4">
              {region && (
                <div className="flex items-center text-xs text-slate-500">
                  <MapPin className={`${iconSizes.xs} mr-2 text-emerald-500`} aria-hidden="true" />
                  <span className="font-medium">{region}</span>
                </div>
              )}
              
              {subCategories.length > 1 && (
                <div className="flex flex-wrap gap-1">
                  {subCategories.slice(1, 3).map((category) => (
                    <Badge 
                      key={category} 
                      variant="secondary" 
                      className="text-xs bg-emerald-50 text-emerald-600 border-0 font-medium"
                    >
                      {category}
                    </Badge>
                  ))}
                  {subCategories.length > 3 && (
                    <Badge 
                      variant="outline" 
                      className="text-xs border-emerald-200 text-emerald-600"
                    >
                      +{subCategories.length - 3}
                    </Badge>
                  )}
                </div>
              )}
            </div>

            {/* Enhanced Footer with Social Sharing */}
            <footer className="border-t border-slate-100 pt-3">
              <div className="flex items-center justify-between">
                <span className="text-emerald-700 text-sm font-semibold group-hover:text-emerald-800 transition-colors duration-300">
                  Honor Their Memory
                </span>
                <div className="flex items-center space-x-3">
                  
                  <SocialShare
                    url={shareData.url}
                    title={shareData.title}
                    description={shareData.description}
                    size="sm"
                    variant="dropdown"
                  />
                  <div className="flex items-center text-emerald-600 group-hover:text-emerald-700 transition-colors duration-300">
                    <Heart className={`${iconSizes.sm} mr-1 group-hover:scale-110 transition-transform duration-300`} fill="currentColor" aria-hidden="true" />
                    <span className="text-sm font-medium">Read Story</span>
                  </div>
                </div>
              </div>
            </footer>
          </motion.div>
        </CardContent>
      </Card>
    </Link>
  </motion.article>
  );
}
