/**
 * SEO Meta Utilities for Open Graph, Twitter Cards, and General Meta Tags
 * Provides utilities for generating and managing meta tags for better social media sharing and SEO
 */

export interface SEOMetaData {
  title: string;
  description: string;
  url?: string;
  image?: string;
  imageAlt?: string;
  type?: 'website' | 'article' | 'profile';
  siteName?: string;
  locale?: string;
  alternateLocale?: string[];
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  keywords?: string[];
}

export interface TwitterCardData {
  card: 'summary' | 'summary_large_image' | 'app' | 'player';
  site?: string;
  creator?: string;
  title: string;
  description: string;
  image?: string;
  imageAlt?: string;
}

/**
 * Generate Open Graph meta tags
 */
export function generateOpenGraphTags(data: SEOMetaData): Array<{property: string; content: string}> {
  const tags: Array<{property: string; content: string}> = [
    { property: 'og:type', content: data.type || 'website' },
    { property: 'og:title', content: data.title },
    { property: 'og:description', content: data.description },
    { property: 'og:site_name', content: data.siteName || 'Martyrs Archive' },
    { property: 'og:locale', content: data.locale || 'en_US' }
  ];

  // Add URL if provided
  if (data.url) {
    tags.push({ property: 'og:url', content: data.url });
  }

  // Add image if provided
  if (data.image) {
    tags.push(
      { property: 'og:image', content: data.image },
      { property: 'og:image:alt', content: data.imageAlt || data.title },
      { property: 'og:image:width', content: '1200' },
      { property: 'og:image:height', content: '630' },
      { property: 'og:image:type', content: 'image/jpeg' }
    );
  }

  // Add alternate locales
  if (data.alternateLocale) {
    data.alternateLocale.forEach(locale => {
      tags.push({ property: 'og:locale:alternate', content: locale });
    });
  }

  // Add article-specific tags
  if (data.type === 'article') {
    if (data.author) {
      tags.push({ property: 'article:author', content: data.author });
    }
    if (data.publishedTime) {
      tags.push({ property: 'article:published_time', content: data.publishedTime });
    }
    if (data.modifiedTime) {
      tags.push({ property: 'article:modified_time', content: data.modifiedTime });
    }
    if (data.section) {
      tags.push({ property: 'article:section', content: data.section });
    }
    if (data.tags) {
      data.tags.forEach(tag => {
        tags.push({ property: 'article:tag', content: tag });
      });
    }
  }

  return tags;
}

/**
 * Generate Twitter Card meta tags
 */
export function generateTwitterCardTags(data: TwitterCardData): Array<{name: string; content: string}> {
  const tags: Array<{name: string; content: string}> = [
    { name: 'twitter:card', content: data.card },
    { name: 'twitter:title', content: data.title },
    { name: 'twitter:description', content: data.description }
  ];

  if (data.site) {
    tags.push({ name: 'twitter:site', content: data.site });
  }

  if (data.creator) {
    tags.push({ name: 'twitter:creator', content: data.creator });
  }

  if (data.image) {
    tags.push(
      { name: 'twitter:image', content: data.image },
      { name: 'twitter:image:alt', content: data.imageAlt || data.title }
    );
  }

  return tags;
}

/**
 * Generate general meta tags
 */
export function generateGeneralMetaTags(data: SEOMetaData): Array<{name: string; content: string}> {
  const tags: Array<{name: string; content: string}> = [
    { name: 'description', content: data.description }
  ];

  if (data.keywords && data.keywords.length > 0) {
    tags.push({ name: 'keywords', content: data.keywords.join(', ') });
  }

  if (data.author) {
    tags.push({ name: 'author', content: data.author });
  }

  // Add robots meta tag
  tags.push({ name: 'robots', content: 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1' });

  // Add viewport meta tag
  tags.push({ name: 'viewport', content: 'width=device-width, initial-scale=1, shrink-to-fit=no' });

  return tags;
}

/**
 * Get homepage SEO data
 */
export function getHomepageSEOData(): SEOMetaData {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  
  return {
    title: 'Martyrs Archive - Comprehensive Documentation of Nigerian IMN Martyrs',
    description: 'Explore the Martyrs Archive, a comprehensive educational platform documenting the lives and sacrifices of martyrs from Nigeria, specifically focusing on the Islamic Movement of Nigeria (IMN). Search through 500+ IMN martyrs and 950+ total records spanning 1996-Present.',
    url: baseUrl,
    image: `${baseUrl}/images/og-homepage.jpg`,
    imageAlt: 'Martyrs Archive - Educational platform documenting Nigerian IMN martyrs',
    type: 'website',
    siteName: 'Martyrs Archive',
    locale: 'en_US',
    alternateLocale: ['ar_SA'],
    keywords: [
      'martyrs archive',
      'Islamic Movement Nigeria',
      'IMN martyrs',
      'historical documentation',
      'memorial database',
      'human rights',
      'religious freedom',
      'Nigeria',
      'Shia martyrs',
      'educational platform',
      'historical research',
      'memorial archive',
      'justice documentation',
      'faith sacrifice',
      'human dignity'
    ]
  };
}

/**
 * Utility function to inject meta tags into document head
 */
export function injectMetaTags(seoData: SEOMetaData, twitterData?: TwitterCardData): void {
  if (typeof window === 'undefined') return;

  const head = document.head;
  
  // Remove existing meta tags
  removeMetaTags();
  
  // Set document title
  document.title = seoData.title;
  
  // Add general meta tags
  const generalTags = generateGeneralMetaTags(seoData);
  generalTags.forEach(tag => {
    const meta = document.createElement('meta');
    meta.name = tag.name;
    meta.content = tag.content;
    meta.setAttribute('data-seo', 'true');
    head.appendChild(meta);
  });
  
  // Add Open Graph tags
  const ogTags = generateOpenGraphTags(seoData);
  ogTags.forEach(tag => {
    const meta = document.createElement('meta');
    meta.setAttribute('property', tag.property);
    meta.content = tag.content;
    meta.setAttribute('data-seo', 'true');
    head.appendChild(meta);
  });
  
  // Add Twitter Card tags
  if (twitterData) {
    const twitterTags = generateTwitterCardTags(twitterData);
    twitterTags.forEach(tag => {
      const meta = document.createElement('meta');
      meta.name = tag.name;
      meta.content = tag.content;
      meta.setAttribute('data-seo', 'true');
      head.appendChild(meta);
    });
  } else {
    // Generate Twitter Card from SEO data
    const defaultTwitterData: TwitterCardData = {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      image: seoData.image,
      imageAlt: seoData.imageAlt
    };
    
    const twitterTags = generateTwitterCardTags(defaultTwitterData);
    twitterTags.forEach(tag => {
      const meta = document.createElement('meta');
      meta.name = tag.name;
      meta.content = tag.content;
      meta.setAttribute('data-seo', 'true');
      head.appendChild(meta);
    });
  }
  
  // Add canonical link
  if (seoData.url) {
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.rel = 'canonical';
      canonical.setAttribute('data-seo', 'true');
      head.appendChild(canonical);
    }
    canonical.href = seoData.url;
  }
}

/**
 * Remove SEO meta tags from document head
 */
export function removeMetaTags(): void {
  if (typeof window === 'undefined') return;

  const seoElements = document.querySelectorAll('[data-seo="true"]');
  seoElements.forEach(element => element.remove());
}

/**
 * SEO metadata for Research Methodology page
 */
export const getResearchMethodologyPageMeta = (baseUrl: string = 'https://martyrsarchive.org') => {
  return {
    title: 'Research Methodology - Nigerian IMN Martyrs Archive',
    description: 'Learn about our comprehensive research methodology for documenting Nigerian IMN martyrs. Discover our verification processes, ethical guidelines, and academic standards.',
    url: `${baseUrl}/methodology`,
    image: `${baseUrl}/images/og-methodology.jpg`,
    imageAlt: 'Research Methodology - Nigerian IMN Martyrs Archive',
    type: 'website' as const,
    keywords: [
      'research methodology',
      'Nigerian IMN martyrs',
      'verification process',
      'ethical guidelines',
      'academic standards',
      'historical documentation',
      'Islamic Movement Nigeria',
      'martyrdom research'
    ]
  };
};

/**
 * SEO metadata for Educational Resources page
 */
export const getEducationalResourcesPageMeta = (baseUrl: string = 'https://martyrsarchive.org') => {
  return {
    title: 'Educational Resources - Nigerian IMN Martyrs Archive',
    description: 'Access comprehensive educational materials about Nigerian IMN martyrs and Islamic history. Download research guides, timelines, and academic resources.',
    url: `${baseUrl}/educational`,
    image: `${baseUrl}/images/og-educational.jpg`,
    imageAlt: 'Educational Resources - Nigerian IMN Martyrs Archive',
    type: 'website' as const,
    keywords: [
      'educational resources',
      'Nigerian IMN martyrs',
      'research guides',
      'historical timelines',
      'academic materials',
      'Islamic history',
      'learning resources',
      'educational platform'
    ]
  };
};

/**
 * SEO metadata for Timeline page
 */
export const getTimelinePageMeta = (baseUrl: string = 'https://martyrsarchive.org') => {
  return {
    title: 'IMN Historical Timeline - Nigerian Islamic Movement Martyrs Archive',
    description: 'Comprehensive chronological timeline of the Islamic Movement of Nigeria (IMN) from 1996 to present. Documenting peaceful beginnings, tragic persecutions including the 2015 Zaria Massacre, and the ongoing struggle for justice and religious freedom.',
    url: `${baseUrl}/timeline`,
    image: `${baseUrl}/images/og-timeline.jpg`,
    imageAlt: 'IMN Historical Timeline - Nigerian Islamic Movement Martyrs Archive',
    type: 'website' as const,
    keywords: [
      'IMN timeline',
      'Islamic Movement Nigeria history',
      'Nigerian Shia timeline',
      'Zaria massacre 2015',
      'Sheikh Zakzaky',
      'IMN persecution',
      'Nigerian religious freedom',
      'Islamic Movement chronology',
      'Nigeria martyrs timeline',
      'IMN historical events',
      'Nigerian Shia history',
      'religious persecution Nigeria'
    ]
  };
};