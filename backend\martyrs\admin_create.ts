import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";
import { logMartyrCreate } from "../auth/auditLog";

interface CreateMartyrRequest {
  name: string;
  slug: string;
  birthDate?: string;
  birthPlace?: string;
  deathDate?: string;
  deathPlace?: string;
  martyrdomCause?: string;
  martyrdomContext?: string;
  bio: string;
  subCategories: string[];
  region?: string;
  period?: string;
  quotes: string[];
  familyInfo?: string;
  latitude?: number | null;
  longitude?: number | null;
}

interface Martyr {
  id: number;
  name: string;
  slug: string;
  birthDate?: string;
  birthPlace?: string;
  deathDate?: string;
  deathPlace?: string;
  martyrdomCause?: string;
  martyrdomContext?: string;
  bio: string;
  subCategories: string[];
  region?: string;
  period?: string;
  quotes: string[];
  familyInfo?: string;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  latitude?: number | null;
  longitude?: number | null;
}

// Creates a new martyr profile.
export const createMartyr = api<CreateMartyrRequest, Martyr>(
  { auth: true, expose: true, method: "POST", path: "/admin/martyrs" },
  async (req) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate required fields
    if (!req.name?.trim()) {
      throw APIError.invalidArgument("Name is required");
    }
    if (!req.slug?.trim()) {
      throw APIError.invalidArgument("Slug is required");
    }
    if (!req.bio?.trim()) {
      throw APIError.invalidArgument("Biography is required");
    }

    const timestamp = new Date().toISOString();

    try {
      const result = await martyrsDB.queryRow<{
        id: number;
        name: string;
        slug: string;
        birth_date?: string;
        birth_place?: string;
        death_date?: string;
        death_place?: string;
        martyrdom_cause?: string;
        martyrdom_context?: string;
        bio: string;
        sub_categories: string[];
        region?: string;
        period?: string;
        quotes: string[];
        family_info?: string;
        view_count: number;
        created_at: string;
        updated_at: string;
        latitude?: number | null;
        longitude?: number | null;
      }>`
        INSERT INTO martyrs (
          name, slug, birth_date, birth_place, death_date, death_place,
          martyrdom_cause, martyrdom_context, bio, sub_categories, region, period,
          quotes, family_info, latitude, longitude, created_at, updated_at
        ) VALUES (
          ${req.name}, ${req.slug}, ${req.birthDate}, ${req.birthPlace},
          ${req.deathDate}, ${req.deathPlace}, ${req.martyrdomCause},
          ${req.martyrdomContext}, ${req.bio}, ${req.subCategories},
          ${req.region}, ${req.period}, ${req.quotes}, ${req.familyInfo},
          ${req.latitude}, ${req.longitude}, ${timestamp}, ${timestamp}
        )
        RETURNING *
      `;

      if (!result) {
        throw APIError.internal("Failed to create martyr");
      }

      // Log the creation
      await logMartyrCreate(result.id, result.name);

      return {
        id: result.id,
        name: result.name,
        slug: result.slug,
        birthDate: result.birth_date,
        birthPlace: result.birth_place,
        deathDate: result.death_date,
        deathPlace: result.death_place,
        martyrdomCause: result.martyrdom_cause,
        martyrdomContext: result.martyrdom_context,
        bio: result.bio,
        subCategories: result.sub_categories,
        region: result.region,
        period: result.period,
        quotes: result.quotes,
        familyInfo: result.family_info,
        viewCount: result.view_count,
        createdAt: result.created_at,
        updatedAt: result.updated_at,
        latitude: result.latitude,
        longitude: result.longitude
      };
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw APIError.internal("Database error: " + (error as Error).message);
    }
  }
);