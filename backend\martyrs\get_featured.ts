import { api } from "encore.dev/api";
import { martyrsDB } from "./db";
import type { MartyrCard } from "./types";

interface GetFeaturedResponse {
  featured: MartyrCard[];
}

// Retrieves featured martyrs for the homepage carousel.
export const getFeatured = api<void, GetFeaturedResponse>(
  { expose: true, method: "GET", path: "/featured" },
  async () => {
    const featured = await martyrsDB.queryAll<{
      id: number;
      name: string;
      slug: string;
      bio: string;
      sub_categories: string[];
      region?: string;
      profile_image?: string;
    }>`
      SELECT 
        m.id,
        m.name,
        m.slug,
        LEFT(m.bio, 150) as bio,
        m.sub_categories,
        m.region,
        mi.url as profile_image
      FROM martyrs m
      LEFT JOIN martyr_images mi ON m.id = mi.martyr_id AND mi.is_profile_image = true
      WHERE ${'Shi\'a IMN Martyrs'} = ANY(m.sub_categories)
      ORDER BY m.view_count DESC, m.created_at DESC
      LIMIT 5
    `;

    return {
      featured: featured.map(m => ({
        id: m.id,
        name: m.name,
        slug: m.slug,
        bio: m.bio,
        subCategories: m.sub_categories || [],
        region: m.region,
        profileImage: m.profile_image
      }))
    };
  }
);
