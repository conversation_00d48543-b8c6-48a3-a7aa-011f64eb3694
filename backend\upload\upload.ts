import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "../martyrs/db";
import { StorageService } from "./storage";
import type { UploadResponse, SignedUploadURL, FileMetadata } from "./types";

const storage = new StorageService();

interface GetSignedUrlRequest {
  filename: string;
  contentType: string;
  fileSize: number;
}

// Generate signed upload URL for direct frontend uploads
export const getSignedUploadUrl = api<GetSignedUrlRequest, SignedUploadURL>(
  { auth: true, expose: true, method: "POST", path: "/admin/upload/signed-url" },
  async (req) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate file type (images only)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(req.contentType.toLowerCase())) {
      throw APIError.invalidArgument("Only image files are allowed (JPEG, PNG, WebP, GIF)");
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (req.fileSize > maxSize) {
      throw APIError.invalidArgument("File size must be less than 10MB");
    }

    // Validate filename
    if (!req.filename || req.filename.length > 255) {
      throw APIError.invalidArgument("Invalid filename");
    }

    try {
      return await storage.generateSignedUploadUrl(req.filename, req.contentType);
    } catch (error) {
      throw APIError.internal("Failed to generate upload URL");
    }
  }
);

interface DirectUploadRequest {
  fileData: string; // Base64 encoded file data
  filename: string;
  contentType: string;
  martyrId?: number;
}

// Alternative: Direct upload endpoint for smaller files
export const uploadFile = api<DirectUploadRequest, UploadResponse>(
  { auth: true, expose: true, method: "POST", path: "/admin/upload/direct" },
  async (req) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(req.contentType.toLowerCase())) {
      throw APIError.invalidArgument("Only image files are allowed");
    }

    try {
      // Decode base64 file data
      const base64Data = req.fileData.split(',')[1] || req.fileData;
      
      // Simple base64 decode without Buffer dependency
      let binaryString = '';
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
      for (let i = 0; i < base64Data.length; i += 4) {
        const chunk = base64Data.substr(i, 4);
        let encoded = 0;
        for (let j = 0; j < chunk.length; j++) {
          encoded = (encoded << 6) | chars.indexOf(chunk[j]);
        }
        for (let j = 16; j >= 0; j -= 8) {
          if (j === 0 && chunk.indexOf('=') !== -1) break;
          binaryString += String.fromCharCode((encoded >> j) & 255);
        }
      }
      
      const buffer = new Uint8Array(
        binaryString
          .split('')
          .map((char: string) => char.charCodeAt(0))
      );

      // Validate file size
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (buffer.length > maxSize) {
        throw APIError.invalidArgument("File size must be less than 10MB");
      }

      // Upload file to storage
      const fileUrl = await storage.uploadFile(buffer, req.filename, req.contentType);
      
      // Generate unique ID for the file
      const fileId = (globalThis as any).crypto?.randomUUID() || 
        'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      const timestamp = new Date().toISOString();

      // Store file metadata in database
      await martyrsDB.exec`
        INSERT INTO file_uploads (
          id, original_filename, stored_filename, file_url, file_size, 
          mime_type, uploaded_by, martyr_id, created_at
        ) VALUES (
          ${fileId}, ${req.filename}, ${fileId + '-' + req.filename}, 
          ${fileUrl}, ${buffer.length}, ${req.contentType}, 
          ${auth.userID}, ${req.martyrId}, ${timestamp}
        )
      `;

      return {
        file: {
          id: fileId,
          url: fileUrl,
          filename: req.filename,
          size: buffer.length,
          mimeType: req.contentType,
          uploadedAt: timestamp
        },
        success: true,
        message: "File uploaded successfully"
      };
    } catch (error) {
      throw APIError.internal("Failed to upload file: " + (error as Error).message);
    }
  }
);

interface DeleteFileRequest {
  fileUrl: string;
}

// Delete uploaded file
export const deleteFile = api<DeleteFileRequest, { success: boolean; message: string }>(
  { auth: true, expose: true, method: "DELETE", path: "/admin/upload/file" },
  async (req) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    try {
      // Find file in database
      const fileRecord = await martyrsDB.queryRow<{ id: string; uploaded_by: string }>`
        SELECT id, uploaded_by FROM file_uploads WHERE file_url = ${req.fileUrl}
      `;

      if (!fileRecord) {
        throw APIError.notFound("File not found");
      }

      // Delete from storage
      const deleted = await storage.deleteFile(req.fileUrl);
      
      if (deleted) {
        // Remove from database
        await martyrsDB.exec`
          DELETE FROM file_uploads WHERE file_url = ${req.fileUrl}
        `;

        return {
          success: true,
          message: "File deleted successfully"
        };
      } else {
        throw APIError.internal("Failed to delete file from storage");
      }
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw APIError.internal("Failed to delete file: " + (error as Error).message);
    }
  }
);

interface ListFilesRequest {
  martyrId?: number;
  limit?: number;
  offset?: number;
}

interface ListFilesResponse {
  files: FileMetadata[];
  total: number;
}

// List uploaded files
export const listFiles = api<ListFilesRequest, ListFilesResponse>(
  { auth: true, expose: true, method: "GET", path: "/admin/upload/files" },
  async (req) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    const { martyrId, limit = 50, offset = 0 } = req;

    let whereClause = "WHERE 1=1";
    const params: any[] = [];
    let paramIndex = 1;

    if (martyrId) {
      whereClause += ` AND martyr_id = $${paramIndex}`;
      params.push(martyrId);
      paramIndex++;
    }

    const [files, countResult] = await Promise.all([
      martyrsDB.rawQueryAll<{
        id: string;
        original_filename: string;
        stored_filename: string;
        file_url: string;
        file_size: number;
        mime_type: string;
        uploaded_by: string;
        martyr_id?: number;
        created_at: string;
      }>(`
        SELECT 
          id, original_filename, stored_filename, file_url, file_size,
          mime_type, uploaded_by, martyr_id, created_at
        FROM file_uploads 
        ${whereClause}
        ORDER BY created_at DESC 
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, ...params, limit, offset),
      
      martyrsDB.rawQueryRow<{ total: number }>(
        `SELECT COUNT(*) as total FROM file_uploads ${whereClause}`,
        ...params
      )
    ]);

    return {
      files: files.map(file => ({
        id: file.id,
        originalFilename: file.original_filename,
        storedFilename: file.stored_filename,
        fileUrl: file.file_url,
        fileSize: file.file_size,
        mimeType: file.mime_type,
        uploadedBy: file.uploaded_by,
        martyrId: file.martyr_id,
        createdAt: file.created_at
      })),
      total: countResult?.total || 0
    };
  }
);