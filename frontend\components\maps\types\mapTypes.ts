export interface MartyrMapData {
  name: string;
  slug: string;
  latitude: number;
  longitude: number;
  subCategories?: string[];
  profileImage?: string;
}

export interface MapContainerProps {
  center?: [number, number];
  zoom?: number;
  height?: string;
  className?: string;
  onMarkerClick?: (martyr: MartyrMapData) => void;
  showClustering?: boolean;
  markers?: MartyrMapData[];
  loading?: boolean;
}

export interface ClusterFeature {
  get: (key: string) => any;
  getGeometry: () => any;
}

export interface MapContextType {
  map: import('ol/Map').default | null;
  addLayer: (layer: import('ol/layer/Base').default) => void;
  removeLayer: (layer: import('ol/layer/Base').default) => void;
}

export interface UseMapInstanceOptions {
  center: [number, number];
  zoom: number;
  onMapReady?: (map: import('ol/Map').default) => void;
}

export interface MarkerLayerProps {
  markers: MartyrMapData[];
  onMarkerClick?: (martyr: MartyrMapData) => void;
  showClustering?: boolean;
}

export interface PopupOverlayProps {
  martyr: MartyrMapData | null;
  position: [number, number] | null;
  onClose: () => void;
}