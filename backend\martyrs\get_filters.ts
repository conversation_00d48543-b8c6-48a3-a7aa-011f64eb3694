import { api } from "encore.dev/api";
import { martyrsDB } from "./db";

interface FilterOptions {
  subCategories: string[];
  regions: string[];
  periods: string[];
  causes: string[];
}

// Retrieves available filter options for search.
export const getFilters = api<void, FilterOptions>(
  { expose: true, method: "GET", path: "/filters" },
  async () => {
    const subCategories = await martyrsDB.queryAll<{ category: string }>`
      SELECT DISTINCT unnest(sub_categories) as category
      FROM martyrs
      WHERE sub_categories IS NOT NULL
      ORDER BY category
    `;

    const regions = await martyrsDB.queryAll<{ region: string }>`
      SELECT DISTINCT region
      FROM martyrs
      WHERE region IS NOT NULL
      ORDER BY region
    `;

    const periods = await martyrsDB.queryAll<{ period: string }>`
      SELECT DISTINCT period
      FROM martyrs
      WHERE period IS NOT NULL
      ORDER BY period
    `;

    const causes = await martyrsDB.queryAll<{ cause: string }>`
      SELECT DISTINCT martyrdom_cause as cause
      FROM martyrs
      WHERE martyrdom_cause IS NOT NULL
      ORDER BY cause
    `;

    return {
      subCategories: subCategories.map(sc => sc.category),
      regions: regions.map(r => r.region),
      periods: periods.map(p => p.period),
      causes: causes.map(c => c.cause)
    };
  }
);
