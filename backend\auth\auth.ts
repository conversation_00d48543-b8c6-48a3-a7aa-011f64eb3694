import { API<PERSON>rror, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from "encore.dev/api";
import { authHand<PERSON> } from "encore.dev/auth";
import { verifyJWT } from "./jwt";

interface AuthParams {
  authorization?: Header<"Authorization">;
  cookie?: Header<"Cookie">;
}

export interface AuthData {
  userID: string;
  role: string;
  email: string;
}

const auth = authHandler<AuthParams, AuthData>(
  async (params) => {
    // First try to get token from Authorization header (for API calls)
    let token = params.authorization?.replace("Bearer ", "");
    
    // If not found in header, try to get from cookies
    if (!token && params.cookie) {
      const cookies = parseCookies(params.cookie);
      token = cookies['admin_access_token'];
    }
    
    if (!token) {
      throw APIError.unauthenticated("missing token");
    }

    // Verify JWT token
    try {
      const payload = verifyJWT(token);
      
      return {
        userID: payload.userId,
        role: payload.role,
        email: payload.email
      };
    } catch (error) {
      throw APIError.unauthenticated("invalid token");
    }
  }
);

// Configure the API gateway to use the auth handler
export const gw = new Gateway({ authHandler: auth });

export { auth };

// Helper function to parse cookies
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};
  if (!cookieHeader) return cookies;
  
  cookieHeader.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = value;
    }
  });
  
  return cookies;
}