// Error handling utilities with retry mechanisms and graceful fallbacks

// Custom error types
export class NetworkError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Retry options
interface RetryOptions {
  maxRetries?: number;
  delay?: number;
  exponentialBackoff?: boolean;
  retryOn?: number[]; // HTTP status codes to retry on
}

// Default retry configuration
const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 3,
  delay: 1000,
  exponentialBackoff: true,
  retryOn: [408, 429, 500, 502, 503, 504]
};

// Enhanced fetch with retry mechanism
export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retryOptions: RetryOptions = {}
): Promise<Response> {
  const opts = { ...DEFAULT_RETRY_OPTIONS, ...retryOptions };
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= (opts.maxRetries || 0); attempt++) {
    try {
      const response = await fetch(url, {
        ...options,
        credentials: 'include' // Include cookies for authentication
      });

      // If successful, return the response
      if (response.ok) {
        return response;
      }

      // Check if we should retry based on status code
      if (opts.retryOn && opts.retryOn.includes(response.status)) {
        lastError = new NetworkError(`HTTP ${response.status}: ${response.statusText}`, response.status);
        
        // If this isn't the last attempt, wait before retrying
        if (attempt < (opts.maxRetries || 0)) {
          const delay = opts.exponentialBackoff 
            ? (opts.delay || 1000) * Math.pow(2, attempt)
            : (opts.delay || 1000);
          
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }

      // For authentication errors
      if (response.status === 401) {
        throw new AuthenticationError('Authentication failed');
      }

      // For client errors, don't retry
      if (response.status >= 400 && response.status < 500) {
        throw new Error(`Client error: ${response.status} ${response.statusText}`);
      }

      // For other errors, throw a NetworkError
      throw new NetworkError(`HTTP ${response.status}: ${response.statusText}`, response.status);
      
    } catch (error) {
      lastError = error as Error;
      
      // If this is the last attempt, throw the error
      if (attempt >= (opts.maxRetries || 0)) {
        throw lastError;
      }
      
      // Wait before retrying
      const delay = opts.exponentialBackoff 
        ? (opts.delay || 1000) * Math.pow(2, attempt)
        : (opts.delay || 1000);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // This should never be reached, but just in case
  throw lastError || new Error('Unknown error occurred');
}

// Wrapper for API calls with error handling
export async function apiCall<T>(
  call: () => Promise<T>,
  options: {
    onError?: (error: Error) => void;
    onRetry?: (attempt: number) => void;
    maxRetries?: number;
  } = {}
): Promise<T | null> {
  const maxRetries = options.maxRetries ?? 3;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await call();
    } catch (error) {
      // If this is the last attempt, call onError and return null
      if (attempt >= maxRetries) {
        options.onError?.(error as Error);
        return null;
      }
      
      // Notify about retry
      options.onRetry?.(attempt + 1);
      
      // Wait before retrying with exponential backoff
      const delay = 1000 * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  return null;
}

// Error message utilities
export function getErrorMessage(error: Error): string {
  if (error instanceof AuthenticationError) {
    return 'Your session has expired. Please log in again.';
  }
  
  if (error instanceof NetworkError) {
    if (error.statusCode === 408) return 'Request timeout. Please try again.';
    if (error.statusCode === 429) return 'Too many requests. Please wait a moment and try again.';
    if (error.statusCode === 500) return 'Server error. Please try again later.';
    if (error.statusCode === 502 || error.statusCode === 503 || error.statusCode === 504) {
      return 'Service temporarily unavailable. Please try again later.';
    }
    return `Network error (${error.statusCode}): ${error.message}`;
  }
  
  if (error instanceof ValidationError) {
    return error.field ? `${error.field}: ${error.message}` : error.message;
  }
  
  return error.message || 'An unknown error occurred';
}

// Check if error is recoverable
export function isRecoverableError(error: Error): boolean {
  if (error instanceof AuthenticationError) {
    return false; // Need to re-authenticate
  }
  
  if (error instanceof NetworkError) {
    // Network errors and server errors are typically recoverable
    return true;
  }
  
  // Other errors are not recoverable
  return false;
}