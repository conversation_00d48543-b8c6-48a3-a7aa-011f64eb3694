import React, { createContext, useContext, useEffect, useState } from 'react';

interface MotionPreferencesContextType {
  prefersReducedMotion: boolean;
  respectMotionPreferences: boolean;
  setRespectMotionPreferences: (value: boolean) => void;
}

const MotionPreferencesContext = createContext<MotionPreferencesContextType>({
  prefersReducedMotion: false,
  respectMotionPreferences: true,
  setRespectMotionPreferences: () => {}
});

export const useMotionPreferences = () => {
  const context = useContext(MotionPreferencesContext);
  if (!context) {
    throw new Error('useMotionPreferences must be used within MotionPreferencesProvider');
  }
  return context;
};

interface MotionPreferencesProviderProps {
  children: React.ReactNode;
}

export const MotionPreferencesProvider: React.FC<MotionPreferencesProviderProps> = ({ children }) => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [respectMotionPreferences, setRespectMotionPreferences] = useState(true);

  useEffect(() => {
    // Check for user's motion preferences
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const value = {
    prefersReducedMotion,
    respectMotionPreferences,
    setRespectMotionPreferences
  };

  return (
    <MotionPreferencesContext.Provider value={value}>
      {children}
    </MotionPreferencesContext.Provider>
  );
};

// Component to conditionally render animations
interface ConditionalMotionProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  forceAnimation?: boolean;
}

export const ConditionalMotion: React.FC<ConditionalMotionProps> = ({ 
  children, 
  fallback = null, 
  forceAnimation = false 
}) => {
  const { prefersReducedMotion, respectMotionPreferences } = useMotionPreferences();
  
  const shouldAnimate = forceAnimation || !respectMotionPreferences || !prefersReducedMotion;
  
  return <>{shouldAnimate ? children : (fallback || children)}</>;
};