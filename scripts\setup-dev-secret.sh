#!/usr/bin/env bash
# Generates secure random secrets for local development and prints commands
set -euo pipefail

if ! command -v openssl >/dev/null 2>&1; then
  echo "openssl not found. Please install openssl to use this script (brew install openssl)" >&2
  exit 1
fi

ADMIN_SECRET=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -hex 32)

cat <<EOF
Generated DEV_ADMIN_SECRET (do NOT commit this):
$ADMIN_SECRET

Generated DEV_JWT_SECRET (do NOT commit this):
$JWT_SECRET

To set these for local env (zsh):
export DEV_ADMIN_SECRET='$ADMIN_SECRET'
export DEV_JWT_SECRET='$JWT_SECRET'

To set these via Encore CLI (preferred, requires encore installed & logged in):
# run from the backend directory
cd backend
encore secret set AdminSecret '$ADMIN_SECRET'
encore secret set JWTSecret '$JWT_SECRET'

EOF