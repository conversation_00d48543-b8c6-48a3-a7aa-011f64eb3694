/**
 * Utility functions for handling animations and motion preferences
 */

// Check if user prefers reduced motion
export const prefersReducedMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
  return mediaQuery.matches;
};

// Get animation duration based on user preferences
export const getAnimationDuration = (duration: number): number => {
  return prefersReducedMotion() ? 0 : duration;
};

// Get safe animation config for framer-motion
export const getSafeAnimationConfig = (config: any) => {
  if (prefersReducedMotion()) {
    return {
      ...config,
      animate: config.initial || {},
      transition: { duration: 0 }
    };
  }
  return config;
};

// Debounce function for performance optimization
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: number;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = window.setTimeout(() => func(...args), delay);
  };
};

// Throttle function for scroll events
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), delay);
    }
  };
};

// Generate staggered delay for list animations
export const getStaggerDelay = (index: number, baseDelay: number = 100): number => {
  return prefersReducedMotion() ? 0 : index * baseDelay;
};

// Safe requestAnimationFrame wrapper
export const safeRAF = (callback: FrameRequestCallback): number => {
  if (typeof window !== 'undefined' && window.requestAnimationFrame) {
    return window.requestAnimationFrame(callback);
  }
  return window.setTimeout(callback, 16); // Fallback to setTimeout
};

// Cancel animation frame safely
export const safeCancelRAF = (id: number): void => {
  if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
    window.cancelAnimationFrame(id);
  } else {
    clearTimeout(id);
  }
};