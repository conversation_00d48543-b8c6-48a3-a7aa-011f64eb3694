import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";

interface ViewsOverTimeResponse {
  data: {
    date: string;
    views: number;
  }[];
}

// Gets aggregated view data for charts.
export const getViewsOverTime = api<{}, ViewsOverTimeResponse>(
  { auth: true, expose: true, method: "GET", path: "/admin/charts/views" },
  async () => {
    const auth = getAuthData()!;
    // This check is redundant due to auth:true, but good for defense in depth
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    const data = await martyrsDB.queryAll<{
      date: string;
      views: number;
    }>`
      SELECT
        view_date::TEXT as date,
        SUM(view_count)::INT as views
      FROM martyr_daily_views
      WHERE view_date >= NOW() - INTERVAL '30 days'
      GROUP BY view_date
      ORDER BY view_date
    `;

    return { data };
  }
);
