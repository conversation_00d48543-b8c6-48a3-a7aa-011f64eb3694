import { APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";

// Define the structure of an audit log entry
export interface AuditLogEntry {
  id: string;
  userId: string;
  userEmail: string;
  action: string;
  resourceType: string;
  resourceId?: string | number;
  details?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
}

// Simple in-memory store for audit logs
// In production, you would use a database table
class AuditLogger {
  private logs: AuditLogEntry[] = [];
  
  // Log an admin action
  async logAction(
    action: string,
    resourceType: string,
    resourceId?: string | number,
    details?: Record<string, any>
  ): Promise<void> {
    try {
      // Get authenticated user data
      const authData = getAuthData();
      
      if (!authData) {
        throw new Error("No authenticated user data available");
      }
      
      // Get request metadata (this would come from the request context in a real implementation)
      const ipAddress = (globalThis as any).process?.env?.TEST_IP || '127.0.0.1';
      const userAgent = (globalThis as any).process?.env?.TEST_USER_AGENT || 'Unknown';
      
      // Create audit log entry
      const logEntry: AuditLogEntry = {
        id: this.generateId(),
        userId: authData.userID,
        userEmail: authData.email,
        action,
        resourceType,
        resourceId,
        details,
        ipAddress,
        userAgent,
        timestamp: new Date().toISOString()
      };
      
      // Store the log entry
      this.logs.push(logEntry);
      
      // In a real implementation, you would store this in a database
      console.log('Audit Log Entry:', JSON.stringify(logEntry, null, 2));
      
      // Keep only the last 1000 logs to prevent memory issues
      if (this.logs.length > 1000) {
        this.logs = this.logs.slice(-1000);
      }
    } catch (error) {
      // Don't let audit logging failures break the main functionality
      console.error('Failed to log audit entry:', error);
    }
  }
  
  // Get audit logs (with optional filtering)
  getLogs(options?: {
    userId?: string;
    action?: string;
    resourceType?: string;
    limit?: number;
    offset?: number;
  }): AuditLogEntry[] {
    let filteredLogs = [...this.logs];
    
    // Apply filters
    if (options?.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === options.userId);
    }
    
    if (options?.action) {
      filteredLogs = filteredLogs.filter(log => log.action === options.action);
    }
    
    if (options?.resourceType) {
      filteredLogs = filteredLogs.filter(log => log.resourceType === options.resourceType);
    }
    
    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    // Apply pagination
    const limit = options?.limit || 50;
    const offset = options?.offset || 0;
    
    return filteredLogs.slice(offset, offset + limit);
  }
  
  // Get total count of logs (with optional filtering)
  getLogCount(options?: {
    userId?: string;
    action?: string;
    resourceType?: string;
  }): number {
    let filteredLogs = [...this.logs];
    
    // Apply filters
    if (options?.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === options.userId);
    }
    
    if (options?.action) {
      filteredLogs = filteredLogs.filter(log => log.action === options.action);
    }
    
    if (options?.resourceType) {
      filteredLogs = filteredLogs.filter(log => log.resourceType === options.resourceType);
    }
    
    return filteredLogs.length;
  }
  
  // Generate a unique ID for log entries
  private generateId(): string {
    return (globalThis as any).crypto?.randomUUID?.() || 
      'log_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

// Export singleton instance
export const auditLogger = new AuditLogger();

// Helper functions for common audit log actions
export async function logMartyrCreate(martyrId: number, martyrName: string): Promise<void> {
  await auditLogger.logAction('CREATE', 'MARTYR', martyrId, { name: martyrName });
}

export async function logMartyrUpdate(martyrId: number, martyrName: string): Promise<void> {
  await auditLogger.logAction('UPDATE', 'MARTYR', martyrId, { name: martyrName });
}

export async function logMartyrDelete(martyrId: number, martyrName: string): Promise<void> {
  await auditLogger.logAction('DELETE', 'MARTYR', martyrId, { name: martyrName });
}

export async function logImageUpload(imageId: number, martyrId: number): Promise<void> {
  await auditLogger.logAction('UPLOAD', 'IMAGE', imageId, { martyrId });
}

export async function logImageDelete(imageId: number): Promise<void> {
  await auditLogger.logAction('DELETE', 'IMAGE', imageId);
}

export async function logTimelineEventCreate(eventId: number, martyrId: number): Promise<void> {
  await auditLogger.logAction('CREATE', 'TIMELINE_EVENT', eventId, { martyrId });
}

export async function logTimelineEventDelete(eventId: number): Promise<void> {
  await auditLogger.logAction('DELETE', 'TIMELINE_EVENT', eventId);
}

export async function logAdminLogin(email: string): Promise<void> {
  await auditLogger.logAction('LOGIN', 'ADMIN', undefined, { email });
}

export async function logAdminLogout(email: string): Promise<void> {
  await auditLogger.logAction('LOGOUT', 'ADMIN', undefined, { email });
}