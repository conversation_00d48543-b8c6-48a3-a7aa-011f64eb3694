// Design System Tokens for Martyrs Archive
// Standardized colors, spacing, typography, and component variants

export const colors = {
  // Primary Brand Colors
  primary: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981', // Main emerald
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
  },
  
  // Secondary Colors
  secondary: {
    50: '#f0fdfa',
    100: '#ccfbf1',
    200: '#99f6e4',
    300: '#5eead4',
    400: '#2dd4bf',
    500: '#14b8a6', // Main teal
    600: '#0d9488',
    700: '#0f766e',
    800: '#115e59',
    900: '#134e4a',
  },
  
  // Accent Colors
  accent: {
    amber: {
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
    },
    red: {
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
    },
    blue: {
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
    },
    purple: {
      500: '#8b5cf6',
      600: '#7c3aed',
      700: '#6d28d9',
    },
  },
  
  // Neutral Colors
  neutral: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },
  
  // Semantic Colors
  semantic: {
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  // Background Gradients
  gradients: {
    heroBackground: 'from-emerald-900 via-teal-800 to-slate-900',
    primaryButton: 'from-emerald-600 to-teal-600',
    cardBackground: 'from-slate-50 via-emerald-50/30 to-amber-50/20',
    missionBackground: 'from-emerald-50 via-teal-50 to-amber-50',
    categoryCard: {
      imn: 'from-emerald-50 to-teal-50',
      religious: 'from-blue-50 to-indigo-50',
      global: 'from-purple-50 to-violet-50',
    }
  }
};

export const spacing = {
  // Section Spacing
  section: {
    mobile: 'py-16',
    desktop: 'py-20 md:py-24',
    hero: 'py-16 md:py-24 lg:py-32',
  },
  
  // Component Spacing
  component: {
    xs: 'p-2',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  },
  
  // Grid Gaps
  grid: {
    sm: 'gap-4',
    md: 'gap-6 md:gap-8',
    lg: 'gap-8 md:gap-12',
  },
  
  // Margins
  margin: {
    xs: 'mb-4',
    sm: 'mb-6',
    md: 'mb-8',
    lg: 'mb-12',
    xl: 'mb-16',
  }
};

export const typography = {
  // Heading Scales
  headings: {
    h1: 'text-4xl sm:text-6xl md:text-8xl font-bold',
    h2: 'text-3xl md:text-4xl lg:text-5xl font-bold',
    h3: 'text-2xl md:text-3xl font-bold',
    h4: 'text-xl md:text-2xl font-semibold',
    h5: 'text-lg md:text-xl font-semibold',
    h6: 'text-base md:text-lg font-semibold',
  },
  
  // Body Text
  body: {
    xs: 'text-sm',
    sm: 'text-base',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl',
  },
  
  // Arabic Typography
  arabic: {
    elegant: 'arabic-text-elegant',
    large: 'arabic-text-large text-xl md:text-2xl',
    xl: 'arabic-text-xl text-2xl md:text-3xl',
  },
  
  // Font Weights
  weights: {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  }
};

export const shadows = {
  card: 'shadow-lg hover:shadow-xl',
  button: 'shadow-md hover:shadow-lg',
  hero: 'shadow-2xl hover:shadow-3xl',
  floating: 'drop-shadow-lg',
};

export const borders = {
  radius: {
    sm: 'rounded-lg',
    md: 'rounded-xl',
    lg: 'rounded-2xl',
    xl: 'rounded-3xl',
  },
  
  width: {
    thin: 'border',
    medium: 'border-2',
    thick: 'border-3',
  }
};

export const animations = {
  // Transition Durations
  duration: {
    fast: 'duration-200',
    normal: 'duration-300',
    slow: 'duration-500',
    slower: 'duration-700',
  },
  
  // Transform Effects
  hover: {
    scale: 'hover:scale-105',
    scaleSmall: 'hover:scale-[1.02]',
    rotate: 'hover:rotate-1',
  },
  
  // Transition Classes
  transition: {
    all: 'transition-all duration-300',
    colors: 'transition-colors duration-300',
    transform: 'transition-transform duration-300',
    shadow: 'transition-shadow duration-300',
  }
};

// Component Variants
export const componentVariants = {
  // Button Variants
  button: {
    primary: {
      base: `bg-gradient-to-r ${colors.gradients.primaryButton} text-white ${shadows.button} ${animations.transition.all} ${animations.hover.scale} focus:ring-4 focus:ring-emerald-500/50`,
      sizes: {
        sm: 'px-4 py-2 text-sm min-h-[36px]',
        md: 'px-6 py-3 text-base min-h-[44px]',
        lg: 'px-8 py-4 text-lg min-h-[48px]',
        xl: 'px-10 py-5 text-xl min-h-[52px]',
      }
    },
    
    secondary: {
      base: `border-2 border-emerald-600 text-emerald-700 hover:bg-emerald-600 hover:text-white ${shadows.button} ${animations.transition.all} ${animations.hover.scale} focus:ring-4 focus:ring-emerald-500/50`,
      sizes: {
        sm: 'px-4 py-2 text-sm min-h-[36px]',
        md: 'px-6 py-3 text-base min-h-[44px]',
        lg: 'px-8 py-4 text-lg min-h-[48px]',
        xl: 'px-10 py-5 text-xl min-h-[52px]',
      }
    },
    
    ghost: {
      base: `text-slate-600 hover:text-emerald-700 hover:bg-emerald-50/80 ${animations.transition.all} focus:ring-4 focus:ring-emerald-500/50`,
      sizes: {
        sm: 'px-3 py-2 text-sm min-h-[36px]',
        md: 'px-4 py-2 text-base min-h-[44px]',
        lg: 'px-6 py-3 text-lg min-h-[48px]',
      }
    }
  },
  
  // Card Variants
  card: {
    default: {
      base: `bg-white/90 backdrop-blur-sm ${borders.radius.lg} ${shadows.card} border border-slate-100 ${animations.transition.all} overflow-hidden`,
      hover: `${animations.hover.scale} hover:shadow-2xl`,
    },
    
    category: {
      base: `${borders.radius.lg} ${shadows.card} ${animations.transition.all} border-2 overflow-hidden relative`,
      hover: `${animations.hover.scale} hover:shadow-2xl`,
    },
    
    martyr: {
      base: `bg-white/90 backdrop-blur-sm ${borders.radius.lg} ${shadows.card} border-0 overflow-hidden relative`,
      hover: `${animations.hover.scale} hover:shadow-2xl`,
    }
  },
  
  // Input Variants
  input: {
    default: {
      base: `border border-emerald-200/80 ${borders.radius.md} focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500 bg-white/95 backdrop-blur-sm ${animations.transition.all} placeholder:text-slate-400`,
      sizes: {
        sm: 'px-3 py-2 text-sm',
        md: 'px-4 py-2.5 text-sm',
        lg: 'px-4 py-3 text-base',
      }
    }
  }
};

// Icon Sizes
export const iconSizes = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-12 h-12',
  '3xl': 'w-16 h-16',
};

// Container Widths
export const containers = {
  sm: 'max-w-2xl mx-auto',
  md: 'max-w-4xl mx-auto',
  lg: 'max-w-6xl mx-auto',
  xl: 'max-w-7xl mx-auto',
  full: 'w-full',
};

// Utility Functions
export const getButtonClasses = (variant: 'primary' | 'secondary' | 'ghost', size: keyof typeof componentVariants.button.primary.sizes) => {
  const baseClasses = componentVariants.button[variant].base;
  const sizeClasses = componentVariants.button[variant].sizes[size as keyof typeof componentVariants.button[typeof variant].sizes];
  return `${baseClasses} ${sizeClasses} ${borders.radius.md}`;
};

export const getCardClasses = (variant: 'default' | 'category' | 'martyr', withHover: boolean = true) => {
  const baseClasses = componentVariants.card[variant].base;
  const hoverClasses = withHover ? componentVariants.card[variant].hover : '';
  return `${baseClasses} ${hoverClasses}`;
};

export const getInputClasses = (size: keyof typeof componentVariants.input.default.sizes) => {
  const baseClasses = componentVariants.input.default.base;
  const sizeClasses = componentVariants.input.default.sizes[size];
  return `${baseClasses} ${sizeClasses}`;
};

// Focus Ring Utilities
export const focusRing = {
  emerald: 'focus:ring-4 focus:ring-emerald-500/50 focus:outline-none',
  white: 'focus:ring-4 focus:ring-white/50 focus:outline-none',
  blue: 'focus:ring-4 focus:ring-blue-500/50 focus:outline-none',
};