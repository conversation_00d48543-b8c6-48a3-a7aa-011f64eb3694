import { useEffect, useCallback, useState } from 'react';
import type Map from 'ol/Map';
import type { MartyrMapData } from '../types/mapTypes';

interface UseMapEventsOptions {
  map: Map | null;
  onMarkerClick?: (martyr: MartyrMapData) => void;
}

export const useMapEvents = ({ map, onMarkerClick }: UseMapEventsOptions) => {
  const [selectedMartyr, setSelectedMartyr] = useState<MartyrMapData | null>(null);
  const [popupPosition, setPopupPosition] = useState<[number, number] | null>(null);

  const handleMapClick = useCallback((event: any) => {
    if (!map) return;

    const feature = map.forEachFeatureAtPixel(event.pixel, (feature) => feature);
    
    if (feature) {
      const properties = feature.getProperties();
      const martyr = properties as MartyrMapData;
      
      if (martyr) {
        setSelectedMartyr(martyr);
        setPopupPosition([martyr.longitude, martyr.latitude]);
        
        if (onMarkerClick) {
          onMarker<PERSON>lick(martyr);
        }
      }
    } else {
      // Clicked on empty space - close popup
      setSelectedMartyr(null);
      setPopupPosition(null);
    }
  }, [map, onMarkerClick]);

  const handleKeyboardNavigation = useCallback((event: KeyboardEvent) => {
    if (!map) return;

    const view = map.getView();
    const currentCenter = view.getCenter();
    const currentZoom = view.getZoom();
    
    if (!currentCenter || currentZoom === undefined) return;

    const panDistance = 100; // pixels
    const resolution = view.getResolution() || 1;
    const deltaX = panDistance * resolution;
    const deltaY = panDistance * resolution;

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        view.setCenter([currentCenter[0], currentCenter[1] + deltaY]);
        break;
      case 'ArrowDown':
        event.preventDefault();
        view.setCenter([currentCenter[0], currentCenter[1] - deltaY]);
        break;
      case 'ArrowLeft':
        event.preventDefault();
        view.setCenter([currentCenter[0] - deltaX, currentCenter[1]]);
        break;
      case 'ArrowRight':
        event.preventDefault();
        view.setCenter([currentCenter[0] + deltaX, currentCenter[1]]);
        break;
      case '+':
      case '=':
        event.preventDefault();
        view.setZoom(Math.min(currentZoom + 1, 18));
        break;
      case '-':
        event.preventDefault();
        view.setZoom(Math.max(currentZoom - 1, 2));
        break;
      case 'Escape':
        event.preventDefault();
        setSelectedMartyr(null);
        setPopupPosition(null);
        break;
    }
  }, [map]);

  const closePopup = useCallback(() => {
    setSelectedMartyr(null);
    setPopupPosition(null);
  }, []);

  // Set up event listeners
  useEffect(() => {
    if (!map) return;

    const mapElement = map.getTargetElement();
    if (!mapElement) return;

    // Add click event listener
    map.on('click', handleMapClick);

    // Add keyboard event listener
    mapElement.addEventListener('keydown', handleKeyboardNavigation);

    // Cleanup
    return () => {
      map.un('click', handleMapClick);
      mapElement.removeEventListener('keydown', handleKeyboardNavigation);
    };
  }, [map, handleMapClick, handleKeyboardNavigation]);

  return {
    selectedMartyr,
    popupPosition,
    closePopup,
  };
};