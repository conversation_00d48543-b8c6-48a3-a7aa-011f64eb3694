import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Star, Moon, Heart, Shield, BookOpen, Archive, Globe, Users, Phone, Mail } from 'lucide-react';
import { QuranVerseRotator } from './animations';
import { footerVerses } from '../lib/quranVerses';
import PrivacyEthicsModal from './PrivacyEthicsModal';

export default function Footer() {
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  return (
    <>
      <footer className="bg-gradient-to-br from-slate-900 via-emerald-900 to-teal-900 text-white relative overflow-hidden">
      {/* Simplified Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.08'%3E%3Cpath d='M30 30c0-8.284-6.716-15-15-15s-15 6.716-15 15 6.716 15 15 15 15-6.716 15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
        {/* Main Footer Content - Enhanced Mobile Layout */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-8">
          {/* Brand & Mission - Full width on mobile */}
          <div className="sm:col-span-2 lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                <div className="relative">
                  <Star className="w-6 h-6 text-amber-200 absolute -top-1 -left-1" fill="currentColor" />
                  <Moon className="w-4 h-4 text-white" fill="currentColor" />
                </div>
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">Martyrs Archive</h3>
                <p className="text-sm text-emerald-300" dir="rtl">أرشيف الشهداء</p>
              </div>
            </div>
            
            <p className="text-slate-300 mb-6 leading-relaxed text-sm sm:text-base">
              A comprehensive educational platform documenting martyrs from Nigeria, specifically focusing on
              the Islamic Movement of Nigeria (IMN) and those who sacrificed for justice and faith.
            </p>
            
            {/* Newsletter Signup */}
            <div className="bg-emerald-800/40 rounded-xl p-3 sm:p-4 border border-emerald-700/30 mb-4 sm:mb-6">
              <QuranVerseRotator
                verses={footerVerses.slice(0, 2)}
                interval={15000}
                showTypewriter={false}
                verseClassName="text-emerald-200 text-xs sm:text-sm leading-relaxed"
                sourceClassName="text-amber-300 text-xs"
                pauseOnHover={true}
              />
            </div>

          </div>

          {/* Archive Navigation - Mobile Optimized */}
          <div className="order-2 sm:order-none">
            <h3 className="text-lg font-semibold mb-3 sm:mb-4 text-white flex items-center">
              <Archive className="w-5 h-5 mr-2 text-emerald-400" />
              Archive
            </h3>
            <ul className="space-y-2 sm:space-y-3">
              <li>
                <Link to="/search" className="text-slate-300 hover:text-emerald-400 transition-colors duration-200 text-sm block py-1">
                  Search All Martyrs
                </Link>
              </li>
              <li>
                <Link to="/categories/Shi'a IMN Martyrs" className="text-slate-300 hover:text-emerald-400 transition-colors duration-200 text-sm block py-1">
                  IMN Martyrs
                </Link>
              </li>
              <li>
                <Link to="/categories/Marhum" className="text-slate-300 hover:text-emerald-400 transition-colors duration-200 text-sm block py-1">
                  Marhum
                </Link>
              </li>

              <li>
                <Link to="/timeline" className="text-slate-300 hover:text-emerald-400 transition-colors duration-200 text-sm block py-1">
                  Historical Timeline
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources & Information - Mobile Optimized */}
          <div className="order-3 sm:order-none">
            <h3 className="text-lg font-semibold mb-3 sm:mb-4 text-white flex items-center">
              <BookOpen className="w-5 h-5 mr-2 text-blue-400" />
              Resources
            </h3>
            <ul className="space-y-2 sm:space-y-3 text-sm">
              <li>
                <Link to="/about" className="text-slate-300 hover:text-emerald-400 transition-colors duration-200 block py-1">
                  About the Archive
                </Link>
              </li>
              <li>
                <Link to="/methodology" className="text-slate-300 hover:text-emerald-400 transition-colors duration-200 block py-1">
                  Research Methodology
                </Link>
              </li>

              <li>
                <Link to="/educational" className="text-slate-300 hover:text-emerald-400 transition-colors duration-200 block py-1">
                  Educational Resources
                </Link>
              </li>
              <li>
                <button
                  onClick={() => setShowPrivacyModal(true)}
                  className="text-slate-300 hover:text-emerald-400 transition-colors duration-200 block py-1 text-left"
                >
                  Privacy & Ethics
                </button>
              </li>
            </ul>
            
            {/* Mission Values - Compact Mobile Layout */}
            <div className="mt-4 sm:mt-6 grid grid-cols-2 gap-1.5 sm:gap-2 text-xs">
              <div className="flex items-center text-emerald-300 py-1">
                <Heart className="w-3 h-3 mr-1 text-red-400" />
                <span>Respectful</span>
              </div>
              <div className="flex items-center text-emerald-300 py-1">
                <Shield className="w-3 h-3 mr-1 text-blue-400" />
                <span>Educational</span>
              </div>
              <div className="flex items-center text-emerald-300 py-1">
                <Users className="w-3 h-3 mr-1 text-purple-400" />
                <span>Community</span>
              </div>
              <div className="flex items-center text-emerald-300 py-1">
                <Globe className="w-3 h-3 mr-1 text-teal-400" />
                <span>Global</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar - Mobile Optimized */}
        <div className="border-t border-slate-700/50 pt-4 sm:pt-6">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
            <div className="text-center sm:text-left order-2 sm:order-1">
              <p className="text-slate-400 text-xs sm:text-sm">
                © 2024 Martyrs Archive. Educational & non-commercial use only.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 text-center order-1 sm:order-2">
              <span className="text-slate-400 text-xs sm:text-sm">Nigeria IMN Archive</span>
              <div className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
                <span className="text-emerald-300 text-xs sm:text-sm font-medium" dir="rtl">
                  رحمة الله عليهم
                </span>
                <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>

    {/* Privacy & Ethics Modal */}
    <PrivacyEthicsModal
      isOpen={showPrivacyModal}
      onClose={() => setShowPrivacyModal(false)}
    />
  </>
  );
}
