import { api, Query } from "encore.dev/api";
import { martyrsDB } from "./db";

interface GetTimelineParams {
  subCategories?: Query<string>;
  regions?: Query<string>;
  periods?: Query<string>;
  startDate?: Query<string>;
  endDate?: Query<string>;
}

interface TimelineEventWithMartyr {
  id: number;
  martyrId: number;
  martyrName: string;
  martyrSlug: string;
  eventDate: string;
  description: string;
  imageUrl?: string;
  subCategories: string[];
  region?: string;
}

interface GetTimelineResponse {
  events: TimelineEventWithMartyr[];
}

// Retrieves timeline events for the global timeline page.
export const getTimeline = api<GetTimelineParams, GetTimelineResponse>(
  { expose: true, method: "GET", path: "/timeline" },
  async (params) => {
    const {
      subCategories = "",
      regions = "",
      periods = "",
      startDate,
      endDate
    } = params;

    let whereClause = "WHERE 1=1";
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Filter by sub-categories
    if (subCategories) {
      const subCatArray = subCategories.split(",").filter(Boolean);
      if (subCatArray.length > 0) {
        whereClause += ` AND m.sub_categories && $${paramIndex}`;
        queryParams.push(subCatArray);
        paramIndex++;
      }
    }

    // Filter by regions
    if (regions) {
      const regionArray = regions.split(",").filter(Boolean);
      if (regionArray.length > 0) {
        whereClause += ` AND m.region = ANY($${paramIndex})`;
        queryParams.push(regionArray);
        paramIndex++;
      }
    }

    // Filter by periods
    if (periods) {
      const periodArray = periods.split(",").filter(Boolean);
      if (periodArray.length > 0) {
        whereClause += ` AND m.period = ANY($${paramIndex})`;
        queryParams.push(periodArray);
        paramIndex++;
      }
    }

    // Filter by date range
    if (startDate) {
      whereClause += ` AND te.event_date >= $${paramIndex}`;
      queryParams.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereClause += ` AND te.event_date <= $${paramIndex}`;
      queryParams.push(endDate);
      paramIndex++;
    }

    const timelineQuery = `
      SELECT 
        te.id,
        te.martyr_id,
        m.name as martyr_name,
        m.slug as martyr_slug,
        te.event_date,
        te.description,
        te.image_url,
        m.sub_categories,
        m.region
      FROM timeline_events te
      JOIN martyrs m ON te.martyr_id = m.id
      ${whereClause}
      ORDER BY te.event_date DESC
      LIMIT 100
    `;

    const events = await martyrsDB.rawQueryAll<{
      id: number;
      martyr_id: number;
      martyr_name: string;
      martyr_slug: string;
      event_date: string;
      description: string;
      image_url?: string;
      sub_categories: string[];
      region?: string;
    }>(timelineQuery, ...queryParams);

    return {
      events: events.map(event => ({
        id: event.id,
        martyrId: event.martyr_id,
        martyrName: event.martyr_name,
        martyrSlug: event.martyr_slug,
        eventDate: event.event_date,
        description: event.description,
        imageUrl: event.image_url,
        subCategories: event.sub_categories || [],
        region: event.region
      }))
    };
  }
);
