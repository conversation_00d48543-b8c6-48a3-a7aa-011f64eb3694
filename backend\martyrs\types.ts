export interface Martyr {
  id: number;
  name: string;
  slug: string;
  birthDate?: string;
  birthPlace?: string;
  deathDate?: string;
  deathPlace?: string;
  martyrdomCause?: string;
  martyrdomContext?: string;
  bio: string;
  subCategories: string[];
  region?: string;
  period?: string;
  quotes: string[];
  familyInfo?: string;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  latitude?: number | null;
  longitude?: number | null;
}

export interface MartyrImage {
  id: number;
  martyrId: number;
  url: string;
  caption?: string;
  credit?: string;
  isProfileImage: boolean;
  createdAt: string;
}

export interface TimelineEvent {
  id: number;
  martyrId: number;
  eventDate: string;
  description: string;
  imageUrl?: string;
  createdAt: string;
}

export interface MartyrProfile extends Martyr {
  images: MartyrImage[];
  timelineEvents: TimelineEvent[];
}

export interface SearchFilters {
  subCategories?: string[];
  regions?: string[];
  periods?: string[];
  causes?: string[];
}

export interface SearchResult {
  martyrs: MartyrCard[];
  total: number;
}

export interface MartyrCard {
  id: number;
  name: string;
  slug: string;
  bio: string;
  subCategories: string[];
  region?: string;
  profileImage?: string;
  viewCount?: number;
}
