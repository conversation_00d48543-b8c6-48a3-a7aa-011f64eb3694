---
trigger: always_on
alwaysApply: true
---
# Martyr Website Project Rules & AI Assistant Instructions

## 1. Project Overview & Mission

### Core Purpose
The **Martyr Website** is a cultural and religious memorial platform dedicated to documenting and honoring martyrs from Nigeria, specifically focusing on the Islamic Movement of Nigeria (IMN) martyrs. This is not just a database - it's a sacred digital archive that requires respectful, accurate, and culturally sensitive handling.

### Cultural & Religious Sensitivity Rules
- **MANDATORY**: Always approach this project with the utmost respect and cultural sensitivity
- **REQUIRED**: Understand that martyrs (<PERSON><PERSON><PERSON>) hold sacred significance in Islamic tradition
- **CRITICAL**: Never suggest content or features that could be seen as disrespectful to the memory of the deceased
- **ESSENTIAL**: Maintain historical accuracy and verify information when making content suggestions

## 2. Technology Stack & Architecture

### Backend: Encore.dev Framework
- **Framework**: Encore.dev 1.49.1+ (Modern TypeScript backend platform)
- **Architecture**: Service-Oriented Architecture (SOA) with 3 core services:
  - `auth`: Admin authentication and JWT token management
  - `martyrs`: Core CRUD operations, search, and data management
  - `upload`: File/image upload and cloud storage management
- **Database**: PostgreSQL with SQL migrations
- **API Generation**: Automatic type-safe API generation with Encore.dev
- **Authentication**: JWT-based admin authentication with role-based access control

### Frontend: Modern React Stack
- **Framework**: React 19.1.1+ with TypeScript
- **Build Tool**: Vite 6.3.5+
- **Styling**: Tailwind CSS 4.1.12+ with custom design tokens
- **UI Components**: Radix UI primitives with custom components
- **Routing**: React Router DOM 7.8.1+
- **State Management**: TanStack Query (React Query) 5.85.3+
- **Animations**: Framer Motion 12.23.12+
- **Maps**: React Leaflet 5.0.0+ for geographic data
- **Rich Text**: TipTap 3.3.0+ for content editing

### Development Environment
- **Package Manager**: npm 9.8.1 (strict requirement)
- **Node.js**: Compatible with npm 9.8.1
- **Workspace**: npm workspaces with `backend/` and `frontend/` packages
- **Local Development**: Encore dev server on port 4000, Vite on port 5173

## 3. Mandatory Research & Verification Requirements

### CRITICAL: Always Use Exa Tools for Implementation
Before implementing any new feature, fix, or content modification, you **MUST**:

1. **Research Current Best Practices**:
   ```
   Use mcp_exa_web_search_exa to research:
   - Latest security practices for the technologies used
   - Current accessibility standards (WCAG 2.1+)
   - Performance optimization techniques
   - Modern UI/UX patterns for memorial websites
   ```

2. **Verify Technical Implementation**:
   ```
   Use mcp_exa_web_search_exa to verify:
   - Encore.dev latest documentation and best practices
   - React/TypeScript current patterns and recommendations
   - Database schema best practices for the data types involved
   - Security considerations for admin authentication
   ```

3. **Cultural and Religious Research**:
   ```
   For any content-related work, use mcp_exa_web_search_exa to research:
   - Islamic traditions regarding martyrdom and memorial practices
   - Cultural sensitivities for the regions/communities involved
   - Historical accuracy requirements for the period/events
   - Appropriate language and terminology usage
   ```

4. **Accessibility and Compliance**:
   ```
   Use mcp_exa_web_search_exa to ensure:
   - WCAG 2.1 AA compliance for new features
   - Screen reader compatibility for memorial content
   - Multi-language support considerations (Arabic, English)
   - Performance standards for low-bandwidth regions
   ```

### Deep Research Requirements
For complex features or significant changes, use `mcp_exa_deep_researcher_start` and `mcp_exa_deep_researcher_check` to conduct comprehensive research on:
- Security implications of new backend endpoints
- Performance impact of frontend changes
- Cultural appropriateness of new features
- Historical accuracy of content modifications

## 4. File Structure & Code Organization

### Backend Structure (`backend/`)
```
backend/
├── auth/                    # Authentication service
│   ├── auth.ts             # Auth handler and middleware
│   ├── login.ts            # Login endpoint
│   └── encore.service.ts   # Service registration
├── martyrs/                # Core martyrs management service
│   ├── migrations/         # SQL schema migrations
│   ├── admin_*.ts         # Admin-only endpoints (CRUD operations)
│   ├── get_*.ts          # Public read endpoints
│   ├── search.ts         # Search functionality
│   ├── db.ts             # Database connection
│   └── types.ts          # TypeScript interfaces
├── upload/                 # File upload service
│   ├── upload.ts         # Upload endpoints
│   ├── storage.ts        # Storage abstraction layer
│   └── types.ts          # Upload-related types
└── encore.gen/            # Auto-generated API clients (DO NOT EDIT)
```

### Frontend Structure (`frontend/`)
```
frontend/
├── components/
│   ├── ui/               # Reusable UI components (button, card, etc.)
│   ├── animations/       # Animation components (Quran rotator, typewriter)
│   ├── Header.tsx        # Main navigation with search
│   ├── Footer.tsx        # Site footer
│   ├── MartyrCard.tsx    # Martyr display card
│   ├── ImageManager.tsx  # Admin image management component
│   ├── ImageUploadForm.tsx # Admin image upload with metadata
│   ├── FileUpload.tsx    # Generic file upload component
│   ├── RichTextEditor.tsx # TipTap rich text editor
│   └── [Feature]*.tsx   # Feature-specific components
├── pages/                # Route components
│   ├── HomePage.tsx      # Landing page with featured martyrs
│   ├── SearchPage.tsx    # Search interface
│   ├── MartyrProfilePage.tsx  # Individual martyr details
│   ├── CategoryPage.tsx  # Category listing page
│   ├── TimelinePage.tsx  # Global timeline page
│   ├── AboutPage.tsx     # About page
│   ├── ContactPage.tsx   # Contact page
│   └── Admin Routes/     # Admin-specific pages (protected)
│       ├── AdminLoginPage.tsx        # Admin authentication
│       ├── AdminDashboardPage.tsx    # Main admin dashboard with analytics
│       ├── AdminMartyrFormPage.tsx   # Create/edit martyr forms
│       └── [AdminFeature]Page.tsx    # Other admin functionality
├── hooks/                # Custom React hooks
│   └── useScrollAnimation.ts # Scroll-based animations
├── lib/                  # Utility libraries
│   ├── utils.ts          # General utilities
│   ├── quranVerses.ts    # Quran verse content
│   ├── designTokens.ts   # Design system tokens
│   ├── animationPatterns.ts # Animation utilities
│   ├── seoMeta.ts        # SEO metadata helpers
│   ├── structuredData.ts # JSON-LD schema generation
│   └── [Feature]Utils.ts # Feature-specific utilities
└── styles/               # CSS files
    ├── index.css         # Global styles
    ├── animations.css    # Animation definitions
    ├── critical.css      # Above-the-fold CSS
    └── prose.css         # Rich text content styling
```

## 5. Database Schema & Data Rules

### Core Tables
1. **martyrs**: Main martyr records with biographical data
2. **martyr_images**: Associated images with profile image designation
3. **timeline_events**: Historical timeline events for martyrs
4. **file_uploads**: File upload metadata and tracking

### Data Integrity Rules
- **REQUIRED**: All martyr names must be respectfully formatted
- **MANDATORY**: Dates must be historically accurate and verified
- **CRITICAL**: Geographic data (latitude/longitude) must be precise
- **ESSENTIAL**: Sub-categories must follow the established taxonomy
- **IMPORTANT**: All user-generated content must be sanitized and verified

### Field Requirements
```sql
-- Core required fields for martyrs
name: TEXT NOT NULL          -- Full respectful name
slug: TEXT UNIQUE NOT NULL   -- URL-safe identifier
bio: TEXT NOT NULL           -- Biographical information
sub_categories: TEXT[]       -- Classification tags
created_at: TIMESTAMP        -- Record creation
updated_at: TIMESTAMP        -- Last modification
```

## 6. Authentication & Security Requirements

### Admin Authentication System
- **JWT Token Authentication**: Simple token-based authentication for admin routes using Bearer tokens
- **Role-Based Access Control (RBAC)**: Only `admin` role can access CRUD operations
- **Session Management**: Tokens stored in localStorage with automatic cleanup on logout
- **Secret Management**: Use Encore secret management (`AdminSecret`) or `DEV_ADMIN_SECRET` fallback
- **Protected Routes**: All admin endpoints (`/admin/*`) require authentication
- **Demo Credentials**: Email: `<EMAIL>`, Password: `Admin@2025!`

### Admin Dashboard Features
- **Comprehensive Dashboard**: Analytics, martyrs management, content health monitoring
- **Data Visualization**: Charts for views over time, martyrs by category (using Recharts)
- **Content Health Scoring**: Automated scoring based on biography length and profile images
- **Search & Pagination**: Real-time search with debouncing and paginated results
- **Bulk Operations**: Mass operations on martyr records

### Admin CRUD Operations
1. **Martyr Management**:
   - Create/Update/Delete martyr profiles
   - Rich text editing for biographies and context
   - Geographic data (latitude/longitude) support
   - Category and quote management
   - Timeline event management

2. **Image Management**:
   - Multiple image upload with drag-and-drop
   - Profile image designation
   - Image metadata (caption, credit)
   - Signed URL uploads for large files
   - Cloud storage integration

3. **Analytics & Monitoring**:
   - View statistics and trends
   - Content health monitoring
   - User activity tracking
   - Performance metrics

### Security Implementation
- **JWT Validation**: All admin endpoints validate JWT tokens with role checking
- **Input Validation**: Comprehensive validation using TypeScript interfaces
- **File Upload Security**: 
  - Type restrictions (JPEG, PNG, WebP, GIF only)
  - Size limits (10MB maximum)
  - Signed URL uploads for secure file handling
  - Metadata sanitization
- **SQL Injection Prevention**: Parameterized queries using Encore's SQL templates
- **XSS Prevention**: DOMPurify for content sanitization
- **CSRF Protection**: Bearer token authentication prevents CSRF attacks
- **Route Protection**: Automatic redirection for unauthenticated access attempts

## 7. UI/UX Design Principles

### Visual Design Guidelines
- **Color Palette**: Emerald/teal gradient themes with Islamic geometric patterns
- **Typography**: Respectful, readable fonts with Arabic text support
- **Cultural Elements**: Islamic patterns and motifs used tastefully
- **Accessibility**: WCAG 2.1 AA compliance required

### Component Design Rules
- **Consistency**: Follow established design system in `lib/designTokens.ts`
- **Responsiveness**: Mobile-first design approach
- **Performance**: Optimize for low-bandwidth regions
- **Animations**: Smooth, respectful animations that enhance rather than distract

### Islamic Cultural Elements
- **Quran Verses**: Use appropriate verses with proper citation
- **Arabic Text**: Proper RTL support and beautiful Arabic typography
- **Color Symbolism**: Use colors that respect Islamic cultural significance
- **Pattern Usage**: Incorporate traditional Islamic geometric patterns tastefully

## 8. Content & Language Requirements

### Content Guidelines
- **Accuracy**: All historical information must be verified
- **Respectful Language**: Use dignified, respectful terminology
- **Cultural Sensitivity**: Understand regional and cultural contexts
- **Arabic Support**: Proper handling of Arabic names and text
- **Translation**: Consider multi-language support requirements

### Terminology Standards
- Use "Martyr" (Shahid/Shaheed) respectfully
- Proper capitalization of religious terms
- Accurate historical period references
- Respectful family and personal information handling

## 9. Performance & Optimization Rules

### Frontend Performance
- **Lazy Loading**: Implement for images and non-critical components
- **Code Splitting**: Route-based code splitting with React.lazy
- **Image Optimization**: Proper image sizing and compression
- **Caching**: Implement appropriate caching strategies
- **Bundle Size**: Monitor and optimize bundle sizes

### Backend Performance
- **Database Indexing**: Proper indexes on frequently queried fields
- **Query Optimization**: Efficient SQL queries with pagination
- **Caching**: Implement Redis or similar for frequently accessed data
- **Rate Limiting**: Implement to prevent abuse

## 10. Testing & Quality Assurance

### Testing Requirements
- **Unit Tests**: Required for all business logic functions
- **Integration Tests**: Required for API endpoints
- **E2E Tests**: Required for critical user journeys
- **Accessibility Testing**: Required for all user-facing features
- **Performance Testing**: Required for all new features

### Quality Gates
- **Type Safety**: Full TypeScript coverage with strict mode
- **Linting**: ESLint and Prettier compliance
- **Security Scanning**: Regular security audits
- **Performance Budgets**: Defined performance metrics

## 11. Deployment & Environment Management

### Environment Configuration
- **Development**: Local Encore dev server with file storage simulation
- **Staging**: Encore Cloud staging environment
- **Production**: Encore Cloud production with real cloud storage

### Deployment Rules
- **Git Workflow**: Feature branches with PR reviews required
- **CI/CD**: Automated testing and deployment pipelines
- **Secret Management**: Proper secret handling across environments
- **Database Migrations**: Careful migration planning and rollback strategies

## 12. API Design & Integration Standards

### API Endpoint Patterns
```typescript
// Public endpoints (no auth required)
GET /martyrs/:slug              # Get martyr profile
GET /search                     # Search martyrs
GET /categories/:category       # Get category martyrs
GET /timeline                   # Get timeline events
GET /featured                   # Get featured martyrs
GET /filters                    # Get search filters
GET /martyrs/on-this-day        # Get martyrs for current date
GET /martyrs/map-data           # Get martyrs with location data
GET /martyrs/:slug/related      # Get related martyrs

// Admin endpoints (auth required)
# Martyr Management
POST /admin/martyrs             # Create martyr
GET /admin/martyrs              # List all martyrs (admin)
PUT /admin/martyrs/:id          # Update martyr
DELETE /admin/martyrs/:id       # Delete martyr

# Image Management
POST /admin/martyrs/images/upload-url  # Get signed upload URL
POST /admin/martyrs/images      # Add image to martyr
DELETE /admin/martyrs/images/:id # Delete image
POST /admin/martyrs/images/:id/profile # Set as profile image

# Timeline Management
POST /admin/martyrs/timeline    # Add timeline event
DELETE /admin/martyrs/timeline/:id # Delete timeline event

# File Upload System
POST /admin/upload/signed-url   # Generate signed upload URL
POST /admin/upload/direct       # Direct file upload (small files)
GET /admin/upload/files         # List uploaded files
DELETE /admin/upload/file       # Delete uploaded file

# Analytics & Monitoring
GET /admin/charts/views         # Get views over time data

# Authentication
POST /admin/login               # Admin login endpoint
```

### Response Format Standards
```typescript
// Success responses
{ 
  data: T,                      # Actual data
  success: true,                # Success flag
  message?: string              # Optional message
}

// Error responses
{
  error: string,                # Error message
  code: ErrorCode,              # Structured error code
  details?: any                 # Additional error details
}
```

## 13. Accessibility & Internationalization

### Accessibility Requirements
- **WCAG 2.1 AA**: Full compliance required
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Keyboard Navigation**: Full keyboard accessibility
- **Color Contrast**: Minimum 4.5:1 contrast ratios
- **Focus Management**: Proper focus handling in SPAs

### Internationalization
- **Arabic Support**: RTL text direction and proper typography
- **English**: Primary language with proper fallbacks
- **Date Formats**: Culturally appropriate date representations
- **Number Formats**: Localized number formatting

## 14. Error Handling & Logging

### Error Handling Patterns
```typescript
// Frontend error boundaries
<ErrorBoundary fallback={<ErrorFallback />}>
  <Component />
</ErrorBoundary>

// Backend error responses
throw APIError.notFound("Resource not found");
throw APIError.invalidArgument("Invalid input data");
throw APIError.permissionDenied("Admin access required");
```

### Logging Requirements
- **Structured Logging**: JSON format with consistent fields
- **Error Tracking**: Comprehensive error reporting
- **Audit Logging**: Track all admin actions
- **Performance Monitoring**: Monitor API response times

## 15. Documentation Standards

### Code Documentation
- **TypeScript**: Comprehensive type definitions with JSDoc comments
- **API Documentation**: OpenAPI/Swagger documentation for all endpoints
- **Component Documentation**: Storybook or similar for UI components
- **Setup Documentation**: Clear development setup instructions

### Content Documentation
- **Historical Sources**: Document sources for historical claims
- **Image Attribution**: Proper credit for all images used
- **Content Guidelines**: Editorial guidelines for content creators
- **Translation Notes**: Documentation for multi-language content

## 16. AI Assistant Implementation Guidelines

### Before Starting Any Task

1. **Research Phase** (MANDATORY):
   ```
   - Use mcp_exa_web_search_exa to research current best practices
   - Use mcp_exa_deep_researcher_start for complex features
   - Verify cultural appropriateness for content changes
   - Check latest security recommendations
   ```

2. **Planning Phase**:
   ```
   - Create detailed task breakdown
   - Identify potential cultural sensitivity issues
   - Plan testing approach
   - Consider accessibility implications
   ```

3. **Implementation Phase**:
   ```
   - Follow established code patterns
   - Maintain type safety throughout
   - Implement proper error handling
   - Add comprehensive logging
   ```

4. **Validation Phase**:
   ```
   - Test thoroughly on multiple devices
   - Validate accessibility compliance
   - Verify cultural appropriateness
   - Check performance impact
   ```

### Common Task Patterns

#### Adding New Martyr Data
1. Research historical accuracy of the information
2. Verify proper spelling of names and places
3. Ensure respectful language usage
4. Add proper categorization and metadata
5. Include source attribution where possible

#### UI/UX Improvements
1. Research current accessibility standards
2. Verify design consistency with Islamic cultural elements
3. Test on various devices and screen readers
4. Ensure proper color contrast and typography
5. Validate cultural appropriateness of visual elements

#### Backend API Changes
1. Research security best practices for the change type
2. Ensure proper authentication and authorization
3. Implement comprehensive input validation
4. Add proper error handling and logging
5. Test with realistic data volumes

#### Performance Optimizations
1. Research current performance best practices
2. Measure baseline performance before changes
3. Implement optimizations incrementally
4. Test with various network conditions
5. Monitor for any regressions

### Prohibited Actions
- **NEVER** implement features that could be disrespectful to martyrs
- **NEVER** bypass authentication for admin features
- **NEVER** modify historical data without proper verification
- **NEVER** implement features without cultural sensitivity research
- **NEVER** ignore accessibility requirements
- **NEVER** deploy without proper testing

### Required Confirmations
Before implementing significant changes, confirm:
- [ ] Cultural appropriateness verified through research
- [ ] Historical accuracy confirmed (if applicable)
- [ ] Accessibility standards will be maintained
- [ ] Security implications understood and addressed
- [ ] Performance impact assessed
- [ ] Testing plan defined and executed

## 18. Admin System Architecture & Implementation

### Admin Frontend Components

#### Core Admin Pages
1. **AdminLoginPage.tsx**:
   - JWT-based authentication form
   - Demo credentials display for development
   - Automatic redirection for authenticated users
   - Token storage in localStorage
   - Comprehensive error handling

2. **AdminDashboardPage.tsx**:
   - Analytics dashboard with multiple chart types (Line, Bar, Area)
   - Martyr management with search and pagination
   - Content health monitoring and scoring
   - Statistics overview (total martyrs, views, published content)
   - Quick actions (view, edit, delete) for each martyr
   - Responsive design with sticky header

3. **AdminMartyrFormPage.tsx**:
   - Comprehensive form for martyr creation/editing
   - Rich text editor integration (TipTap)
   - Dynamic slug generation from names
   - Category and quote management
   - Image upload integration
   - Timeline event management
   - Geographic data input (latitude/longitude)
   - Real-time form validation

#### Specialized Admin Components
1. **ImageManager.tsx**:
   - Grid-based image display
   - Profile image designation
   - Bulk image operations
   - Image metadata display
   - Delete confirmation dialogs

2. **ImageUploadForm.tsx**:
   - Drag-and-drop file upload
   - Multiple file support
   - Real-time upload progress
   - Image preview with metadata forms
   - Caption and credit management
   - Profile image selection

3. **FileUpload.tsx**:
   - Generic file upload component
   - Signed URL upload for large files
   - File type and size validation
   - Progress tracking and error handling
   - Backend connectivity status

### Admin Backend Services

#### Authentication Service (`backend/auth/`)
- **auth.ts**: JWT validation middleware with role checking
- **login.ts**: Admin login endpoint with credential validation
- **Secret Management**: AdminSecret with DEV_ADMIN_SECRET fallback

#### Martyr Management Service (`backend/martyrs/admin_*.ts`)
1. **admin_create.ts**: Create new martyr profiles with validation
2. **admin_update.ts**: Update existing martyr data
3. **admin_delete.ts**: Delete martyrs and associated data
4. **admin_list.ts**: List martyrs with search, pagination, and health scoring
5. **admin_images.ts**: Image management (add, delete, upload URLs)
6. **admin_images_set_profile.ts**: Profile image designation
7. **admin_timeline.ts**: Timeline event management
8. **admin_chart_views.ts**: Analytics data for dashboard charts

#### Upload Service (`backend/upload/`)
1. **upload.ts**: File upload endpoints (signed URL, direct upload, delete, list)
2. **storage.ts**: Storage abstraction layer with development simulation
3. **types.ts**: Upload-related TypeScript interfaces

### Admin Security Model

#### Authentication Flow
1. **Login Process**:
   ```
   User Credentials → Backend Validation → JWT Generation → localStorage Storage
   ```

2. **API Request Authentication**:
   ```
   Frontend Request → Bearer Token Header → JWT Validation → Role Check → API Access
   ```

3. **Route Protection**:
   ```
   Page Access → Token Check → Admin Role Verification → Dashboard Access
   ```

#### Authorization Levels
- **Public**: Read-only access to martyr profiles and search
- **Admin**: Full CRUD access to all resources
- **Future**: Potential for Editor, Moderator roles

### Admin Data Management

#### Content Health Scoring
```typescript
interface ContentHealth {
  bioLength: number;      // 0.5 points for bio > 100 chars
  hasProfileImage: boolean; // 0.5 points for profile image
  totalScore: number;     // 0.0 to 1.0 scale
}
```

#### File Upload Strategy
1. **Small Files (< 10MB)**: Direct base64 upload to `/admin/upload/direct`
2. **Large Files**: Signed URL upload to `/admin/upload/signed-url`
3. **Development Mode**: Simulated storage with mock URLs
4. **Production Mode**: Cloud storage integration (S3-compatible)

#### Database Integration
- **Martyrs Table**: Core biographical data with JSON arrays for categories/quotes
- **Martyr Images**: Linked images with profile designation flag
- **Timeline Events**: Chronological events for martyr stories
- **File Uploads**: Upload metadata and tracking
- **Daily Views**: Analytics data for dashboard charts

### Admin UI/UX Design Principles

#### Dashboard Design
- **Information Hierarchy**: Key metrics at top, detailed data below
- **Progressive Disclosure**: Complex operations in expandable sections
- **Contextual Actions**: Relevant actions near data they affect
- **Responsive Layout**: Mobile-friendly admin interface
- **Loading States**: Skeleton screens and progress indicators
- **Error Handling**: User-friendly error messages with actionable suggestions

#### Form Design
- **Multi-Column Layout**: Efficient use of screen space
- **Progressive Enhancement**: Optional fields clearly marked
- **Real-Time Validation**: Immediate feedback on form inputs
- **Auto-Save**: Draft saving for long forms (future enhancement)
- **Rich Text Editing**: WYSIWYG editor for biographical content

### Admin Performance Optimization

#### Frontend Optimizations
- **React Query**: Efficient data fetching with caching
- **Pagination**: Server-side pagination for large datasets
- **Debounced Search**: Reduced API calls during search
- **Image Lazy Loading**: Improved dashboard performance
- **Bundle Splitting**: Admin routes loaded separately

#### Backend Optimizations
- **Database Indexing**: Optimized queries for admin operations
- **Content Health Caching**: Pre-calculated health scores
- **Bulk Operations**: Efficient multi-record operations
- **File Upload Optimization**: Signed URLs for direct cloud uploads

### Admin Monitoring & Analytics

#### Dashboard Analytics
- **View Trends**: 30-day view history with multiple chart types
- **Content Metrics**: Total martyrs, categories, regions
- **Health Monitoring**: Content completeness scoring
- **User Activity**: Admin action tracking (future enhancement)

#### Error Tracking
- **Upload Failures**: File upload error monitoring
- **Authentication Issues**: Login failure tracking
- **API Errors**: Backend error logging and reporting
- **Performance Metrics**: Dashboard load times and API response times

### Future Admin Enhancements

#### Planned Features
1. **Advanced Role Management**: Editor, Moderator, Super Admin roles
2. **Content Workflow**: Draft, Review, Publish workflow
3. **Audit Logging**: Complete action history tracking
4. **Bulk Import/Export**: CSV/JSON data operations
5. **Advanced Analytics**: User engagement, search trends
6. **Content Scheduling**: Scheduled publication of martyr profiles
7. **Multi-language Support**: Admin interface localization
8. **Advanced Search**: Full-text search with filters
9. **Backup Management**: Automated backup and restore
10. **Integration APIs**: Third-party service integrations

This comprehensive admin system ensures that managing the Martyr Website is both efficient and secure, while providing powerful tools for content creators and administrators to maintain the dignity and accuracy that this memorial platform requires.

## 17. Maintenance & Updates

### Regular Maintenance Tasks
- **Security Updates**: Keep all dependencies updated
- **Performance Monitoring**: Regular performance audits
- **Content Review**: Periodic review of content accuracy
- **Accessibility Audits**: Regular accessibility testing
- **Backup Verification**: Ensure backup systems are working

### Update Procedures
- **Dependency Updates**: Careful testing of all dependency updates
- **Framework Updates**: Staged rollout of framework updates
- **Content Updates**: Proper review process for content changes
- **Security Patches**: Immediate application of security updates

This comprehensive guide ensures that any AI assistant working on this project understands not only the technical requirements but also the cultural, religious, and ethical responsibilities involved in maintaining a memorial platform dedicated to honoring martyrs.