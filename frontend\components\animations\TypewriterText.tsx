import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface TypewriterTextProps {
  text: string;
  speed?: number;
  loop?: boolean;
  onComplete?: () => void;
  className?: string;
  startDelay?: number;
  cursor?: boolean;
  direction?: 'ltr' | 'rtl';
}

const TypewriterText: React.FC<TypewriterTextProps> = ({
  text = '',
  speed = 50,
  loop = false,
  onComplete,
  className = '',
  startDelay = 0,
  cursor = true,
  direction = 'ltr'
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [showCursor, setShowCursor] = useState(true);
  const timeoutRef = useRef<number | null>(null);
  const cursorTimeoutRef = useRef<number | null>(null);

  // Handle cursor blinking
  useEffect(() => {
    if (cursor) {
      const blinkCursor = () => {
        setShowCursor(prev => !prev);
        cursorTimeoutRef.current = window.setTimeout(blinkCursor, 530);
      };
      cursorTimeoutRef.current = window.setTimeout(blinkCursor, 530);
    }

    return () => {
      if (cursorTimeoutRef.current) {
        clearTimeout(cursorTimeoutRef.current);
      }
    };
  }, [cursor]);

  // Handle typing animation
  useEffect(() => {
    if (!text) return;

    const startTyping = () => {
      setIsTyping(true);
      setCurrentIndex(0);
      setDisplayText('');
    };

    if (startDelay > 0) {
      timeoutRef.current = window.setTimeout(startTyping, startDelay);
    } else {
      startTyping();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [text, startDelay]);

  useEffect(() => {
    if (!isTyping || !text) return;

    if (currentIndex < text.length) {
      timeoutRef.current = window.setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);
    } else {
      setIsTyping(false);
      if (onComplete) {
        onComplete();
      }

      if (loop) {
        timeoutRef.current = window.setTimeout(() => {
          setDisplayText('');
          setCurrentIndex(0);
          setIsTyping(true);
        }, 2000);
      }
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentIndex, text, speed, isTyping, loop, onComplete]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (cursorTimeoutRef.current) {
        clearTimeout(cursorTimeoutRef.current);
      }
    };
  }, []);

  return (
    <motion.span
      className={`${className} ${direction === 'rtl' ? 'text-right' : 'text-left'}`}
      dir={direction}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {displayText}
      {cursor && (
        <motion.span
          className={`inline-block w-0.5 h-[1em] bg-current ml-1 ${
            showCursor ? 'opacity-100' : 'opacity-0'
          }`}
          animate={{ opacity: showCursor ? 1 : 0 }}
          transition={{ duration: 0.1 }}
        />
      )}
    </motion.span>
  );
};

export default TypewriterText;