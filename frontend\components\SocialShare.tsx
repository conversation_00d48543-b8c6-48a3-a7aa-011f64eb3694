import React, { useState } from 'react';
import { Share2, Facebook, Twitter, MessageCircle, Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SocialShareProps {
  url: string;
  title: string;
  description?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'dropdown' | 'inline';
}

export const SocialShare: React.FC<SocialShareProps> = ({
  url,
  title,
  description = '',
  className = '',
  size = 'sm',
  variant = 'dropdown'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
    telegram: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const openShareWindow = (shareUrl: string) => {
    const width = 600;
    const height = 400;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;
    
    window.open(
      shareUrl,
      'share',
      `width=${width},height=${height},left=${left},top=${top},toolbar=0,status=0,resizable=1`
    );
  };

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  if (variant === 'inline') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <span className="text-sm text-slate-600 font-medium">Share:</span>
        <div className="flex space-x-1">
          <Button
            size="sm"
            variant="outline"
            className={`${sizeClasses[size]} p-0 border-blue-200 text-blue-600 hover:bg-blue-50`}
            onClick={() => openShareWindow(shareLinks.facebook)}
            aria-label="Share on Facebook"
          >
            <Facebook className={iconSizes[size]} />
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            className={`${sizeClasses[size]} p-0 border-sky-200 text-sky-600 hover:bg-sky-50`}
            onClick={() => openShareWindow(shareLinks.twitter)}
            aria-label="Share on Twitter"
          >
            <Twitter className={iconSizes[size]} />
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            className={`${sizeClasses[size]} p-0 border-green-200 text-green-600 hover:bg-green-50`}
            onClick={() => openShareWindow(shareLinks.whatsapp)}
            aria-label="Share on WhatsApp"
          >
            <MessageCircle className={iconSizes[size]} />
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            className={`${sizeClasses[size]} p-0 border-slate-200 text-slate-600 hover:bg-slate-50`}
            onClick={copyToClipboard}
            aria-label="Copy link"
          >
            {copied ? (
              <Check className={`${iconSizes[size]} text-green-600`} />
            ) : (
              <Copy className={iconSizes[size]} />
            )}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <Button
        size="sm"
        variant="outline"
        className={`${sizeClasses[size]} p-0 border-emerald-200 text-emerald-600 hover:bg-emerald-50 transition-all duration-200`}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
        aria-label="Share martyr story"
        aria-expanded={isOpen}
      >
        <Share2 className={iconSizes[size]} />
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsOpen(false);
            }}
          />
          
          {/* Share dropdown */}
          <div className="absolute bottom-full right-0 mb-2 z-50 bg-white rounded-lg shadow-xl border border-slate-200 overflow-hidden min-w-48">
            <div className="p-3 border-b border-slate-100">
              <h4 className="text-sm font-semibold text-slate-800">Share this story</h4>
              <p className="text-xs text-slate-500 mt-1 line-clamp-2">{title}</p>
            </div>
            
            <div className="p-2 space-y-1">
              <button
                className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-700 hover:bg-blue-50 hover:text-blue-700 rounded-md transition-colors duration-150"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  openShareWindow(shareLinks.facebook);
                  setIsOpen(false);
                }}
              >
                <Facebook className="w-4 h-4 text-blue-600" />
                <span>Share on Facebook</span>
              </button>
              
              <button
                className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-700 hover:bg-sky-50 hover:text-sky-700 rounded-md transition-colors duration-150"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  openShareWindow(shareLinks.twitter);
                  setIsOpen(false);
                }}
              >
                <Twitter className="w-4 h-4 text-sky-600" />
                <span>Share on Twitter</span>
              </button>
              
              <button
                className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-700 hover:bg-green-50 hover:text-green-700 rounded-md transition-colors duration-150"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  openShareWindow(shareLinks.whatsapp);
                  setIsOpen(false);
                }}
              >
                <MessageCircle className="w-4 h-4 text-green-600" />
                <span>Share on WhatsApp</span>
              </button>
              
              <button
                className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-700 hover:bg-slate-50 hover:text-slate-700 rounded-md transition-colors duration-150"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  copyToClipboard();
                  setIsOpen(false);
                }}
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4 text-green-600" />
                    <span className="text-green-600">Copied!</span>
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4 text-slate-600" />
                    <span>Copy link</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SocialShare;