import React, { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, BookOpen, Clock, Users, FileText, Video, Image, ExternalLink } from 'lucide-react';
import { IslamicGeometricPattern, IslamicCalligraphyBorder, IslamicFrameBorder } from '@/components/IslamicPatterns';
import ReadingProgress from '../components/ReadingProgress';
import { motion } from 'framer-motion';
import animationPatterns from '../lib/animationPatterns';
import { getEducationalResourcesPageMeta, injectMetaTags } from '../lib/seoMeta';

export default function EducationalResourcesPage() {
  // SEO metadata injection
  useEffect(() => {
    const seoData = getEducationalResourcesPageMeta();
    injectMetaTags(seoData);

    return () => {
      // Cleanup is handled by the next page's SEO injection
    };
  }, []);

  const resourceCategories = [
    {
      icon: FileText,
      title: "Research Guides",
      description: "Comprehensive guides for researchers studying Nigerian IMN martyrs and Islamic history",
      color: "from-blue-500 to-indigo-500",
      bgColor: "from-blue-50 to-indigo-50",
      borderColor: "border-blue-200",
      resources: [
        { name: "IMN Historical Context Guide", type: "PDF", size: "2.3 MB" },
        { name: "Research Methodology Handbook", type: "PDF", size: "1.8 MB" },
        { name: "Source Verification Checklist", type: "PDF", size: "0.5 MB" }
      ]
    },
    {
      icon: Clock,
      title: "Historical Timelines",
      description: "Interactive and downloadable timelines of key events in Nigerian IMN history",
      color: "from-emerald-500 to-teal-500",
      bgColor: "from-emerald-50 to-teal-50",
      borderColor: "border-emerald-200",
      resources: [
        { name: "IMN Movement Timeline (1996-Present)", type: "Interactive", size: "Web" },
        { name: "Zaria Massacre 2015 Timeline", type: "PDF", size: "3.1 MB" },
        { name: "Key Events Chronology", type: "Excel", size: "0.8 MB" }
      ]
    },
    {
      icon: BookOpen,
      title: "Academic References",
      description: "Scholarly articles, papers, and academic resources on martyrdom and Islamic studies",
      color: "from-purple-500 to-violet-500",
      bgColor: "from-purple-50 to-violet-50",
      borderColor: "border-purple-200",
      resources: [
        { name: "Islamic Concept of Martyrdom", type: "Article", size: "External" },
        { name: "Nigerian Religious Movements Study", type: "Paper", size: "4.2 MB" },
        { name: "Bibliography of IMN Studies", type: "PDF", size: "1.2 MB" }
      ]
    },
    {
      icon: Image,
      title: "Visual Materials",
      description: "Educational infographics, maps, and visual aids for learning and teaching",
      color: "from-amber-500 to-orange-500",
      bgColor: "from-amber-50 to-orange-50",
      borderColor: "border-amber-200",
      resources: [
        { name: "Nigeria IMN Locations Map", type: "PNG", size: "5.4 MB" },
        { name: "Historical Events Infographic", type: "PDF", size: "2.7 MB" },
        { name: "Educational Poster Set", type: "ZIP", size: "12.3 MB" }
      ]
    }
  ];

  const educationalTools = [
    {
      icon: Users,
      title: "For Educators",
      description: "Teaching materials and lesson plans for educators covering Nigerian IMN history and Islamic studies.",
      features: ["Lesson Plans", "Discussion Guides", "Assessment Tools", "Presentation Templates"]
    },
    {
      icon: BookOpen,
      title: "For Researchers",
      description: "Advanced research tools and databases for academic study of martyrdom and religious movements.",
      features: ["Database Access", "Citation Tools", "Research Templates", "Methodology Guides"]
    },
    {
      icon: Video,
      title: "For Students",
      description: "Interactive learning materials and multimedia resources for students at all levels.",
      features: ["Interactive Timelines", "Video Documentaries", "Study Guides", "Quiz Materials"]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden">
      {/* Reading Progress Indicator */}
      <ReadingProgress 
        showPercentage={true}
        showBackToTop={true}
        position="top"
        color="from-emerald-600 to-teal-600"
      />

      {/* Background Islamic Pattern */}
      <div className="absolute inset-0 opacity-5">
        <IslamicGeometricPattern />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl mb-8 shadow-2xl">
            <BookOpen className="w-10 h-10 text-white" />
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-emerald-800 via-teal-700 to-emerald-900 bg-clip-text text-transparent">
            Educational Resources
          </h1>
          
          <IslamicCalligraphyBorder className="max-w-md mx-auto mb-6" color="#10b981" />
          
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive educational materials and learning resources for studying Nigerian IMN martyrs, 
            Islamic history, and the preservation of sacred memory.
          </p>
          
          <div className="text-lg text-emerald-700 mt-4 font-medium" dir="rtl">
            الموارد التعليمية والأكاديمية
          </div>
        </motion.div>

        {/* Resource Categories */}
        <motion.section 
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.8 }}
        >
          <h2 className="text-3xl font-bold text-center mb-12 text-slate-800">Resource Categories</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {resourceCategories.map((category, index) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index, duration: 0.6 }}
                whileHover={{ y: -4, scale: 1.02 }}
              >
                <Card className={`h-full bg-gradient-to-br ${category.bgColor} border-2 ${category.borderColor} shadow-lg hover:shadow-xl transition-all duration-300`}>
                  <CardHeader>
                    <div className={`w-12 h-12 bg-gradient-to-br ${category.color} rounded-xl flex items-center justify-center mb-4`}>
                      <category.icon className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-xl font-bold text-slate-800">{category.title}</CardTitle>
                    <p className="text-slate-700">{category.description}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {category.resources.map((resource, idx) => (
                        <div key={idx} className="flex items-center justify-between p-3 bg-white/60 rounded-lg border border-slate-200/50">
                          <div className="flex items-center space-x-3">
                            <FileText className="w-4 h-4 text-slate-600" />
                            <div>
                              <p className="font-medium text-slate-800 text-sm">{resource.name}</p>
                              <p className="text-xs text-slate-600">{resource.type} • {resource.size}</p>
                            </div>
                          </div>
                          <Button size="sm" variant="outline" className="text-xs">
                            <Download className="w-3 h-3 mr-1" />
                            Get
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Educational Tools */}
        <motion.section 
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <IslamicFrameBorder className="p-8">
            <h2 className="text-3xl font-bold text-center mb-12 text-slate-800">Educational Tools</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {educationalTools.map((tool, index) => (
                <motion.div
                  key={tool.title}
                  className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-emerald-200/60 shadow-lg"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.1 * index, duration: 0.5 }}
                  whileHover={{ y: -4, scale: 1.05 }}
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <tool.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-800 mb-3">{tool.title}</h3>
                  <p className="text-slate-600 mb-4 leading-relaxed">{tool.description}</p>
                  
                  <div className="space-y-2">
                    {tool.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center justify-center text-sm text-emerald-700">
                        <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-2"></div>
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <Button className="mt-4 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700">
                    Access Tools
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </Button>
                </motion.div>
              ))}
            </div>
          </IslamicFrameBorder>
        </motion.section>

        {/* Call to Action */}
        <motion.section 
          className="text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7, duration: 0.8 }}
        >
          <Card className="bg-gradient-to-br from-emerald-50 to-teal-50 border-2 border-emerald-200 shadow-xl">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-emerald-800 mb-4">
                Contribute to Educational Resources
              </h2>
              <p className="text-slate-700 mb-6 max-w-2xl mx-auto">
                Help us expand our educational materials by contributing research, translations, 
                or educational content about Nigerian IMN martyrs and Islamic history.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700">
                  Submit Resources
                </Button>
                <Button variant="outline" className="border-emerald-300 text-emerald-700 hover:bg-emerald-50">
                  Request Materials
                </Button>
              </div>
              
              <div className="text-center pt-6 border-t border-emerald-200 mt-6">
                <p className="text-sm text-slate-600" dir="rtl">
                  بارك الله فيكم على جهودكم التعليمية
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.section>
      </div>
    </div>
  );
}
