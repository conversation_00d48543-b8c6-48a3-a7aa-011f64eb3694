---
trigger: model_decision
description: This document is served as a technical documentation of the project is better using it when implementing something 
---
# Technical Implementation Guide for AI Assistants

## Quick Reference Commands & Patterns

### Development Environment Setup
```bash
# Start development servers
cd backend && encore run                 # Backend on port 4000
cd frontend && npm run dev              # Frontend on port 5173

# Generate API client after backend changes
cd backend && encore gen client --target frontend

# Set up admin secret for local development
./scripts/setup-dev-secret.sh          # Generates secure random secret
encore secret set AdminSecret <value>  # Set via Encore CLI
```

### Common Code Patterns

#### Backend API Endpoint Pattern
```typescript
import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";

// Public endpoint
export const getPublicData = api<RequestType, ResponseType>(
  { expose: true, method: "GET", path: "/public-endpoint" },
  async (params) => {
    // Implementation
  }
);

// Admin-only endpoint
export const adminAction = api<RequestType, ResponseType>(
  { auth: true, expose: true, method: "POST", path: "/admin/action" },
  async (params) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }
    // Implementation
  }
);
```

#### Frontend API Call Pattern
```typescript
import { useQuery, useMutation } from '@tanstack/react-query';
import backend from '~backend/client';

// Query pattern
const { data, isLoading, error } = useQuery({
  queryKey: ['resource', id],
  queryFn: () => backend.martyrs.getResource({ id }),
  staleTime: 5 * 60 * 1000, // 5 minutes
});

// Mutation pattern
const mutation = useMutation({
  mutationFn: (data) => backend.martyrs.createResource(data),
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['resources'] });
  },
});
```

#### Component Structure Pattern
```typescript
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface ComponentProps {
  className?: string;
  // Other props
}

export default function Component({ className, ...props }: ComponentProps) {
  return (
    <Card className={cn("default-classes", className)}>
      <CardHeader>
        <CardTitle>Title</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Content */}
      </CardContent>
    </Card>
  );
}
```

## Database Migration Patterns

### Creating New Migration
```sql
-- File: backend/martyrs/migrations/N_description.up.sql
-- Always increment N and use descriptive names

-- Add new column
ALTER TABLE martyrs ADD COLUMN new_field TEXT;

-- Create new table with proper relationships
CREATE TABLE new_table (
  id BIGSERIAL PRIMARY KEY,
  martyr_id BIGINT REFERENCES martyrs(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_new_table_martyr_id ON new_table(martyr_id);
```

### Database Query Patterns
```typescript
// Simple query
const martyr = await martyrsDB.queryRow<MartyrType>`
  SELECT * FROM martyrs WHERE slug = ${slug}
`;

// Query with joins
const martyrWithImages = await martyrsDB.queryRow<ComplexType>`
  SELECT m.*, mi.url as profile_image
  FROM martyrs m
  LEFT JOIN martyr_images mi ON m.id = mi.martyr_id 
    AND mi.is_profile_image = true
  WHERE m.id = ${id}
`;

// Insert with returning
const newMartyr = await martyrsDB.queryRow<MartyrType>`
  INSERT INTO martyrs (name, slug, bio) 
  VALUES (${name}, ${slug}, ${bio})
  RETURNING *
`;
```

## File Upload Implementation

### Backend Upload Endpoint
```typescript
export const uploadFile = api<UploadRequest, UploadResponse>(
  { auth: true, expose: true, method: "POST", path: "/admin/upload" },
  async (req) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(req.contentType)) {
      throw APIError.invalidArgument("Invalid file type");
    }

    // Upload to storage
    const url = await storage.uploadFile(fileData, filename, contentType);
    
    // Save metadata to database
    await martyrsDB.exec`
      INSERT INTO file_uploads (id, file_url, martyr_id, uploaded_by)
      VALUES (${fileId}, ${url}, ${martyrId}, ${auth.userID})
    `;

    return { success: true, url };
  }
);
```

### Frontend Upload Component
```typescript
const FileUploadComponent = ({ onUpload }: { onUpload: (url: string) => void }) => {
  const uploadMutation = useMutation({
    mutationFn: (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      return backend.upload.uploadFile(formData);
    },
    onSuccess: (data) => onUpload(data.url),
  });

  return (
    <div className="upload-zone">
      <input 
        type="file" 
        onChange={(e) => e.target.files?.[0] && uploadMutation.mutate(e.target.files[0])}
        accept="image/*"
      />
      {uploadMutation.isPending && <div>Uploading...</div>}
    </div>
  );
};
```

## Authentication Integration

### Frontend Auth Context
```typescript
const AuthContext = createContext<{
  token: string | null;
  login: (token: string) => void;
  logout: () => void;
}>({});

// Set up client with auth
const authClient = new Client(Local, {
  auth: () => ({
    authorization: `Bearer ${token}`,
  }),
});
```

### Protected Route Pattern
```typescript
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { token } = useAuth();
  
  if (!token) {
    return <Navigate to="/admin/login" replace />;
  }
  
  return <>{children}</>;
};
```

## Search Implementation

### Backend Search with Filters
```typescript
export const search = api<SearchParams, SearchResult>(
  { expose: true, method: "GET", path: "/search" },
  async ({ query, filters, limit = 20, offset = 0 }) => {
    let whereClause = "WHERE 1=1";
    const params: any[] = [];
    let paramIndex = 1;

    // Text search
    if (query) {
      whereClause += ` AND (name ILIKE $${paramIndex} OR bio ILIKE $${paramIndex})`;
      params.push(`%${query}%`);
      paramIndex++;
    }

    // Category filter
    if (filters.categories?.length) {
      whereClause += ` AND sub_categories && $${paramIndex}`;
      params.push(filters.categories);
      paramIndex++;
    }

    const martyrs = await martyrsDB.rawQueryAll<MartyrCard>(
      `SELECT * FROM martyrs ${whereClause} LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
      ...params, limit, offset
    );

    return { martyrs, total: martyrs.length };
  }
);
```

### Frontend Search with Debouncing
```typescript
const SearchPage = () => {
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => setDebouncedQuery(query), 300);
    return () => clearTimeout(timer);
  }, [query]);

  const { data, isLoading } = useQuery({
    queryKey: ['search', debouncedQuery],
    queryFn: () => backend.martyrs.search({ query: debouncedQuery }),
    enabled: debouncedQuery.length >= 2,
  });

  return (
    <div>
      <input 
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search martyrs..."
      />
      {/* Results */}
    </div>
  );
};
```

## Performance Optimization Patterns

### Frontend Optimization
```typescript
// Lazy loading with React.lazy
const LazyComponent = React.lazy(() => import('./HeavyComponent'));

// Virtual scrolling for large lists
import { FixedSizeList as List } from 'react-window';

// Image optimization
<img 
  src={imageSrc}
  loading="lazy"
  alt={altText}
  className="w-full h-auto"
/>

// Code splitting by route
const HomePage = React.lazy(() => import('./pages/HomePage'));
```

### Backend Optimization
```typescript
// Database indexing
CREATE INDEX CONCURRENTLY idx_martyrs_search ON martyrs USING GIN(to_tsvector('english', name || ' ' || bio));

// Pagination
const martyrs = await martyrsDB.rawQueryAll<Martyr>`
  SELECT * FROM martyrs 
  ORDER BY created_at DESC 
  LIMIT ${limit} OFFSET ${offset}
`;

// Caching frequently accessed data
const cache = new Map<string, any>();
const getCachedData = async (key: string) => {
  if (cache.has(key)) return cache.get(key);
  const data = await fetchData(key);
  cache.set(key, data);
  return data;
};
```

## Error Handling Patterns

### Backend Error Handling
```typescript
try {
  const result = await someOperation();
  return result;
} catch (error) {
  if (error instanceof DatabaseError) {
    throw APIError.internal("Database operation failed");
  }
  if (error instanceof ValidationError) {
    throw APIError.invalidArgument(error.message);
  }
  throw APIError.internal("Unexpected error occurred");
}
```

### Frontend Error Handling
```typescript
// Error boundary
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}

// Query error handling
const { data, error, isError } = useQuery({
  queryKey: ['data'],
  queryFn: fetchData,
  retry: 3,
  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
});

if (isError) {
  return <ErrorMessage error={error} />;
}
```

## Testing Patterns

### Backend Testing
```typescript
// API endpoint test
import { beforeAll, describe, expect, test } from 'vitest';

describe('martyrs API', () => {
  test('should create martyr with valid data', async () => {
    const mockData = {
      name: 'Test Martyr',
      slug: 'test-martyr',
      bio: 'Test biography'
    };

    const result = await backend.martyrs.createMartyr(mockData);
    expect(result.name).toBe(mockData.name);
  });
});
```

### Frontend Testing
```typescript
// Component test
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

test('renders martyr card correctly', () => {
  const martyr = { name: 'Test Martyr', bio: 'Test bio' };
  render(<MartyrCard {...martyr} />, { wrapper: TestWrapper });
  expect(screen.getByText('Test Martyr')).toBeInTheDocument();
});
```

## Accessibility Implementation

### ARIA Labels and Semantic HTML
```typescript
// Proper heading hierarchy
<main role="main">
  <h1>Martyrs Archive</h1>
  <section aria-labelledby="featured-heading">
    <h2 id="featured-heading">Featured Martyrs</h2>
    <ul role="list">
      {martyrs.map(martyr => (
        <li key={martyr.id} role="listitem">
          <article aria-labelledby={`martyr-${martyr.id}`}>
            <h3 id={`martyr-${martyr.id}`}>{martyr.name}</h3>
            <p>{martyr.bio}</p>
          </article>
        </li>
      ))}
    </ul>
  </section>
</main>

// Focus management
const useAutoFocus = (shouldFocus: boolean) => {
  const ref = useRef<HTMLElement>(null);
  
  useEffect(() => {
    if (shouldFocus && ref.current) {
      ref.current.focus();
    }
  }, [shouldFocus]);
  
  return ref;
};
```

## Internationalization Setup

### Arabic Text Support
```css
/* RTL support */
.arabic-text {
  direction: rtl;
  text-align: right;
  font-family: 'Amiri', 'Times New Roman', serif;
}

/* Bilingual layout */
.bilingual-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

@media (max-width: 768px) {
  .bilingual-content {
    grid-template-columns: 1fr;
  }
}
```

### Date and Number Formatting
```typescript
// Locale-aware formatting
const formatDate = (date: Date, locale: string = 'en-US') => {
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};

const formatNumber = (num: number, locale: string = 'en-US') => {
  return new Intl.NumberFormat(locale).format(num);
};
```

## Deployment and DevOps

### Environment Configuration
```typescript
// Environment-specific configuration
const config = {
  development: {
    apiUrl: 'http://localhost:4000',
    storage: 'local',
    logLevel: 'debug'
  },
  production: {
    apiUrl: process.env.ENCORE_API_URL,
    storage: 's3',
    logLevel: 'info'
  }
};
```

### CI/CD Pipeline Considerations
```yaml
# Example GitHub Actions workflow
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          cd backend && npm test
          cd frontend && npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Encore
        run: encore deploy --env production
```

This technical guide provides the concrete implementation patterns and code examples that AI assistants need to work effectively with this project while following the cultural and ethical guidelines established in the main PROJECT_RULES.md file.