import { useEffect, useRef, useState, useCallback } from 'react';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import OSM from 'ol/source/OSM';
import { defaults as defaultControls } from 'ol/control';
import { fromLonLat } from 'ol/proj';
import type { UseMapInstanceOptions } from '../types/mapTypes';

export const useMapInstance = (options: UseMapInstanceOptions) => {
  const { center, zoom, onMapReady } = options;
  const mapRef = useRef<Map | null>(null);
  const mapContainerRef = useRef<HTMLDivElement | null>(null);
  const [isMapReady, setIsMapReady] = useState(false);

  const initializeMap = useCallback(() => {
    if (!mapContainerRef.current || mapRef.current) return;

    // Create base tile layer with OpenStreetMap
    const baseLayer = new TileLayer({
      source: new OSM({
        // Use a respectful attribution
        attributions: [
          '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        ],
      }),
    });

    // Create map instance
    const map = new Map({
      target: mapContainerRef.current,
      layers: [baseLayer],
      view: new View({
        center: fromLonLat(center),
        zoom: zoom,
        minZoom: 2,
        maxZoom: 18,
      }),
      controls: defaultControls({
        attribution: true,
        zoom: true,
        rotate: false,
      }),
    });

    mapRef.current = map;
    setIsMapReady(true);

    // Call onMapReady callback if provided
    if (onMapReady) {
      onMapReady(map);
    }

    // Add keyboard navigation for accessibility
    map.getTargetElement()?.setAttribute('tabindex', '0');
    map.getTargetElement()?.setAttribute('role', 'application');
    map.getTargetElement()?.setAttribute('aria-label', 'Interactive map showing martyr locations');

  }, [center, zoom, onMapReady]);

  const addLayer = useCallback((layer: any) => {
    if (mapRef.current) {
      mapRef.current.addLayer(layer);
    }
  }, []);

  const removeLayer = useCallback((layer: any) => {
    if (mapRef.current) {
      mapRef.current.removeLayer(layer);
    }
  }, []);

  const updateCenter = useCallback((newCenter: [number, number]) => {
    if (mapRef.current) {
      const view = mapRef.current.getView();
      view.setCenter(fromLonLat(newCenter));
    }
  }, []);

  const updateZoom = useCallback((newZoom: number) => {
    if (mapRef.current) {
      const view = mapRef.current.getView();
      view.setZoom(newZoom);
    }
  }, []);

  // Initialize map on mount
  useEffect(() => {
    initializeMap();

    // Cleanup function
    return () => {
      if (mapRef.current) {
        mapRef.current.setTarget(undefined);
        mapRef.current = null;
        setIsMapReady(false);
      }
    };
  }, [initializeMap]);

  // Handle responsive resizing
  useEffect(() => {
    const handleResize = () => {
      if (mapRef.current) {
        mapRef.current.updateSize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    mapRef,
    mapContainerRef,
    isMapReady,
    addLayer,
    removeLayer,
    updateCenter,
    updateZoom,
  };
};