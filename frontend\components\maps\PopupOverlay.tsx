import React from 'react';
import { Link } from 'react-router-dom';
import { X, ExternalLink } from 'lucide-react';
import type { PopupOverlayProps } from './types/mapTypes';
import styles from './styles/map.module.css';

const PopupOverlay: React.FC<PopupOverlayProps> = ({ 
  martyr, 
  position, 
  onClose 
}) => {
  if (!martyr || !position) return null;

  return (
    <div className={styles.mapPopup}>
      <div className={styles.popupHeader}>
        <h3 className={styles.popupTitle}>{martyr.name}</h3>
        <button
          className={styles.popupCloseButton}
          onClick={onClose}
          aria-label="Close popup"
          type="button"
        >
          <X size={16} />
        </button>
      </div>
      
      <div className={styles.popupContent}>
        {martyr.profileImage && (
          <img
            src={martyr.profileImage}
            alt={`${martyr.name} profile`}
            className={styles.popupImage}
            loading="lazy"
          />
        )}
        
        {martyr.subCategories && martyr.subCategories.length > 0 && (
          <div className={styles.popupCategories}>
            {martyr.subCategories.slice(0, 3).map((category, index) => (
              <span key={index} className={styles.popupCategory}>
                {category}
              </span>
            ))}
          </div>
        )}
        
        <Link
          to={`/martyrs/${martyr.slug}`}
          className={styles.popupLink}
          onClick={onClose}
        >
          View Full Profile
          <ExternalLink size={14} style={{ marginLeft: '0.25rem' }} />
        </Link>
      </div>
    </div>
  );
};

export default PopupOverlay;