import React, { useState, useCallback } from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription } from './ui/alert-dialog';
import { Progress } from './ui/progress';
import { useToast } from './ui/use-toast';
import backend from '~backend/client';
import { fetchWithRetry, getErrorMessage } from '@/lib/errorHandling';

// Update the backend client to include credentials
const backendWithCredentials = backend.with({
  requestInit: {
    credentials: 'include'
  }
});

interface FileUploadProps {
  martyrId: number;
  onUploadSuccess?: (fileUrl: string, fileId: string) => void;
  onUploadError?: (error: string) => void;
  accept?: string;
  maxSize?: number; // in bytes
  multiple?: boolean;
}

interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  martyrId,
  onUploadSuccess,
  onUploadError,
  accept = 'image/*',
  maxSize = 10 * 1024 * 1024, // 10MB default
  multiple = false
}) => {
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const { toast } = useToast();

  const updateUploadProgress = useCallback((fileName: string, updates: Partial<UploadProgress>) => {
    setUploads(prev => prev.map(upload => 
      upload.fileName === fileName ? { ...upload, ...updates } : upload
    ));
  }, []);

  const validateFile = (file: File): string | null => {
    if (file.size > maxSize) {
      return `File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`;
    }

    const acceptedTypes = accept.split(',').map(type => type.trim());
    if (!acceptedTypes.includes('*/*') && !acceptedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.slice(0, -2));
      }
      return file.type === type;
    })) {
      return `File type ${file.type} is not supported`;
    }

    return null;
  };

  const uploadFile = async (file: File): Promise<void> => {
    const validation = validateFile(file);
    if (validation) {
      toast({
        title: "Invalid File",
        description: validation,
        variant: "destructive",
      });
      return;
    }

    // Add to uploads list
    setUploads(prev => [...prev, {
      fileName: file.name,
      progress: 0,
      status: 'uploading'
    }]);

    try {
      // First, get signed upload URL using the generated client with retry
      const response = await fetchWithRetry(
        '/api/admin/martyrs/images/upload-url',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest' // CSRF protection
          },
          body: JSON.stringify({
            martyrId,
            filename: file.name,
            contentType: file.type,
            fileSize: file.size,
          }),
        }
      );
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Upload endpoints not available yet. Please regenerate the backend client first.');
        }
        throw new Error(`Failed to get upload URL: ${response.statusText}`);
      }

      const { uploadUrl, fileUrl, fields } = await response.json();

      // Create form data for direct upload
      const formData = new FormData();
      Object.entries(fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append('file', file);

      // Upload file with progress tracking
      await new Promise<void>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            updateUploadProgress(file.name, { progress });
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            updateUploadProgress(file.name, { 
              progress: 100, 
              status: 'success' 
            });
            onUploadSuccess?.(fileUrl, `${martyrId}-${Date.now()}`);
            resolve();
          } else {
            reject(new Error(`Upload failed: ${xhr.statusText}`));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed'));
        });

        xhr.open('POST', uploadUrl);
        // Include credentials for CSRF protection
        xhr.withCredentials = true;
        xhr.send(formData);
      });

      toast({
        title: "Upload Successful",
        description: `${file.name} has been uploaded successfully.`,
      });

    } catch (error) {
      const errorMessage = getErrorMessage(error as Error);
      updateUploadProgress(file.name, { 
        status: 'error', 
        error: errorMessage 
      });
      onUploadError?.(errorMessage);
      
      toast({
        title: "Upload Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    if (!multiple && fileArray.length > 1) {
      toast({
        title: "Multiple Files Not Allowed",
        description: "Please select only one file.",
        variant: "destructive",
      });
      return;
    }

    setShowProgressDialog(true);
    fileArray.forEach(uploadFile);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const closeProgressDialog = () => {
    setShowProgressDialog(false);
    setUploads([]);
  };

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Upload Images</CardTitle>
          <CardDescription>
            Drag and drop images here or click to select files
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragging 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-primary/50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="space-y-4">
              <div className="text-muted-foreground">
                <svg 
                  className="mx-auto h-12 w-12 mb-4" 
                  stroke="currentColor" 
                  fill="none" 
                  viewBox="0 0 48 48"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                {isDragging ? (
                  <p className="text-lg font-medium">Drop files here</p>
                ) : (
                  <>
                    <p className="text-lg font-medium">Drop files here or click to browse</p>
                    <p className="text-sm">
                      Supports: {accept} • Max size: {Math.round(maxSize / 1024 / 1024)}MB
                    </p>
                  </>
                )}
              </div>
              
              <Label htmlFor="file-upload">
                <Button asChild className="cursor-pointer">
                  <span>Select Files</span>
                </Button>
                <Input
                  id="file-upload"
                  type="file"
                  accept={accept}
                  multiple={multiple}
                  className="hidden"
                  onChange={(e) => handleFileSelect(e.target.files)}
                />
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress Dialog */}
      <AlertDialog open={showProgressDialog} onOpenChange={setShowProgressDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Upload Progress</AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-4">
                {uploads.map((upload) => (
                  <div key={upload.fileName} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="truncate">{upload.fileName}</span>
                      <span className={`font-medium ${
                        upload.status === 'success' ? 'text-green-600' :
                        upload.status === 'error' ? 'text-red-600' :
                        'text-blue-600'
                      }`}>
                        {upload.status === 'success' ? '✓' :
                         upload.status === 'error' ? '✗' :
                         `${upload.progress}%`}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all ${
                          upload.status === 'success' ? 'bg-green-500' :
                          upload.status === 'error' ? 'bg-red-500' :
                          'bg-blue-500'
                        }`}
                        style={{ width: `${upload.progress}%` }}
                      />
                    </div>
                    {upload.error && (
                      <p className="text-sm text-red-600">{upload.error}</p>
                    )}
                  </div>
                ))}
                
                {uploads.every(u => u.status !== 'uploading') && (
                  <div className="pt-4">
                    <Button onClick={closeProgressDialog} className="w-full">
                      Close
                    </Button>
                  </div>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};