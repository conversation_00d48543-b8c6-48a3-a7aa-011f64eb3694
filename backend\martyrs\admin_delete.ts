import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";
import { logMartyrDelete } from "../auth/auditLog";

interface DeleteMartyrRequest {
  id: number;
}

interface DeleteMartyrResponse {
  success: boolean;
  message: string;
}

// Deletes a martyr profile and all associated data.
export const deleteMartyr = api<DeleteMartyrRequest, DeleteMartyrResponse>(
  { auth: true, expose: true, method: "DELETE", path: "/admin/martyrs/:id" },
  async ({ id }) => {
    const auth = getAuthData();
    if (!auth || auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate ID
    if (!id || id <= 0) {
      throw APIError.invalidArgument("Invalid martyr ID");
    }

    try {
      // First, get the martyr's name for logging
      const martyr = await martyrsDB.queryRow<{ name: string; id: number }>`
        SELECT id, name FROM martyrs WHERE id = ${id}
      `;

      if (!martyr) {
        throw APIError.notFound("Martyr not found");
      }

      // Delete associated timeline events
      await martyrsDB.exec`
        DELETE FROM timeline_events WHERE martyr_id = ${id}
      `;

      // Delete associated images
      await martyrsDB.exec`
        DELETE FROM martyr_images WHERE martyr_id = ${id}
      `;

      // Delete the martyr record
      const result = await martyrsDB.exec`
        DELETE FROM martyrs WHERE id = ${id}
      `;

      if (result.rowsAffected === 0) {
        throw APIError.notFound("Martyr not found");
      }

      // Log the deletion
      await logMartyrDelete(martyr.id, martyr.name);

      return {
        success: true,
        message: "Martyr deleted successfully"
      };
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      console.error("Database error in deleteMartyr:", error);
      throw APIError.internal("Database error: " + (error as Error).message);
    }
  }
);