import { middleware, APIError } from "encore.dev/api";
import { createHash } from "crypto";

// Simple CSRF protection middleware
// This middleware checks for a custom header to prevent CSRF attacks
// Since we're using HttpOnly cookies, we need additional protection for state-changing operations
export const csrfProtection = middleware(
  { target: { auth: true, expose: true } }, // Apply to all authenticated exposed endpoints
  async (req, next) => {
    // Get the request method
    const method = req.requestMeta?.method?.toUpperCase();
    
    // Only apply CSRF protection to state-changing methods
    const stateChangingMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];
    if (method && stateChangingMethods.includes(method)) {
      // Check for custom header (a simple form of CSRF protection)
      // In a real implementation, you would use a more sophisticated approach
      const csrfHeader = req.requestMeta?.header?.get('X-Requested-With');
      
      // For AJAX requests, check if it's marked as such
      if (csrfHeader !== 'XMLHttpRequest') {
        // Additional check: verify SameSite attribute is working by checking Origin/Referer
        const origin = req.requestMeta?.header?.get('Origin');
        const host = req.requestMeta?.header?.get('Host');
        
        // In development, we might be more lenient, but in production we should be strict
        const isDevelopment = process.env.NODE_ENV === 'development';
        
        if (!isDevelopment && origin && host) {
          // Check if origin matches our expected domain
          // This is a simplified check - in production you'd want to be more specific
          if (!origin.includes(host) && !origin.includes('localhost')) {
            throw APIError.permissionDenied("CSRF protection: Invalid origin");
          }
        }
      }
    }
    
    // Continue with the request
    return await next(req);
  }
);