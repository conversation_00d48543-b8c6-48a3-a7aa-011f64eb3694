/**
 * Utility functions for generating shareable URLs and handling social sharing
 */

/**
 * Gets the base URL for the application
 * Client-side only since sharing typically happens in the browser
 */
export const getBaseUrl = (): string => {
  // Client-side
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  // Fallback for SSR (will be replaced on hydration)
  return 'https://martyrs-archive.org';
};

/**
 * Generates a full URL for a given path
 */
export const getFullUrl = (path: string): string => {
  const baseUrl = getBaseUrl();
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
};

/**
 * Generates social media share URLs
 */
export const generateShareUrls = (url: string, title: string, description?: string) => {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = description ? encodeURIComponent(description) : '';

  return {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
    telegram: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    reddit: `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`
  };
};

/**
 * Gets the martyr's full share URL
 */
export const getMartyrShareUrl = (slug: string): string => {
  return getFullUrl(`/martyrs/${slug}`);
};

/**
 * Truncates text for social sharing descriptions
 */
export const truncateForShare = (text: string, maxLength: number = 100): string => {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
};

/**
 * Generates structured data for social sharing
 */
export const generateMartyrShareData = (martyr: {
  name: string;
  slug: string;
  bio: string;
  region?: string;
  profileImage?: string;
}) => {
  const url = getMartyrShareUrl(martyr.slug);
  const title = `${martyr.name} - Martyr Story | Martyrs Archive`;
  const description = truncateForShare(martyr.bio, 120);
  
  return {
    url,
    title,
    description,
    image: martyr.profileImage,
    shareUrls: generateShareUrls(url, title, description)
  };
};