/* Enhanced Arabic Typography and Animation Styles */
/* Note: Base .arabic-text class is in critical.css */

.arabic-text-elegant {
  font-family: '<PERSON><PERSON>', '<PERSON><PERSON>', 'Noto Naskh Arabic', serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.9;
  letter-spacing: 0.03em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Mobile-optimized Arabic typography */
@media (min-width: 640px) {
  .arabic-text-elegant {
    font-size: 1.125rem;
    line-height: 2;
  }
}

@media (min-width: 768px) {
  .arabic-text-elegant {
    font-size: 1.25rem;
    line-height: 2.1;
  }
}

.arabic-text-large {
  font-size: 1.25rem;
  line-height: 2;
  font-weight: 500;
}

/* Mobile-optimized large Arabic text */
@media (min-width: 640px) {
  .arabic-text-large {
    font-size: 1.5rem;
    line-height: 2.2;
  }
}

@media (min-width: 768px) {
  .arabic-text-large {
    font-size: 1.75rem;
    line-height: 2.3;
  }
}

.arabic-text-xl {
  font-size: 1.5rem;
  line-height: 2.2;
  font-weight: 600;
}

/* Mobile-optimized extra large Arabic text */
@media (min-width: 640px) {
  .arabic-text-xl {
    font-size: 1.875rem;
    line-height: 2.4;
  }
}

@media (min-width: 768px) {
  .arabic-text-xl {
    font-size: 2rem;
    line-height: 2.5;
  }
}

/* Hero Arabic text - mobile first */
.arabic-hero-text {
  font-size: 1.125rem;
  line-height: 1.8;
  font-weight: 500;
}

@media (min-width: 640px) {
  .arabic-hero-text {
    font-size: 1.25rem;
    line-height: 1.9;
  }
}

@media (min-width: 768px) {
  .arabic-hero-text {
    font-size: 1.5rem;
    line-height: 2;
  }
}

@media (min-width: 1024px) {
  .arabic-hero-text {
    font-size: 1.75rem;
    line-height: 2.1;
  }
}

/* Arabic text in cards - mobile optimized */
.arabic-card-text {
  font-size: 0.875rem;
  line-height: 1.6;
  font-weight: 500;
}

@media (min-width: 640px) {
  .arabic-card-text {
    font-size: 1rem;
    line-height: 1.7;
  }
}

/* RTL Layout Support */
.rtl-container {
  direction: rtl;
}

.rtl-container .flex {
  flex-direction: row-reverse;
}

.rtl-container .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

.rtl-container .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

.rtl-container .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

/* Arabic-English mixed content spacing */
.mixed-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (min-width: 768px) {
  .mixed-content {
    gap: 0.75rem;
  }
}

/* Enhanced Typewriter Animation */
.typewriter-cursor {
  animation: blink 1.2s infinite;
  background: currentColor;
  width: 2px;
  height: 1em;
  display: inline-block;
  margin-left: 0.1em;
  vertical-align: baseline;
}

.typewriter-cursor.rtl {
  margin-left: 0;
  margin-right: 0.1em;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Smooth Fade-in Animations */
.fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.fade-in-down {
  animation: fadeInDown 0.8s ease-out forwards;
}

.fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Islamic Geometric Patterns */
.islamic-pattern {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(6, 95, 70, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(6, 95, 70, 0.1) 0%, transparent 50%);
  background-size: 120px 120px;
}

.islamic-pattern-subtle {
  background-image: 
    radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
  background-size: 100px 100px;
}

/* Enhanced Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* Staggered Animation Delays */
.stagger-delay-1 { animation-delay: 0.1s; }
.stagger-delay-2 { animation-delay: 0.2s; }
.stagger-delay-3 { animation-delay: 0.3s; }
.stagger-delay-4 { animation-delay: 0.4s; }
.stagger-delay-5 { animation-delay: 0.5s; }

/* Enhanced Button Hover Effects */
.btn-elegant {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.btn-elegant::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.btn-elegant:hover::before {
  left: 100%;
}

/* Floating Animation */
.float {
  animation: float 6s ease-in-out infinite;
}

.float-delayed {
  animation: float 6s ease-in-out infinite;
  animation-delay: -2s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) translateX(5px) rotate(90deg);
  }
  50% {
    transform: translateY(-10px) translateX(-5px) rotate(180deg);
  }
  75% {
    transform: translateY(-20px) translateX(3px) rotate(270deg);
  }
}

/* Petal floating animation */
.animate-float {
  animation: floatPetal 8s ease-in-out infinite;
}

@keyframes floatPetal {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) translateX(-8px) rotate(180deg);
    opacity: 0.4;
  }
  75% {
    transform: translateY(-25px) translateX(12px) rotate(270deg);
    opacity: 0.5;
  }
}

/* Pulse Animation for Sacred Elements */
.pulse-sacred {
  animation: pulseSacred 3s ease-in-out infinite;
}

@keyframes pulseSacred {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Respecting Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .typewriter-cursor {
    animation: none;
    opacity: 1;
  }
  
  .scroll-reveal,
  .scroll-reveal-left,
  .scroll-reveal-right {
    opacity: 1;
    transform: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .arabic-text {
    text-shadow: none;
  }
  
  .typewriter-cursor {
    background: currentColor;
    border: 1px solid currentColor;
  }
}

/* Enhanced Focus States for Accessibility */
.focus-enhanced:focus {
  outline: 3px solid rgba(16, 185, 129, 0.6);
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* Print Styles */
@media print {
  .arabic-text {
    font-size: 12pt;
    line-height: 1.5;
    color: black !important;
  }
  
  .typewriter-cursor {
    display: none;
  }
  
  .scroll-reveal,
  .scroll-reveal-left,
  .scroll-reveal-right {
    opacity: 1;
    transform: none;
  }
}