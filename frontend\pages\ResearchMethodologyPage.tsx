import React, { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Shield, BookOpen, Users, CheckCircle, FileText, Database, Eye } from 'lucide-react';
import { IslamicGeometricPattern, IslamicCalligraphyBorder, IslamicFrameBorder } from '@/components/IslamicPatterns';
import ReadingProgress from '../components/ReadingProgress';
import { motion } from 'framer-motion';
import animationPatterns from '../lib/animationPatterns';
import { getResearchMethodologyPageMeta, injectMetaTags } from '../lib/seoMeta';

export default function ResearchMethodologyPage() {
  // SEO metadata injection
  useEffect(() => {
    const seoData = getResearchMethodologyPageMeta();
    injectMetaTags(seoData);

    return () => {
      // Cleanup is handled by the next page's SEO injection
    };
  }, []);

  const methodologySteps = [
    {
      icon: Search,
      title: "Source Discovery",
      description: "We systematically identify and collect information from multiple reliable sources including family testimonies, community records, historical documents, and verified news reports focusing on Nigerian IMN martyrs.",
      color: "from-blue-500 to-indigo-500",
      bgColor: "from-blue-50 to-indigo-50",
      borderColor: "border-blue-200"
    },
    {
      icon: Shield,
      title: "Verification Process",
      description: "Each piece of information undergoes rigorous verification through cross-referencing multiple sources, fact-checking with community leaders, and validation against historical records to ensure accuracy.",
      color: "from-emerald-500 to-teal-500",
      bgColor: "from-emerald-50 to-teal-50",
      borderColor: "border-emerald-200"
    },
    {
      icon: FileText,
      title: "Documentation Standards",
      description: "All information is documented according to academic standards with proper source attribution, date verification, and contextual information to maintain scholarly integrity.",
      color: "from-purple-500 to-violet-500",
      bgColor: "from-purple-50 to-violet-50",
      borderColor: "border-purple-200"
    },
    {
      icon: Users,
      title: "Community Validation",
      description: "We work closely with families, community members, and Islamic Movement of Nigeria (IMN) representatives to validate information and ensure respectful representation of martyrs' stories.",
      color: "from-amber-500 to-orange-500",
      bgColor: "from-amber-50 to-orange-50",
      borderColor: "border-amber-200"
    }
  ];

  const qualityStandards = [
    {
      icon: CheckCircle,
      title: "Historical Accuracy",
      description: "All dates, locations, and events are verified through multiple sources and cross-referenced with historical records."
    },
    {
      icon: Database,
      title: "Data Integrity",
      description: "Information is stored with proper metadata, source tracking, and regular audits to maintain data quality."
    },
    {
      icon: Eye,
      title: "Transparency",
      description: "Our research process is open and transparent, with clear documentation of sources and methodology."
    },
    {
      icon: BookOpen,
      title: "Academic Rigor",
      description: "We follow established academic and journalistic standards for historical documentation and research."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 relative overflow-hidden">
      {/* Reading Progress Indicator */}
      <ReadingProgress 
        showPercentage={true}
        showBackToTop={true}
        position="top"
        color="from-emerald-600 to-teal-600"
      />

      {/* Background Islamic Pattern */}
      <div className="absolute inset-0 opacity-5">
        <IslamicGeometricPattern />
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl mb-8 shadow-2xl">
            <Search className="w-10 h-10 text-white" />
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-emerald-800 via-teal-700 to-emerald-900 bg-clip-text text-transparent">
            Research Methodology
          </h1>
          
          <IslamicCalligraphyBorder className="max-w-md mx-auto mb-6" color="#10b981" />
          
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Our comprehensive approach to documenting and preserving the stories of Nigerian IMN martyrs 
            through rigorous research standards and respectful methodology.
          </p>
          
          <div className="text-lg text-emerald-700 mt-4 font-medium" dir="rtl">
            منهجية البحث والتوثيق
          </div>
        </motion.div>

        {/* Methodology Steps */}
        <motion.section 
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.8 }}
        >
          <h2 className="text-3xl font-bold text-center mb-12 text-slate-800">Our Research Process</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {methodologySteps.map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index, duration: 0.6 }}
                whileHover={{ y: -4, scale: 1.02 }}
              >
                <Card className={`h-full bg-gradient-to-br ${step.bgColor} border-2 ${step.borderColor} shadow-lg hover:shadow-xl transition-all duration-300`}>
                  <CardHeader>
                    <div className={`w-12 h-12 bg-gradient-to-br ${step.color} rounded-xl flex items-center justify-center mb-4`}>
                      <step.icon className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-xl font-bold text-slate-800">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-700 leading-relaxed">{step.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Quality Standards */}
        <motion.section 
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <IslamicFrameBorder className="p-8">
            <h2 className="text-3xl font-bold text-center mb-12 text-slate-800">Quality Standards</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {qualityStandards.map((standard, index) => (
                <motion.div
                  key={standard.title}
                  className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-emerald-200/60 shadow-lg"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.1 * index, duration: 0.5 }}
                  whileHover={{ y: -4, scale: 1.05 }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <standard.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-slate-800 mb-2">{standard.title}</h3>
                  <p className="text-slate-600 text-sm leading-relaxed">{standard.description}</p>
                </motion.div>
              ))}
            </div>
          </IslamicFrameBorder>
        </motion.section>

        {/* Ethical Guidelines */}
        <motion.section 
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7, duration: 0.8 }}
        >
          <Card className="bg-gradient-to-br from-emerald-50 to-teal-50 border-2 border-emerald-200 shadow-xl">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-emerald-800 text-center">
                Ethical Guidelines for Nigerian IMN Martyrs Documentation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-emerald-700">Respectful Representation</h3>
                  <ul className="space-y-2 text-slate-700">
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Honor the dignity and memory of each martyr</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Respect Islamic traditions and cultural sensitivities</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Maintain accuracy without sensationalism</span>
                    </li>
                  </ul>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-emerald-700">Family Privacy</h3>
                  <ul className="space-y-2 text-slate-700">
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Obtain consent from families when possible</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Protect sensitive personal information</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-emerald-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Honor requests for information removal</span>
                    </li>
                  </ul>
                </div>
              </div>
              
              <div className="text-center pt-6 border-t border-emerald-200">
                <p className="text-emerald-700 font-medium">
                  "We are committed to preserving the sacred memory of Nigerian IMN martyrs with the highest ethical standards."
                </p>
                <p className="text-sm text-slate-600 mt-2" dir="rtl">
                  رحمة الله عليهم أجمعين
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.section>
      </div>
    </div>
  );
}
