import { api } from "encore.dev/api";
import { martyrsDB } from "./db";
import type { MartyrCard } from "./types";

interface GetCategoryParams {
  category: string;
}

interface GetCategoryResponse {
  martyrs: MartyrCard[];
  total: number;
}

// Retrieves martyrs by category.
export const getCategory = api<GetCategoryParams, GetCategoryResponse>(
  { expose: true, method: "GET", path: "/categories/:category" },
  async ({ category }) => {
    const decodedCategory = decodeURIComponent(category);
    
    const martyrs = await martyrsDB.queryAll<{
      id: number;
      name: string;
      slug: string;
      bio: string;
      sub_categories: string[];
      region?: string;
      profile_image?: string;
    }>`
      SELECT 
        m.id,
        m.name,
        m.slug,
        LEFT(m.bio, 150) as bio,
        m.sub_categories,
        m.region,
        mi.url as profile_image
      FROM martyrs m
      LEFT JOIN martyr_images mi ON m.id = mi.martyr_id AND mi.is_profile_image = true
      WHERE ${decodedCategory} = ANY(m.sub_categories)
      ORDER BY m.name
    `;

    return {
      martyrs: martyrs.map(m => ({
        id: m.id,
        name: m.name,
        slug: m.slug,
        bio: m.bio,
        subCategories: m.sub_categories || [],
        region: m.region,
        profileImage: m.profile_image
      })),
      total: martyrs.length
    };
  }
);
