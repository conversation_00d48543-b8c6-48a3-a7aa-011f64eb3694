import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import backend from '~backend/client';

export default function AdminLoginPage() {
  const [credentials, setCredentials] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Check if already logged in by making a request to a protected endpoint
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Try to fetch dashboard data to check if we're already authenticated
        await backend.martyrs.getViewsOverTime();
        console.log('Already logged in, redirecting to admin dashboard');
        navigate('/admin', { replace: true });
      } catch (error) {
        // Not authenticated, stay on login page
        console.log('Not authenticated, staying on login page');
      }
    };
    
    checkAuthStatus();
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('Attempting login with:', credentials.email);
      const response = await backend.auth.login(credentials);
      console.log('Login response received:', response);
      
      // Store user data in localStorage
      localStorage.setItem('admin_user', JSON.stringify(response.user));
      console.log('User data stored in localStorage');
      
      // Show success toast
      toast({
        title: "Login Successful",
        description: "Welcome to the admin dashboard.",
      });

      // Force immediate navigation
      console.log('Navigating to admin dashboard');
      navigate('/admin', { replace: true });
      
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: "Login Failed",
        description: "Invalid email or password.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        <Card>
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white font-bold text-xl">M</span>
            </div>
            <CardTitle className="text-2xl">Admin Login</CardTitle>
            <p className="text-gray-600">Access the Martyrs Archive admin dashboard</p>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={credentials.email}
                  onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
                  required
                  className="mt-1"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={credentials.password}
                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                  required
                  className="mt-1"
                  placeholder="Enter your password"
                />
              </div>
              
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                {isLoading ? 'Signing in...' : 'Sign In'}
              </Button>
            </form>
            
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Demo Credentials:</strong><br />
                Email: <EMAIL><br />
                Password: Admin@2025!
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}