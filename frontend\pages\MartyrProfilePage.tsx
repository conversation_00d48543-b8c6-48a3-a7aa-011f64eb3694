import React from 'react';
import { usePara<PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import DOMPurify from 'dompurify';
import { Calendar, MapPin, ArrowLeft, Users, Star, Moon, Heart, BookOpen, Clock, Award, Shield, Scroll } from 'lucide-react';
import backend from '~backend/client';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ImageGallery from '../components/ImageGallery';
import Timeline from '../components/Timeline';
import MartyrCard from '../components/MartyrCard';
import '../styles/prose.css';

export default function MartyrProfilePage() {
  const { slug } = useParams<{ slug: string }>();

  const { data: profile, isLoading, error } = useQuery({
    queryKey: ['martyr-profile', slug],
    queryFn: () => backend.martyrs.getProfile({ slug: slug! }),
    enabled: !!slug
  });

  const { data: relatedData } = useQuery({
    queryKey: ['related-martyrs', slug],
    queryFn: () => backend.martyrs.getRelated({ slug: slug! }),
    enabled: !!slug
  });

  const createMarkup = (htmlContent: string) => {
    return { __html: DOMPurify.sanitize(htmlContent) };
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded-xl w-1/4 mb-8"></div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 mb-8">
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="w-full lg:w-80 h-80 bg-slate-200 rounded-2xl"></div>
                <div className="flex-1 space-y-6">
                  <div className="h-10 bg-slate-200 rounded-xl w-3/4"></div>
                  <div className="h-6 bg-slate-200 rounded-lg w-1/2"></div>
                  <div className="h-6 bg-slate-200 rounded-lg w-1/3"></div>
                  <div className="h-32 bg-slate-200 rounded-xl"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20 flex items-center justify-center">
        <div className="text-center">
          <div className="w-24 h-24 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
            <Heart className="w-12 h-12 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-slate-800 mb-4">Martyr Not Found</h1>
          <p className="text-slate-600 mb-8">The requested martyr profile could not be found.</p>
          <Link to="/search">
            <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-3 rounded-xl shadow-lg">
              Return to Search
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const profileImage = profile.images.find(img => img.isProfileImage) || profile.images[0];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-emerald-50/30 to-amber-50/20">
      <div className="fixed inset-0 opacity-5 pointer-events-none">
        <div className="w-full h-full bg-[radial-gradient(circle_at_center,transparent_40%,currentColor_40%,currentColor_60%,transparent_60%)] bg-emerald-600" 
             style={{ backgroundSize: '80px 80px' }}></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <Link to="/search" className="inline-flex items-center text-emerald-700 hover:text-emerald-800 mb-8 group transition-all duration-300">
          <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          <span className="font-medium">Return to Martyrs Archive</span>
        </Link>

        <div className="mb-12">
          <Card className="bg-gradient-to-r from-emerald-800 to-teal-800 text-white border-0 shadow-2xl overflow-hidden relative">
            <div className="absolute inset-0 opacity-20">
              <div className="w-full h-full bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.1)_25%,rgba(255,255,255,0.1)_50%,transparent_50%,transparent_75%,rgba(255,255,255,0.1)_75%)]" 
                   style={{ backgroundSize: '30px 30px' }}></div>
            </div>
            
            <CardContent className="p-0 relative z-10">
              <div className="flex flex-col lg:flex-row">
                <div className="w-full lg:w-96 flex-shrink-0 relative">
                  {profileImage ? (
                    <div className="relative h-96 lg:h-full">
                      <img src={profileImage.url} alt={profile.name} className="w-full h-full object-cover" />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                      <div className="absolute top-6 right-6 flex space-x-2">
                        <div className="w-12 h-12 bg-amber-400/20 backdrop-blur-md rounded-full flex items-center justify-center border border-amber-400/30"><Star className="w-6 h-6 text-amber-300" /></div>
                        <div className="w-12 h-12 bg-emerald-400/20 backdrop-blur-md rounded-full flex items-center justify-center border border-emerald-400/30"><Moon className="w-6 h-6 text-emerald-300" /></div>
                      </div>
                      <div className="absolute bottom-6 left-6 bg-red-600/90 backdrop-blur-sm px-4 py-2 rounded-full">
                        <div className="flex items-center space-x-2"><Heart className="w-4 h-4 text-white" /><span className="text-white text-sm font-semibold">SHAHID</span></div>
                      </div>
                    </div>
                  ) : (
                    <div className="h-96 lg:h-full bg-gradient-to-br from-emerald-600/20 via-teal-500/20 to-amber-500/20 flex items-center justify-center relative overflow-hidden">
                      <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(255,255,255,0.1)_0%,transparent_70%)]"></div>
                      <div className="relative z-10 text-center">
                        <div className="w-32 h-32 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center mb-6 mx-auto shadow-2xl backdrop-blur-sm border border-white/20"><span className="text-6xl font-bold text-white">{profile.name.charAt(0)}</span></div>
                        <div className="flex justify-center items-center space-x-4"><Star className="w-6 h-6 text-amber-300 animate-pulse" /><Moon className="w-7 h-7 text-white" /><Star className="w-6 h-6 text-amber-300 animate-pulse delay-500" /></div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex-1 p-8 lg:p-12">
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-400 rounded-full flex items-center justify-center mr-4 shadow-lg"><Shield className="w-5 h-5 text-white" /></div>
                    <div>
                      <span className="text-amber-200 text-sm font-medium block">In Sacred Memory</span>
                      <span className="text-white/80 text-xs">Martyr of Faith • شهید الایمان</span>
                    </div>
                  </div>
                  <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">{profile.name}</h1>
                  <div className="flex items-center mb-8 text-amber-200"><Clock className="w-5 h-5 mr-3" /><span className="text-lg font-medium">{profile.birthDate && formatDate(profile.birthDate)}{profile.birthDate && profile.deathDate && ' - '}{profile.deathDate && formatDate(profile.deathDate)}</span></div>
                  <div className="flex flex-wrap gap-3 mb-8">
                    {profile.subCategories.map((category) => (<Badge key={category} className="bg-white/20 backdrop-blur-sm text-white border border-white/30 px-4 py-2 text-sm font-medium rounded-xl hover:bg-white/30 transition-colors duration-300">{category}</Badge>))}
                  </div>
                  {(profile.birthPlace || profile.deathPlace || profile.region) && (
                    <div className="space-y-3 mb-8">
                      {profile.birthPlace && <div className="flex items-center text-white/90"><MapPin className="w-4 h-4 mr-3 text-emerald-300" /><span className="text-sm">Born in {profile.birthPlace}</span></div>}
                      {profile.deathPlace && <div className="flex items-center text-white/90"><Star className="w-4 h-4 mr-3 text-amber-300" /><span className="text-sm">Martyred in {profile.deathPlace}</span></div>}
                      {profile.region && <div className="flex items-center text-white/90"><MapPin className="w-4 h-4 mr-3 text-blue-300" /><span className="text-sm">Region: {profile.region}</span></div>}
                    </div>
                  )}
                  <div className="prose prose-invert line-clamp-3" dangerouslySetInnerHTML={createMarkup(profile.bio)} />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {(profile.martyrdomCause || profile.martyrdomContext) && (
          <Card className="mb-12 bg-gradient-to-r from-red-50 to-rose-50 border-l-8 border-red-500 shadow-2xl">
            <CardHeader><CardTitle className="flex items-center text-2xl text-red-800"><Heart className="w-8 h-8 mr-4 text-red-600" />Path to Martyrdom<div className="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center ml-4"><Star className="w-3 h-3 text-white" /></div></CardTitle></CardHeader>
            <CardContent className="p-8">
              <div className="grid md:grid-cols-2 gap-8">
                {profile.martyrdomCause && (
                  <div>
                    <h4 className="font-bold text-red-800 mb-3 flex items-center"><Shield className="w-5 h-5 mr-2" />Cause of Martyrdom</h4>
                    <div className="prose" dangerouslySetInnerHTML={createMarkup(profile.martyrdomCause)} />
                  </div>
                )}
                {profile.martyrdomContext && (
                  <div>
                    <h4 className="font-bold text-red-800 mb-3 flex items-center"><BookOpen className="w-5 h-5 mr-2" />Context & Circumstances</h4>
                    <div className="prose" dangerouslySetInnerHTML={createMarkup(profile.martyrdomContext)} />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="mb-12 bg-white/90 backdrop-blur-sm border-0 shadow-2xl">
          <CardHeader><CardTitle className="flex items-center text-3xl text-slate-800"><Scroll className="w-8 h-8 mr-4 text-emerald-600" />Life Story<div className="w-8 h-8 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full flex items-center justify-center ml-4"><BookOpen className="w-4 h-4 text-white" /></div></CardTitle></CardHeader>
          <CardContent className="p-8">
            <div className="prose max-w-none" dangerouslySetInnerHTML={createMarkup(profile.bio)} />
            {profile.familyInfo && (
              <div className="mt-8 p-6 bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl border-l-4 border-amber-400 shadow-lg">
                <h4 className="font-bold text-amber-800 mb-4 flex items-center text-xl"><Users className="w-6 h-6 mr-3" />Family Heritage</h4>
                <div className="prose" dangerouslySetInnerHTML={createMarkup(profile.familyInfo)} />
                <div className="mt-4 p-3 bg-amber-100/50 rounded-xl"><p className="text-xs text-amber-600 italic">* Family information shared with respect for privacy and dignity</p></div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="mb-12 bg-white/90 backdrop-blur-sm border-0 shadow-2xl">
          <CardHeader><CardTitle className="flex items-center text-3xl text-slate-800"><Calendar className="w-8 h-8 mr-4 text-emerald-600" />Life Journey<div className="w-8 h-8 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full flex items-center justify-center ml-4"><Clock className="w-4 h-4 text-white" /></div></CardTitle></CardHeader>
          <CardContent className="p-8"><Timeline events={profile.timelineEvents} /></CardContent>
        </Card>

        {profile.quotes.length > 0 && (
          <Card className="mb-12 bg-gradient-to-br from-emerald-50 to-teal-50 border-0 shadow-2xl">
            <CardHeader><CardTitle className="flex items-center text-3xl text-slate-800"><Scroll className="w-8 h-8 mr-4 text-emerald-600" />Sacred Wisdom<div className="w-8 h-8 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full flex items-center justify-center ml-4"><Star className="w-4 h-4 text-white" /></div></CardTitle></CardHeader>
            <CardContent className="p-8">
              <div className="grid gap-8">
                {profile.quotes.map((quote, index) => (
                  <div key={index} className="bg-white/80 backdrop-blur-sm border-l-4 border-emerald-500 rounded-2xl p-8 shadow-xl relative">
                    <div className="absolute top-4 left-4 w-12 h-12 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full flex items-center justify-center"><span className="text-white font-bold text-lg">{index + 1}</span></div>
                    <div className="pl-16">
                      <blockquote className="text-xl text-slate-700 italic leading-relaxed mb-4">"{quote}"</blockquote>
                      <div className="flex items-center justify-end"><div className="flex items-center space-x-2"><Star className="w-5 h-5 text-amber-500" /><span className="text-emerald-700 font-semibold">— {profile.name}</span></div></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {profile.images.length > 0 && (
          <Card className="mb-12 bg-white/90 backdrop-blur-sm border-0 shadow-2xl">
            <CardHeader><CardTitle className="flex items-center text-3xl text-slate-800"><Award className="w-8 h-8 mr-4 text-emerald-600" />Visual Legacy<div className="w-8 h-8 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full flex items-center justify-center ml-4"><Star className="w-4 h-4 text-white" /></div></CardTitle></CardHeader>
            <CardContent className="p-8"><ImageGallery images={profile.images} /></CardContent>
          </Card>
        )}

        {relatedData?.related && relatedData.related.length > 0 && (
          <Card className="mb-12 bg-white/90 backdrop-blur-sm border-0 shadow-2xl">
            <CardHeader><CardTitle className="flex items-center text-3xl text-slate-800"><Users className="w-8 h-8 mr-4 text-emerald-600" />Eternal Connections<div className="w-8 h-8 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-full flex items-center justify-center ml-4"><Heart className="w-4 h-4 text-white" /></div></CardTitle></CardHeader>
            <CardContent className="p-8">
              <p className="text-slate-600 mb-8 text-lg">Other martyrs who shared similar paths, beliefs, or circumstances with {profile.name}.</p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {relatedData.related.map((martyr) => (<MartyrCard key={martyr.id} {...martyr} />))}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="space-y-6">
          <Card className="bg-gradient-to-r from-emerald-800 to-teal-800 text-white border-0 shadow-2xl">
            <CardContent className="p-8 text-center">
              <div className="flex justify-center mb-4"><div className="w-16 h-16 bg-amber-400/20 backdrop-blur-md rounded-full flex items-center justify-center border border-amber-400/30"><Moon className="w-8 h-8 text-amber-300" /></div></div>
              <h3 className="text-2xl font-bold mb-4">رحمة الله عليه</h3>
              <p className="text-lg text-white/90 leading-relaxed">May Allah grant {profile.name} the highest ranks in Paradise and shower His mercy upon their soul. May their sacrifice inspire us to live with purpose and faith.</p>
              <div className="flex justify-center space-x-4 mt-6"><Star className="w-6 h-6 text-amber-300" /><Heart className="w-6 h-6 text-red-300" /><Star className="w-6 h-6 text-amber-300" /></div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 shadow-xl">
            <CardContent className="p-6">
              <div className="flex items-start">
                <BookOpen className="w-6 h-6 text-amber-600 mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-bold text-amber-800 mb-2">Archive Disclaimer</h4>
                  <p className="text-amber-700 leading-relaxed">This profile is compiled from verified historical records and testimonies. We deeply respect all copyrights and welcome corrections or additional authentic information. Our mission is to preserve the memory of martyrs with dignity and accuracy. Contact us with any concerns about the content presented here.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}