import React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { X, Filter, Star } from 'lucide-react';

interface SearchFiltersProps {
  filters: {
    subCategories: string[];
    regions: string[];
    periods: string[];
    causes: string[];
  };
  selectedFilters: {
    subCategories: string[];
    regions: string[];
    periods: string[];
    causes: string[];
  };
  onFilterChange: (type: string, value: string, checked: boolean) => void;
  onClearFilters: () => void;
}

export default function SearchFilters({
  filters,
  selectedFilters,
  onFilterChange,
  onClearFilters
}: SearchFiltersProps) {
  const hasActiveFilters = Object.values(selectedFilters).some(arr => arr.length > 0);

  return (
    <Card className="w-full bg-white/80 backdrop-blur-sm border-0 shadow-xl">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl flex items-center text-slate-800">
            <Filter className="w-5 h-5 mr-3 text-emerald-600" />
            Filters
          </CardTitle>
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearFilters}
              className="text-xs border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 rounded-lg"
            >
              <X className="w-3 h-3 mr-1" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-8">
        {/* Sub-categories */}
        <div>
          <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
            <Star className="w-4 h-4 mr-2 text-emerald-600" />
            Categories
          </h4>
          <div className="space-y-3">
            {filters.subCategories.map((category) => (
              <div key={category} className="flex items-center space-x-3 group">
                <Checkbox
                  id={`category-${category}`}
                  checked={selectedFilters.subCategories.includes(category)}
                  onCheckedChange={(checked) =>
                    onFilterChange('subCategories', category, checked as boolean)
                  }
                  className="rounded-md border-2 border-emerald-200 data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600"
                />
                <Label
                  htmlFor={`category-${category}`}
                  className="text-sm text-slate-700 cursor-pointer group-hover:text-emerald-700 transition-colors duration-300 font-medium"
                >
                  {category}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Regions */}
        {filters.regions.length > 0 && (
          <div>
            <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
              <div className="w-4 h-4 mr-2 bg-blue-600 rounded-full"></div>
              Regions
            </h4>
            <div className="space-y-3">
              {filters.regions.map((region) => (
                <div key={region} className="flex items-center space-x-3 group">
                  <Checkbox
                    id={`region-${region}`}
                    checked={selectedFilters.regions.includes(region)}
                    onCheckedChange={(checked) =>
                      onFilterChange('regions', region, checked as boolean)
                    }
                    className="rounded-md border-2 border-blue-200 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                  />
                  <Label
                    htmlFor={`region-${region}`}
                    className="text-sm text-slate-700 cursor-pointer group-hover:text-blue-700 transition-colors duration-300 font-medium"
                  >
                    {region}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Periods */}
        {filters.periods.length > 0 && (
          <div>
            <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
              <div className="w-4 h-4 mr-2 bg-purple-600 rounded-full"></div>
              Periods
            </h4>
            <div className="space-y-3">
              {filters.periods.map((period) => (
                <div key={period} className="flex items-center space-x-3 group">
                  <Checkbox
                    id={`period-${period}`}
                    checked={selectedFilters.periods.includes(period)}
                    onCheckedChange={(checked) =>
                      onFilterChange('periods', period, checked as boolean)
                    }
                    className="rounded-md border-2 border-purple-200 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                  />
                  <Label
                    htmlFor={`period-${period}`}
                    className="text-sm text-slate-700 cursor-pointer group-hover:text-purple-700 transition-colors duration-300 font-medium"
                  >
                    {period}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Causes */}
        {filters.causes.length > 0 && (
          <div>
            <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
              <div className="w-4 h-4 mr-2 bg-amber-600 rounded-full"></div>
              Causes
            </h4>
            <div className="space-y-3">
              {filters.causes.slice(0, 5).map((cause) => (
                <div key={cause} className="flex items-center space-x-3 group">
                  <Checkbox
                    id={`cause-${cause}`}
                    checked={selectedFilters.causes.includes(cause)}
                    onCheckedChange={(checked) =>
                      onFilterChange('causes', cause, checked as boolean)
                    }
                    className="rounded-md border-2 border-amber-200 data-[state=checked]:bg-amber-600 data-[state=checked]:border-amber-600"
                  />
                  <Label
                    htmlFor={`cause-${cause}`}
                    className="text-sm text-slate-700 cursor-pointer group-hover:text-amber-700 transition-colors duration-300 font-medium"
                  >
                    {cause}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
