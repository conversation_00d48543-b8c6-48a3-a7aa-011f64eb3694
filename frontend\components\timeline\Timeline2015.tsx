import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, MapPin, Users, Heart, ChevronDown, ChevronUp, ExternalLink, AlertTriangle } from 'lucide-react';
import { getEventsByYear, getTotalCasualtiesByYear } from './timelineData';
import { IslamicGeometricPattern, IslamicCalligraphyBorder } from '../IslamicPatterns';

interface Timeline2015Props {
  className?: string;
}

export default function Timeline2015({ className = '' }: Timeline2015Props) {
  const [expandedEvent, setExpandedEvent] = useState<string | null>(null);
  const events = getEventsByYear(2015);
  const yearStats = getTotalCasualtiesByYear(2015);

  const toggleEventExpansion = (eventId: string) => {
    setExpandedEvent(expandedEvent === eventId ? null : eventId);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <IslamicGeometricPattern />
      </div>

      <div className="relative">
        {/* Year Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-red-700 to-red-900 rounded-full mb-4 shadow-lg">
            <span className="text-3xl font-bold text-white">2015</span>
          </div>
          <h2 className="text-3xl font-bold text-slate-800 mb-2">
            The Zaria Massacre
          </h2>
          <h3 className="text-xl text-red-700 font-semibold mb-3" dir="rtl">
            مجزرة زاريا الكبرى
          </h3>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
            The most devastating year in IMN history, marked by a systematic three-day military assault 
            that resulted in over 1000 deaths and the arrest of Sheikh Ibrahim Zakzaky.
          </p>
        </motion.div>

        {/* Critical Alert */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-gradient-to-r from-red-100 to-red-200 border-l-4 border-red-600 rounded-lg p-4 mb-6"
        >
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-6 h-6 text-red-600 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-red-800">Most Severe Persecution Event</h4>
              <p className="text-red-700 text-sm">
                This three-day massacre represents the deadliest attack on IMN members in Nigerian history, 
                with systematic targeting of civilians, religious buildings, and medical facilities.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Year Statistics */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-gradient-to-r from-red-50 to-red-100 rounded-xl p-6 mb-8 border border-red-200"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-red-700 rounded-full flex items-center justify-center mb-2">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <span className="text-3xl font-bold text-red-800">{yearStats.killed}</span>
              <span className="text-sm text-red-600">Martyrs</span>
              <span className="text-xs text-red-500 mt-1">Including 6 sons of Sheikh Zakzaky</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-white" />
              </div>
              <span className="text-3xl font-bold text-orange-800">{yearStats.injured}</span>
              <span className="text-sm text-orange-600">Injured</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-slate-600 rounded-full flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-white" />
              </div>
              <span className="text-3xl font-bold text-slate-800">{yearStats.arrested}</span>
              <span className="text-sm text-slate-600">Arrested</span>
              <span className="text-xs text-slate-500 mt-1">Including Sheikh Zakzaky</span>
            </div>
          </div>
        </motion.div>

        {/* Timeline Events */}
        <div className="space-y-6">
          {events.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              className="relative"
            >
              {/* Timeline Line */}
              <div className="absolute left-6 top-16 bottom-0 w-0.5 bg-gradient-to-b from-red-500 to-red-700"></div>
              
              {/* Event Card */}
              <div className="relative bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden">
                <IslamicCalligraphyBorder />
                
                {/* Severity Indicator */}
                <div className="absolute top-0 right-0 w-2 h-full bg-gradient-to-b from-red-600 to-red-800"></div>
                
                {/* Event Header */}
                <div className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* Date Circle */}
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-red-700 to-red-900 rounded-full flex items-center justify-center shadow-lg">
                      <Calendar className="w-6 h-6 text-white" />
                    </div>
                    
                    {/* Event Info */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-xl font-bold text-slate-800">{event.title}</h3>
                        <button
                          onClick={() => toggleEventExpansion(event.id)}
                          className="p-2 hover:bg-slate-100 rounded-lg transition-colors duration-200"
                          aria-label={expandedEvent === event.id ? "Collapse details" : "Expand details"}
                        >
                          {expandedEvent === event.id ? (
                            <ChevronUp className="w-5 h-5 text-slate-600" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-slate-600" />
                          )}
                        </button>
                      </div>
                      
                      {event.arabicTitle && (
                        <p className="text-lg text-emerald-700 font-arabic mb-2" dir="rtl">
                          {event.arabicTitle}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4 text-sm text-slate-600 mb-3">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(event.date).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{event.location}</span>
                        </div>
                      </div>
                      
                      {/* Casualties Summary */}
                      <div className="flex items-center space-x-6 text-sm mb-3">
                        <div className="flex items-center space-x-1 text-red-700">
                          <Heart className="w-4 h-4" />
                          <span className="font-bold">{event.casualties.killed} martyrs</span>
                        </div>
                        <div className="flex items-center space-x-1 text-orange-600">
                          <Users className="w-4 h-4" />
                          <span className="font-semibold">{event.casualties.injured} injured</span>
                        </div>
                        {event.casualties.arrested && (
                          <div className="flex items-center space-x-1 text-slate-600">
                            <Users className="w-4 h-4" />
                            <span className="font-semibold">{event.casualties.arrested} arrested</span>
                          </div>
                        )}
                      </div>
                      
                      <p className="text-slate-700 leading-relaxed">
                        {event.description}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                <AnimatePresence>
                  {expandedEvent === event.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-slate-200 bg-slate-50"
                    >
                      <div className="p-6 space-y-4">
                        {/* Context */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Context</h4>
                          <p className="text-slate-700 leading-relaxed">{event.context}</p>
                        </div>
                        
                        {/* Significance */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Historical Significance</h4>
                          <p className="text-slate-700 leading-relaxed">{event.significance}</p>
                        </div>
                        
                        {/* Notable Martyrs */}
                        {event.notableMartyrs && event.notableMartyrs.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-slate-800 mb-2">Notable Martyrs</h4>
                            <div className="bg-white rounded-lg p-4 border border-red-200">
                              <ul className="space-y-2">
                                {event.notableMartyrs.map((martyr, idx) => (
                                  <li key={idx} className="flex items-center space-x-2">
                                    <Heart className="w-4 h-4 text-red-600 flex-shrink-0" />
                                    <span className="text-slate-700 font-medium">{martyr}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )}
                        
                        {/* Sources */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Sources</h4>
                          <ul className="space-y-1">
                            {event.sources.map((source, idx) => (
                              <li key={idx} className="flex items-center space-x-2 text-sm text-slate-600">
                                <ExternalLink className="w-3 h-3 flex-shrink-0" />
                                <span>{source}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Memorial Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mt-12 bg-gradient-to-r from-slate-800 to-slate-900 rounded-xl p-8 text-white text-center"
        >
          <Heart className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-2xl font-bold mb-2">In Memory of the Martyrs</h3>
          <p className="text-slate-300 mb-4">
            We remember the {yearStats.killed} souls who lost their lives during the Zaria Massacre of December 2015.
            Their sacrifice will never be forgotten, and their memory continues to inspire the pursuit of justice and peace.
          </p>
          <p className="text-lg text-emerald-400 font-arabic" dir="rtl">
            إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ
          </p>
          <p className="text-sm text-slate-400 mt-2">
            "Indeed we belong to Allah, and indeed to Him we will return" - Quran 2:156
          </p>
        </motion.div>
      </div>
    </div>
  );
}
