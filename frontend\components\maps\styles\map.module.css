/* OpenLayers Map Styles */
.mapContainer {
  width: 100%;
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.mapContainer .ol-viewport {
  border-radius: 0.5rem;
}

/* Custom Map Controls */
.ol-zoom {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ol-zoom button {
  background: transparent;
  border: none;
  color: #065f46;
  font-weight: 600;
  font-size: 1.125rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.ol-zoom button:hover {
  background: #d1fae5;
  color: #047857;
}

.ol-zoom button:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

/* Attribution */
.ol-attribution {
  bottom: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.ol-attribution a {
  color: #059669;
  text-decoration: none;
}

.ol-attribution a:hover {
  text-decoration: underline;
}

/* Custom Popup Overlay */
.mapPopup {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  min-width: 250px;
  position: relative;
  z-index: 1000;
}

.mapPopup::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.popupHeader {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
}

.popupTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  padding-right: 2rem;
}

.popupCloseButton {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.popupCloseButton:hover {
  background: #f3f4f6;
  color: #374151;
}

.popupContent {
  padding: 1rem;
}

.popupImage {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 0.375rem;
  margin-bottom: 0.75rem;
}

.popupCategories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.popupCategory {
  background: #d1fae5;
  color: #065f46;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.popupLink {
  display: inline-flex;
  align-items: center;
  color: #059669;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.popupLink:hover {
  color: #047857;
}

/* Loading State */
.mapLoading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  color: #6b7280;
  font-size: 1rem;
}

.loadingSpinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #059669;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.75rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .mapPopup {
    max-width: 280px;
    min-width: 220px;
  }
  
  .ol-zoom {
    top: 0.25rem;
    left: 0.25rem;
  }
  
  .ol-zoom button {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .mapContainer {
    border: 2px solid #000;
  }
  
  .ol-zoom button {
    border: 1px solid #000;
  }
  
  .mapPopup {
    border: 2px solid #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .ol-zoom button,
  .popupCloseButton,
  .popupLink {
    transition: none;
  }
  
  .loadingSpinner {
    animation: none;
  }
}