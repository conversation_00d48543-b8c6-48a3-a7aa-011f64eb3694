/**
 * Structured Data Utilities for Schema.org Markup
 * Generates JSON-LD structured data for martyrs, organizations, and educational content
 */

export interface MartyrData {
  id: string;
  name: string;
  arabicName?: string;
  dateOfBirth?: string;
  dateOfDeath?: string;
  bio?: string;
  category?: string;
  imageUrl?: string;
  location?: string;
  organization?: string;
}

export interface OrganizationData {
  name: string;
  arabicName?: string;
  description: string;
  founded?: string;
  location?: string;
  url?: string;
}

/**
 * Generate structured data for a person (martyr)
 */
export function generatePersonSchema(martyr: MartyrData): object {
  const schema: any = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": martyr.name,
    "identifier": martyr.id,
    "description": martyr.bio || `Memorial page for ${martyr.name}, a martyr documented in the Martyrs Archive.`,
    "keywords": ["martyr", "memorial", "historical figure", martyr.category, "Islamic Movement Nigeria"].filter(Boolean),
    "knowsAbout": ["Justice", "Faith", "Human Rights", "Religious Freedom"],
    "additionalType": "https://schema.org/Person",
    "sameAs": [],
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${typeof window !== 'undefined' ? window.location.origin : ''}/martyr/${martyr.id}`
    }
  };

  // Add alternative names in Arabic
  if (martyr.arabicName) {
    schema.alternateName = martyr.arabicName;
  }

  // Add birth and death dates
  if (martyr.dateOfBirth) {
    schema.birthDate = martyr.dateOfBirth;
  }

  if (martyr.dateOfDeath) {
    schema.deathDate = martyr.dateOfDeath;
  }

  // Add image
  if (martyr.imageUrl) {
    schema.image = {
      "@type": "ImageObject",
      "url": martyr.imageUrl,
      "description": `Portrait of ${martyr.name}`
    };
  }

  // Add location information
  if (martyr.location) {
    schema.birthPlace = {
      "@type": "Place",
      "name": martyr.location
    };
  }

  // Add organization affiliation
  if (martyr.organization) {
    schema.memberOf = {
      "@type": "Organization",
      "name": martyr.organization
    };
  }

  return schema;
}

/**
 * Generate structured data for an organization
 */
export function generateOrganizationSchema(org: OrganizationData): object {
  const schema: any = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": org.name,
    "description": org.description,
    "keywords": ["religious organization", "Islamic movement", "Nigeria", "human rights"],
    "additionalType": "https://schema.org/NGO",
    "knowsAbout": ["Religious Freedom", "Human Rights", "Justice", "Education"],
    "areaServed": {
      "@type": "Country",
      "name": "Nigeria"
    }
  };

  // Add alternative names
  if (org.arabicName) {
    schema.alternateName = org.arabicName;
  }

  // Add founding date
  if (org.founded) {
    schema.foundingDate = org.founded;
  }

  // Add location
  if (org.location) {
    schema.address = {
      "@type": "PostalAddress",
      "addressCountry": "Nigeria",
      "addressRegion": org.location
    };
  }

  // Add URL
  if (org.url) {
    schema.url = org.url;
  }

  return schema;
}

/**
 * Generate structured data for the website/archive itself
 */
export function generateWebsiteSchema(): object {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Martyrs Archive",
    "alternateName": "أرشيف الشهداء",
    "description": "A comprehensive educational platform documenting martyrs from Nigeria, specifically focusing on the Islamic Movement of Nigeria (IMN) and those who sacrificed for justice and faith.",
    "url": typeof window !== 'undefined' ? window.location.origin : '',
    "keywords": ["martyrs", "archive", "Islamic Movement Nigeria", "IMN", "memorial", "historical documentation", "human rights"],
    "inLanguage": ["en", "ar"],
    "about": {
      "@type": "Thing",
      "name": "Historical Documentation",
      "description": "Documentation and preservation of martyrs' stories for educational purposes"
    },
    "audience": {
      "@type": "Audience",
      "audienceType": "Educational researchers, historians, and those interested in human rights documentation"
    },
    "license": "Educational and non-commercial use only",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${typeof window !== 'undefined' ? window.location.origin : ''}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };
}

/**
 * Generate structured data for a dataset/collection
 */
export function generateDatasetSchema(): object {
  return {
    "@context": "https://schema.org",
    "@type": "Dataset",
    "name": "Martyrs Archive Dataset",
    "description": "Educational dataset documenting martyrs from various backgrounds, with emphasis on the Islamic Movement of Nigeria (IMN).",
    "keywords": ["martyrs", "historical data", "Islamic Movement Nigeria", "human rights", "memorial"],
    "license": "Educational use only",
    "creator": {
      "@type": "Organization",
      "name": "Martyrs Archive Project"
    },
    "distribution": {
      "@type": "DataDownload",
      "encodingFormat": "application/json",
      "contentUrl": `${typeof window !== 'undefined' ? window.location.origin : ''}/api/martyrs`
    },
    "temporalCoverage": "2009/2024",
    "spatialCoverage": {
      "@type": "Place",
      "name": "Nigeria"
    }
  };
}

/**
 * Generate breadcrumb structured data
 */
export function generateBreadcrumbSchema(breadcrumbs: Array<{name: string, url: string}>): object {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
}

/**
 * Generate FAQ structured data for common questions
 */
export function generateFAQSchema(): object {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "What is the Martyrs Archive?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "The Martyrs Archive is a comprehensive educational platform documenting martyrs from Nigeria, specifically focusing on the Islamic Movement of Nigeria (IMN) and those who sacrificed for justice and faith."
        }
      },
      {
        "@type": "Question",
        "name": "Who are the Islamic Movement of Nigeria (IMN) martyrs?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "IMN martyrs are members of the Islamic Movement of Nigeria who lost their lives in various incidents while practicing their faith and seeking justice. The archive documents their stories for educational and memorial purposes."
        }
      },
      {
        "@type": "Question",
        "name": "Is this archive official or endorsed by any government?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "This is an independent educational archive created for historical documentation and memorial purposes. It is not officially endorsed by any government entity."
        }
      },
      {
        "@type": "Question",
        "name": "How can I contribute information to the archive?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "You can contribute verified information through our contact page. All submissions are reviewed for accuracy and appropriateness before inclusion in the archive."
        }
      }
    ]
  };
}

/**
 * Utility function to inject structured data into the document head
 */
export function injectStructuredData(data: object | object[]): void {
  if (typeof window === 'undefined') return;

  const scripts = Array.isArray(data) ? data : [data];
  
  scripts.forEach((script, index) => {
    const scriptElement = document.createElement('script');
    scriptElement.type = 'application/ld+json';
    scriptElement.id = `structured-data-${index}`;
    scriptElement.textContent = JSON.stringify(script);
    
    // Remove existing script with same ID if it exists
    const existing = document.getElementById(`structured-data-${index}`);
    if (existing) {
      existing.remove();
    }
    
    document.head.appendChild(scriptElement);
  });
}

/**
 * Remove structured data scripts from document head
 */
export function removeStructuredData(): void {
  if (typeof window === 'undefined') return;
  
  const scripts = document.querySelectorAll('script[type="application/ld+json"][id^="structured-data-"]');
  scripts.forEach(script => script.remove());
}