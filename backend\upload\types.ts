export interface UploadedFile {
  id: string;
  url: string;
  filename: string;
  size: number;
  mimeType: string;
  uploadedAt: string;
}

export interface UploadResponse {
  file: UploadedFile;
  success: boolean;
  message?: string;
}

export interface SignedUploadURL {
  uploadUrl: string;
  fileUrl: string;
  fields: Record<string, string>;
}

export interface FileMetadata {
  id: string;
  originalFilename: string;
  storedFilename: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: string;
  martyrId?: number;
  createdAt: string;
}