import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { Search, Filter } from 'lucide-react';
import backend from '~backend/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import MartyrCard from '../components/MartyrCard';
import SearchFilters from '../components/SearchFilters';

export default function SearchPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    subCategories: [],
    regions: [],
    periods: [],
    causes: []
  });

  // Get available filters
  const { data: filterOptions } = useQuery({
    queryKey: ['filters'],
    queryFn: () => backend.martyrs.getFilters(),
  });

  // Search martyrs
  const { data: searchResults, isLoading } = useQuery({
    queryKey: ['search', query, selectedFilters],
    queryFn: () => backend.martyrs.search({
      query: query || undefined,
      subCategories: selectedFilters.subCategories.join(',') || undefined,
      regions: selectedFilters.regions.join(',') || undefined,
      periods: selectedFilters.periods.join(',') || undefined,
      causes: selectedFilters.causes.join(',') || undefined,
      limit: 50
    }),
    enabled: true
  });

  useEffect(() => {
    const q = searchParams.get('q');
    if (q) {
      setQuery(q);
    }
  }, [searchParams]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      setSearchParams({ q: query.trim() });
    } else {
      setSearchParams({});
    }
  };

  const handleFilterChange = (type: string, value: string, checked: boolean) => {
    setSelectedFilters(prev => ({
      ...prev,
      [type]: checked
        ? [...prev[type], value]
        : prev[type].filter(item => item !== value)
    }));
  };

  const handleClearFilters = () => {
    setSelectedFilters({
      subCategories: [],
      regions: [],
      periods: [],
      causes: []
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Search Martyrs</h1>
          
          <form onSubmit={handleSearch} className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Input
                type="text"
                placeholder="Search by name, biography, or events..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pl-10 pr-4 py-3 text-lg"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            </div>
            <Button type="submit" size="lg" className="bg-green-600 hover:bg-green-700">
              Search
            </Button>
            <Button
              type="button"
              variant="outline"
              size="lg"
              onClick={() => setShowFilters(!showFilters)}
              className="md:hidden"
            >
              <Filter className="w-5 h-5" />
            </Button>
          </form>

          {/* Results Summary */}
          {searchResults && (
            <p className="text-gray-600">
              {searchResults.total} {searchResults.total === 1 ? 'result' : 'results'} found
              {query && ` for "${query}"`}
            </p>
          )}
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            {filterOptions && (
              <SearchFilters
                filters={filterOptions}
                selectedFilters={selectedFilters}
                onFilterChange={handleFilterChange}
                onClearFilters={handleClearFilters}
              />
            )}
          </div>

          {/* Search Results */}
          <div className="flex-1">
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="h-96 animate-pulse">
                    <CardContent className="p-0">
                      <div className="h-48 bg-gray-300 rounded-t-lg"></div>
                      <div className="p-4 space-y-3">
                        <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-300 rounded w-full"></div>
                        <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : searchResults?.martyrs.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Search className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No results found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search terms or filters to find what you're looking for.
                </p>
                <div className="space-y-2">
                  <p className="text-sm text-gray-500">Suggestions:</p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    <Button variant="outline" size="sm" onClick={() => setQuery('Nigeria')}>
                      Search "Nigeria"
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => setQuery('freedom')}>
                      Search "freedom"
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => setQuery('religious')}>
                      Search "religious"
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {searchResults?.martyrs.map((martyr) => (
                  <MartyrCard key={martyr.id} {...martyr} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
