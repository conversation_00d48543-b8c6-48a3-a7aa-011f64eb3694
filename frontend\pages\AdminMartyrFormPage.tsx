import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ArrowLeft, Save, Plus, Trash2, Image as ImageIcon, CalendarPlus, Loader2, Book<PERSON>pen, Quote as Quote<PERSON><PERSON>, Tag, AlertTriangle } from 'lucide-react';
import backend from '~backend/client';
import type { MartyrImage, TimelineEvent as BTimelineEvent } from '~backend/martyrs/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { ImageUploadForm } from '@/components/ImageUploadForm';
import { ImageManager } from '@/components/ImageManager';
import RichTextEditor from '@/components/RichTextEditor';
import { ValidationUtils } from '@/lib/validation';

// Validation Error Display Component
const ValidationError = ({ errors }: { errors?: string[] }) => {
  if (!errors || errors.length === 0) return null;
  return (
    <div className="mt-1 space-y-1">
      {errors.map((error, index) => (
        <div key={index} className="flex items-center gap-1 text-sm text-red-600">
          <AlertTriangle className="w-3 h-3" />
          {error}
        </div>
      ))}
    </div>
  );
};

// Input wrapper with validation
const ValidatedInput = ({ label, error, children, required = false }: { 
  label: string; 
  error?: string[]; 
  children: React.ReactNode; 
  required?: boolean;
}) => (
  <div>
    <Label className={error && error.length > 0 ? 'text-red-600' : ''}>
      {label} {required && <span className="text-red-500">*</span>}
    </Label>
    <div className={error && error.length > 0 ? 'border-red-300 rounded' : ''}>
      {children}
    </div>
    <ValidationError errors={error} />
  </div>
);

const PublishCard = ({ isLoading, handleSubmit }: { isLoading: boolean; handleSubmit: (e: React.FormEvent) => Promise<void> }) => (
  <Card>
    <CardHeader>
      <CardTitle>Publish</CardTitle>
    </CardHeader>
    <CardContent>
      <Button onClick={(e) => handleSubmit(e)} disabled={isLoading} className="w-full bg-green-600 hover:bg-green-700">
        {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Save className="w-4 h-4 mr-2" />}
        {isLoading ? 'Saving...' : 'Save Martyr'}
      </Button>
    </CardContent>
  </Card>
);

const CategoriesCard = ({ subCategories, setFormData, commonCategories }: { 
  subCategories: string[]; 
  setFormData: React.Dispatch<React.SetStateAction<any>>; 
  commonCategories: string[];
}) => {
  const [newCategory, setNewCategory] = useState('');

  const addCategory = () => {
    if (newCategory.trim() && !subCategories.includes(newCategory.trim())) {
      setFormData((prev: any) => ({ ...prev, subCategories: [...prev.subCategories, newCategory.trim()] }));
      setNewCategory('');
    }
  };

  const removeCategory = (category: string) => {
    setFormData((prev: any) => ({ ...prev, subCategories: prev.subCategories.filter((c: string) => c !== category) }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2"><Tag className="w-5 h-5" /> Categories</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          {subCategories.map((category: string) => (
            <Badge key={category} variant="secondary" className="flex items-center gap-1">
              {category}
              <button type="button" onClick={() => removeCategory(category)} className="ml-1 hover:text-red-600"><Trash2 className="w-3 h-3" /></button>
            </Badge>
          ))}
        </div>
        <div className="flex gap-2">
          <Input value={newCategory} onChange={(e) => setNewCategory(e.target.value)} placeholder="New category..." onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addCategory())} />
          <Button type="button" onClick={addCategory} variant="outline"><Plus className="w-4 h-4" /></Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {commonCategories.map((category: string) => (
            <Button key={category} type="button" variant="outline" size="sm" onClick={() => !subCategories.includes(category) && setFormData((prev: any) => ({ ...prev, subCategories: [...prev.subCategories, category] }))} disabled={subCategories.includes(category)}>{category}</Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const QuotesCard = ({ quotes, setFormData }: { 
  quotes: string[]; 
  setFormData: React.Dispatch<React.SetStateAction<any>>;
}) => {
  const [newQuote, setNewQuote] = useState('');

  const addQuote = () => {
    if (newQuote.trim()) {
      setFormData((prev: any) => ({ ...prev, quotes: [...prev.quotes, newQuote.trim()] }));
      setNewQuote('');
    }
  };

  const removeQuote = (index: number) => {
    setFormData((prev: any) => ({ ...prev, quotes: prev.quotes.filter((_: string, i: number) => i !== index) }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2"><QuoteIcon className="w-5 h-5" /> Quotes</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          {quotes.map((quote: string, index: number) => (
            <div key={index} className="flex items-start gap-2 p-2 bg-gray-50 rounded-lg text-sm">
              <p className="flex-1 italic text-gray-700">"{quote}"</p>
              <Button type="button" variant="ghost" size="icon" onClick={() => removeQuote(index)} className="text-red-600 hover:text-red-700 h-6 w-6"><Trash2 className="w-4 h-4" /></Button>
            </div>
          ))}
        </div>
        <div className="flex gap-2">
          <Input value={newQuote} onChange={(e) => setNewQuote(e.target.value)} placeholder="Add a quote..." />
          <Button type="button" onClick={addQuote} variant="outline"><Plus className="w-4 h-4" /></Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default function AdminMartyrFormPage() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const isEdit = !!slug && slug !== 'new';

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    birthDate: '',
    birthPlace: '',
    deathDate: '',
    deathPlace: '',
    martyrdomCause: '',
    martyrdomContext: '',
    bio: '',
    subCategories: [] as string[],
    region: '',
    period: '',
    quotes: [] as string[],
    familyInfo: '',
    latitude: '',
    longitude: ''
  });

  const [images, setImages] = useState<MartyrImage[]>([]);
  const [timelineEvents, setTimelineEvents] = useState<BTimelineEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});

  useEffect(() => {
    // Check authentication by trying to access a protected endpoint
    const checkAuth = async () => {
      try {
        await backend.martyrs.listMartyrs();
      } catch (error: any) {
        if (error?.message?.includes('unauthenticated')) {
          navigate('/admin/login');
        }
      }
    };
    
    checkAuth();
  }, [navigate]);

  const formatDateForInput = (date: string | Date | null | undefined): string => {
    if (!date) return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) return '';
    return dateObj.toISOString().split('T')[0];
  };

  const getAuthenticatedBackend = () => {
    // With HttpOnly cookies, we don't need to manually set the Authorization header
    // The browser will automatically send cookies with requests
    return backend;
  };

  const { data: martyrData, isFetching } = useQuery({
    queryKey: ['martyr-profile', slug],
    queryFn: () => backend.martyrs.getProfile({ slug: slug! }),
    enabled: isEdit,
  });

  useEffect(() => {
    if (martyrData && isEdit) {
      setFormData({
        name: martyrData.name,
        slug: martyrData.slug,
        birthDate: formatDateForInput(martyrData.birthDate),
        birthPlace: martyrData.birthPlace || '',
        deathDate: formatDateForInput(martyrData.deathDate),
        deathPlace: martyrData.deathPlace || '',
        martyrdomCause: martyrData.martyrdomCause || '',
        martyrdomContext: martyrData.martyrdomContext || '',
        bio: martyrData.bio || '',
        subCategories: martyrData.subCategories || [],
        region: martyrData.region || '',
        period: martyrData.period || '',
        quotes: martyrData.quotes || [],
        familyInfo: martyrData.familyInfo || '',
        latitude: martyrData.latitude ? martyrData.latitude.toString() : '',
        longitude: martyrData.longitude ? martyrData.longitude.toString() : ''
      });
      setImages(martyrData.images || []);
      setTimelineEvents(martyrData.timelineEvents || []);
    }
  }, [martyrData, isEdit]);

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
    
    // Real-time validation for critical fields
    if (field === 'slug') {
      const slugResult = ValidationUtils.validateSlug(value);
      if (!slugResult.isValid) {
        setValidationErrors(prev => ({ ...prev, slug: slugResult.errors }));
      }
    } else if (field === 'name') {
      const nameResult = ValidationUtils.validateName(value);
      if (!nameResult.isValid) {
        setValidationErrors(prev => ({ ...prev, name: nameResult.errors }));
      }
    } else if (field === 'latitude') {
      const latResult = ValidationUtils.validateLatitude(value);
      if (!latResult.isValid) {
        setValidationErrors(prev => ({ ...prev, latitude: latResult.errors }));
      }
    } else if (field === 'longitude') {
      const lngResult = ValidationUtils.validateLongitude(value);
      if (!lngResult.isValid) {
        setValidationErrors(prev => ({ ...prev, longitude: lngResult.errors }));
      }
    }
  };

  const generateSlug = (name: string) => name.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').trim();

  const handleNameChange = (name: string) => {
    setFormData(prev => ({ ...prev, name, slug: prev.slug || generateSlug(name) }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Comprehensive validation before submission
    const validation = ValidationUtils.validateMartyrForm(formData);
    if (!validation.isValid) {
      setValidationErrors(validation.fieldErrors);
      toast({ 
        title: "Validation Failed", 
        description: "Please fix the highlighted errors before submitting.", 
        variant: "destructive" 
      });
      return;
    }
    
    setIsLoading(true);
    try {
      const backend = getAuthenticatedBackend();
      const dataToSend = {
        ...formData,
        latitude: formData.latitude === '' ? null : parseFloat(formData.latitude),
        longitude: formData.longitude === '' ? null : parseFloat(formData.longitude),
      };

      if (isEdit && martyrData) {
        await backend.martyrs.updateMartyr({ id: martyrData.id, ...dataToSend });
        toast({ title: "Martyr Updated Successfully", description: "The martyr profile has been updated with respect and care." });
      } else {
        await backend.martyrs.createMartyr(dataToSend);
        toast({ title: "Martyr Created Successfully", description: "A new martyr profile has been created with dignity and honor." });
      }
      navigate('/admin');
    } catch (error: any) {
      console.error('Save error:', error);
      toast({ 
        title: "Save Failed", 
        description: error?.message || "An unexpected error occurred. Please try again.", 
        variant: "destructive" 
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isFetching) {
    return <div className="min-h-screen bg-gray-50 flex items-center justify-center"><Loader2 className="w-8 h-8 animate-spin text-green-600" /></div>;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow-sm border-b sticky top-0 z-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" onClick={() => navigate('/admin')}><ArrowLeft className="w-4 h-4 mr-2" />Back</Button>
              <h1 className="text-xl font-bold text-gray-900">{isEdit ? 'Edit Martyr' : 'Add New Martyr'}</h1>
            </div>
            <Button onClick={(e) => handleSubmit(e as any)} disabled={isLoading} className="bg-green-600 hover:bg-green-700">
              {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Save className="w-4 h-4 mr-2" />}
              {isLoading ? 'Saving...' : 'Save Martyr'}
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
          
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Core details about the martyr.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <ValidatedInput label="Full Name" error={validationErrors.name} required>
                    <Input 
                      id="name" 
                      value={formData.name} 
                      onChange={(e) => handleNameChange(e.target.value)} 
                      required 
                      className={validationErrors.name ? 'border-red-300 focus:border-red-500' : 'mt-1'} 
                      placeholder="Enter the martyr's full name"
                    />
                  </ValidatedInput>
                  <ValidatedInput label="URL Slug" error={validationErrors.slug} required>
                    <Input 
                      id="slug" 
                      value={formData.slug} 
                      onChange={(e) => handleFormChange('slug', e.target.value)} 
                      required 
                      className={validationErrors.slug ? 'border-red-300 focus:border-red-500' : 'mt-1'} 
                      placeholder="url-friendly-name"
                    />
                  </ValidatedInput>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <ValidatedInput label="Birth Date" error={validationErrors.birthDate}>
                    <Input 
                      id="birthDate" 
                      type="date" 
                      value={formData.birthDate} 
                      onChange={(e) => handleFormChange('birthDate', e.target.value)} 
                      className={validationErrors.birthDate ? 'border-red-300 focus:border-red-500' : 'mt-1'} 
                    />
                  </ValidatedInput>
                  <ValidatedInput label="Birth Place" error={validationErrors.birthPlace}>
                    <Input 
                      id="birthPlace" 
                      value={formData.birthPlace} 
                      onChange={(e) => handleFormChange('birthPlace', e.target.value)} 
                      className="mt-1" 
                      placeholder="City, Country"
                    />
                  </ValidatedInput>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <ValidatedInput label="Death Date" error={validationErrors.deathDate}>
                    <Input 
                      id="deathDate" 
                      type="date" 
                      value={formData.deathDate} 
                      onChange={(e) => handleFormChange('deathDate', e.target.value)} 
                      className={validationErrors.deathDate ? 'border-red-300 focus:border-red-500' : 'mt-1'} 
                    />
                  </ValidatedInput>
                  <ValidatedInput label="Death Place" error={validationErrors.deathPlace}>
                    <Input 
                      id="deathPlace" 
                      value={formData.deathPlace} 
                      onChange={(e) => handleFormChange('deathPlace', e.target.value)} 
                      className="mt-1" 
                      placeholder="City, Country"
                    />
                  </ValidatedInput>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <ValidatedInput label="Region" error={validationErrors.region}>
                    <Input 
                      id="region" 
                      value={formData.region} 
                      onChange={(e) => handleFormChange('region', e.target.value)} 
                      className="mt-1" 
                      placeholder="e.g., Nigeria, Middle East" 
                    />
                  </ValidatedInput>
                  <ValidatedInput label="Period" error={validationErrors.period}>
                    <Input 
                      id="period" 
                      value={formData.period} 
                      onChange={(e) => handleFormChange('period', e.target.value)} 
                      className="mt-1" 
                      placeholder="e.g., Modern Era" 
                    />
                  </ValidatedInput>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <ValidatedInput label="Latitude" error={validationErrors.latitude}>
                    <Input 
                      id="latitude" 
                      type="number" 
                      step="any" 
                      value={formData.latitude} 
                      onChange={(e) => handleFormChange('latitude', e.target.value)} 
                      className={validationErrors.latitude ? 'border-red-300 focus:border-red-500' : 'mt-1'} 
                      placeholder="e.g., 9.0765 (-90 to 90)" 
                    />
                  </ValidatedInput>
                  <ValidatedInput label="Longitude" error={validationErrors.longitude}>
                    <Input 
                      id="longitude" 
                      type="number" 
                      step="any" 
                      value={formData.longitude} 
                      onChange={(e) => handleFormChange('longitude', e.target.value)} 
                      className={validationErrors.longitude ? 'border-red-300 focus:border-red-500' : 'mt-1'} 
                      placeholder="e.g., 7.3986 (-180 to 180)" 
                    />
                  </ValidatedInput>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2"><BookOpen className="w-5 h-5"/> Biography & Martyrdom</CardTitle>
                <CardDescription>Detailed information about the martyr's life and martyrdom.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ValidatedInput label="Biography" error={validationErrors.bio} required>
                  <RichTextEditor 
                    value={formData.bio} 
                    onChange={(value: string) => handleFormChange('bio', value)} 
                  />
                  <p className="text-xs text-gray-500 mt-2">Minimum 50 characters required for meaningful content.</p>
                </ValidatedInput>
                <ValidatedInput label="Martyrdom Cause" error={validationErrors.martyrdomCause}>
                  <Input 
                    id="martyrdomCause" 
                    value={formData.martyrdomCause} 
                    onChange={(e) => handleFormChange('martyrdomCause', e.target.value)} 
                    className="mt-1" 
                    placeholder="Brief description of the cause" 
                  />
                </ValidatedInput>
                <ValidatedInput label="Martyrdom Context" error={validationErrors.martyrdomContext}>
                  <RichTextEditor 
                    value={formData.martyrdomContext} 
                    onChange={(value: string) => handleFormChange('martyrdomContext', value)} 
                  />
                </ValidatedInput>
                <ValidatedInput label="Family Information" error={validationErrors.familyInfo}>
                  <RichTextEditor 
                    value={formData.familyInfo} 
                    onChange={(value: string) => handleFormChange('familyInfo', value)} 
                  />
                  <p className="text-xs text-gray-500 mt-2">Only include with explicit consent from family members.</p>
                </ValidatedInput>
              </CardContent>
            </Card>

            {isEdit && martyrData && (
              <>
                <Card>
                  <CardHeader><CardTitle className="flex items-center gap-2"><ImageIcon className="w-5 h-5" /> Images</CardTitle></CardHeader>
                  <CardContent><ImageManager images={images} onImageDeleted={(id) => setImages(p => p.filter(i => i.id !== id))} onImageUpdated={(img) => setImages(p => p.map(i => i.id === img.id ? img : {...i, isProfileImage: false}))} /></CardContent>
                </Card>
                <Card>
                  <CardHeader><CardTitle className="flex items-center gap-2"><CalendarPlus className="w-5 h-5" /> Timeline</CardTitle></CardHeader>
                  <CardContent>{/* Timeline component goes here */}</CardContent>
                </Card>
              </>
            )}
          </div>

          {/* Right Column */}
          <div className="lg:col-span-1 space-y-8 lg:sticky top-24">
            <PublishCard isLoading={isLoading} handleSubmit={handleSubmit} />
            <CategoriesCard subCategories={formData.subCategories} setFormData={setFormData} commonCategories={["Shi'a IMN Martyrs", "Marhum", "Historical Martyrs"]} />
            <QuotesCard quotes={formData.quotes} setFormData={setFormData} />
            {isEdit && martyrData && <Card><CardHeader><CardTitle>Upload Images</CardTitle></CardHeader><CardContent><ImageUploadForm martyrId={martyrData.id} onImageAdded={(img) => setImages(p => [img, ...p.map(i => ({...i, isProfileImage: false}))])} /></CardContent></Card>}
          </div>

        </form>
      </main>
    </div>
  );
}