import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import backend from '~backend/client';
import type { MartyrMapData } from '../types/mapTypes';

interface MapDataResponse {
  martyrs: {
    name: string;
    slug: string;
    latitude: number;
    longitude: number;
  }[];
}

export const useMarkerData = () => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['map-data'],
    queryFn: (): Promise<MapDataResponse> => backend.martyrs.getMapData(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Transform data to GeoJSON format for OpenLayers
  const geoJsonData = useMemo(() => {
    if (!data?.martyrs) return null;

    return {
      type: 'FeatureCollection' as const,
      features: data.martyrs.map(martyr => ({
        type: 'Feature' as const,
        geometry: {
          type: 'Point' as const,
          coordinates: [martyr.longitude, martyr.latitude],
        },
        properties: {
          name: martyr.name,
          slug: martyr.slug,
          latitude: martyr.latitude,
          longitude: martyr.longitude,
        } as MartyrMapData,
      })),
    };
  }, [data]);

  // Also provide the raw markers data
  const markers = useMemo(() => {
    if (!data?.martyrs) return [];
    
    return data.martyrs.map(martyr => ({
      name: martyr.name,
      slug: martyr.slug,
      latitude: martyr.latitude,
      longitude: martyr.longitude,
    })) as MartyrMapData[];
  }, [data]);

  return {
    markers,
    geoJsonData,
    isLoading,
    error,
    hasData: markers.length > 0,
  };
};