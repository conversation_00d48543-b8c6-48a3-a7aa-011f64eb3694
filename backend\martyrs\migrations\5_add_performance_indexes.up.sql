-- Add performance optimization indexes for martyr system
-- This migration adds missing indexes identified in security audit

-- Index for martyr_images profile image queries
CREATE INDEX idx_martyr_images_profile_lookup ON martyr_images(martyr_id, is_profile_image);

-- Index for geographic location queries
CREATE INDEX idx_martyrs_location ON martyrs(latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Index for martyr_images by profile status
CREATE INDEX idx_martyr_images_profile_only ON martyr_images(is_profile_image) WHERE is_profile_image = true;

-- Composite index for admin list queries with health scoring
CREATE INDEX idx_martyrs_admin_list ON martyrs(updated_at DESC, id);

-- Index for better text search performance
CREATE INDEX idx_martyrs_text_search ON martyrs USING gin(to_tsvector('english', name || ' ' || bio));