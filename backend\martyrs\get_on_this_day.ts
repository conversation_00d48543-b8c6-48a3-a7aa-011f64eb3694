
import { api } from "encore.dev/api";
import { martyrsDB } from "./db";
import type { MartyrCard } from "./types";

interface OnThisDayResponse {
  born: MartyrCard[];
  martyred: Martyr<PERSON><PERSON>[];
}

// Gets martyrs born or martyred on the current date.
export const getOnThisDay = api({ expose: true, method: 'GET', path: '/martyrs/on-this-day' }, async (): Promise<OnThisDayResponse> => {
  const today = new Date();
  const month = today.getMonth() + 1;
  const day = today.getDate();

  const bornQuery = `
    SELECT 
      m.id,
      m.name,
      m.slug,
      LEFT(m.bio, 150) as bio,
      m.sub_categories,
      m.region,
      m.view_count,
      mi.url as profile_image
    FROM martyrs m
    LEFT JOIN martyr_images mi ON m.id = mi.martyr_id AND mi.is_profile_image = true
    WHERE EXTRACT(MONTH FROM m.birth_date) = ${month} AND EXTRACT(DAY FROM m.birth_date) = ${day}
    ORDER BY m.birth_date DESC
    LIMIT 5
  `;

  const martyredQuery = `
    SELECT 
      m.id,
      m.name,
      m.slug,
      LEFT(m.bio, 150) as bio,
      m.sub_categories,
      m.region,
      m.view_count,
      mi.url as profile_image
    FROM martyrs m
    LEFT JOIN martyr_images mi ON m.id = mi.martyr_id AND mi.is_profile_image = true
    WHERE EXTRACT(MONTH FROM m.death_date) = ${month} AND EXTRACT(DAY FROM m.death_date) = ${day}
    ORDER BY m.death_date DESC
    LIMIT 5
  `;

  const bornPromise = martyrsDB.rawQueryAll<any>(bornQuery);
  const martyredPromise = martyrsDB.rawQueryAll<any>(martyredQuery);

  const [bornResult, martyredResult] = await Promise.all([
    bornPromise,
    martyredPromise,
  ]);

  const mapToMartyrCard = (m: any): MartyrCard => ({
    id: m.id,
    name: m.name,
    slug: m.slug,
    bio: m.bio,
    subCategories: m.sub_categories || [],
    region: m.region,
    viewCount: m.view_count,
    profileImage: m.profile_image,
  });

  return {
    born: bornResult.map(mapToMartyrCard),
    martyred: martyredResult.map(mapToMartyrCard),
  };
});
