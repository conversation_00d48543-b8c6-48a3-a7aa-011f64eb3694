import React from 'react';
import { Heart, Plus } from 'lucide-react';
import { motion } from 'framer-motion';
import animationPatterns from '../lib/animationPatterns';
import { focusRing } from '../lib/designTokens';

interface ContributeButtonProps {
  onClick: () => void;
}

export default function ContributeButton({ onClick }: ContributeButtonProps) {
  return (
    <motion.button
      onClick={onClick}
      className={`fixed bottom-6 right-6 z-40 w-14 h-14 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group ${focusRing.white}`}
      initial={{ scale: 0, rotate: -180 }}
      animate={{ scale: 1, rotate: 0 }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      transition={animationPatterns.springs.bouncy}
      aria-label="Open contribute to archive modal"
      title="Contribute to the Archive"
    >
      {/* Pulsing background effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-full"
        animate={{ 
          scale: [1, 1.2, 1],
          opacity: [0.7, 0.3, 0.7]
        }}
        transition={{ 
          duration: 2, 
          repeat: Infinity, 
          ease: "easeInOut" 
        }}
      />
      
      {/* Icon container */}
      <div className="relative z-10 flex items-center justify-center">
        <motion.div
          animate={{ rotate: [0, 5, -5, 0] }}
          transition={{ 
            duration: 3, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }}
        >
          <Heart className="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="currentColor" />
        </motion.div>
        
        {/* Plus icon overlay for "add/contribute" meaning */}
        <motion.div
          className="absolute -top-1 -right-1 w-4 h-4 bg-white rounded-full flex items-center justify-center"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, ...animationPatterns.springs.bouncy }}
        >
          <Plus className="w-3 h-3 text-emerald-600" strokeWidth={3} />
        </motion.div>
      </div>
    </motion.button>
  );
}
