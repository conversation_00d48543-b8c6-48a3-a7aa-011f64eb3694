import { api, Query, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";
import type { MartyrCard } from "./types";

interface AdminListRequest {
  page?: Query<number>;
  limit?: Query<number>;
  search?: Query<string>;
}

interface AdminListResponse {
  martyrs: (MartyrCard & { contentHealth: number })[];
  total: number;
  averageContentHealth: number;
  pagination: {
    currentPage: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

// Lists all martyrs for admin management.
export const listMartyrs = api<AdminListRequest, AdminListResponse>(
  { auth: true, expose: true, method: "GET", path: "/admin/martyrs" },
  async ({ page = 1, limit = 20, search = "" }) => {
    const auth = getAuthData()!;
    if (auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate pagination parameters
    const validPage = Math.max(1, page || 1);
    const validLimit = Math.min(100, Math.max(1, limit || 20));
    const offset = (validPage - 1) * validLimit;
    const searchTerm = search?.trim() || "";

    const queryParams: any[] = [];
    let whereClause = "WHERE 1=1";
    let paramIndex = 1;

    if (searchTerm) {
      whereClause += ` AND (name ILIKE $${paramIndex} OR bio ILIKE $${paramIndex})`;
      queryParams.push(`%${searchTerm}%`);
      paramIndex++;
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM martyrs
      ${whereClause}
    `;
    const countResult = await martyrsDB.rawQueryRow<{ total: number }>(countQuery, ...queryParams);
    const total = Number(countResult?.total || 0);

    // Get martyrs with health scoring
    const searchQuery = `
      SELECT 
        m.id,
        m.name,
        m.slug,
        LEFT(m.bio, 150) as bio,
        m.sub_categories,
        m.region,
        m.view_count,
        mi.url as profile_image,
        LENGTH(m.bio) as bio_length,
        (mi.id IS NOT NULL) as has_profile_image
      FROM martyrs m
      LEFT JOIN martyr_images mi ON m.id = mi.martyr_id AND mi.is_profile_image = true
      ${whereClause}
      ORDER BY m.updated_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    queryParams.push(validLimit, offset);

    const martyrs = await martyrsDB.rawQueryAll<any>(searchQuery, ...queryParams);

    let totalContentHealth = 0;
    const martyrsWithHealth = martyrs.map((m: any) => {
      let health = 0;
      if (m.bio_length > 100) health += 0.5;
      if (m.has_profile_image) health += 0.5;
      totalContentHealth += health;

      return {
        id: m.id,
        name: m.name,
        slug: m.slug,
        bio: m.bio,
        subCategories: m.sub_categories || [],
        region: m.region,
        viewCount: m.view_count,
        profileImage: m.profile_image,
        contentHealth: health,
      };
    });

    const totalPages = Math.ceil(total / validLimit);

    return {
      martyrs: martyrsWithHealth,
      total,
      averageContentHealth: total > 0 ? totalContentHealth / total : 0,
      pagination: {
        currentPage: validPage,
        totalPages,
        hasNext: validPage < totalPages,
        hasPrevious: validPage > 1,
      },
    };
  }
);