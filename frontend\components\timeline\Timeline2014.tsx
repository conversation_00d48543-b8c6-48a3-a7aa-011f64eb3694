import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, MapPin, Users, Heart, ChevronDown, ChevronUp, ExternalLink } from 'lucide-react';
import { getEventsByYear, getTotalCasualtiesByYear } from './timelineData';
import { IslamicGeometricPattern, IslamicCalligraphyBorder } from '../IslamicPatterns';

interface Timeline2014Props {
  className?: string;
}

export default function Timeline2014({ className = '' }: Timeline2014Props) {
  const [expandedEvent, setExpandedEvent] = useState<string | null>(null);
  const events = getEventsByYear(2014);
  const yearStats = getTotalCasualtiesByYear(2014);

  const toggleEventExpansion = (eventId: string) => {
    setExpandedEvent(expandedEvent === eventId ? null : eventId);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <IslamicGeometricPattern />
      </div>

      <div className="relative">
        {/* Year Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-red-600 to-red-800 rounded-full mb-4 shadow-lg">
            <span className="text-3xl font-bold text-white">2014</span>
          </div>
          <h2 className="text-3xl font-bold text-slate-800 mb-2">
            The Zaria Quds Day Massacre
          </h2>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
            A tragic turning point in IMN history, marking the first major military assault on peaceful protesters
            and the beginning of systematic persecution.
          </p>
        </motion.div>

        {/* Year Statistics */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-gradient-to-r from-red-50 to-red-100 rounded-xl p-6 mb-8 border border-red-200"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mb-2">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-red-800">{yearStats.killed}</span>
              <span className="text-sm text-red-600">Martyrs</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-orange-800">{yearStats.injured}</span>
              <span className="text-sm text-orange-600">Injured</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-slate-600 rounded-full flex items-center justify-center mb-2">
                <Users className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-slate-800">{yearStats.arrested}</span>
              <span className="text-sm text-slate-600">Arrested</span>
            </div>
          </div>
        </motion.div>

        {/* Timeline Events */}
        <div className="space-y-6">
          {events.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              className="relative"
            >
              {/* Timeline Line */}
              <div className="absolute left-6 top-16 bottom-0 w-0.5 bg-gradient-to-b from-red-400 to-red-600"></div>
              
              {/* Event Card */}
              <div className="relative bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden">
                <IslamicCalligraphyBorder />
                
                {/* Event Header */}
                <div className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* Date Circle */}
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-red-600 to-red-800 rounded-full flex items-center justify-center shadow-lg">
                      <Calendar className="w-6 h-6 text-white" />
                    </div>
                    
                    {/* Event Info */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-xl font-bold text-slate-800">{event.title}</h3>
                        <button
                          onClick={() => toggleEventExpansion(event.id)}
                          className="p-2 hover:bg-slate-100 rounded-lg transition-colors duration-200"
                          aria-label={expandedEvent === event.id ? "Collapse details" : "Expand details"}
                        >
                          {expandedEvent === event.id ? (
                            <ChevronUp className="w-5 h-5 text-slate-600" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-slate-600" />
                          )}
                        </button>
                      </div>
                      
                      {event.arabicTitle && (
                        <p className="text-lg text-emerald-700 font-arabic mb-2" dir="rtl">
                          {event.arabicTitle}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4 text-sm text-slate-600 mb-3">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(event.date).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{event.location}</span>
                        </div>
                      </div>
                      
                      {/* Casualties Summary */}
                      <div className="flex items-center space-x-6 text-sm">
                        <div className="flex items-center space-x-1 text-red-600">
                          <Heart className="w-4 h-4" />
                          <span className="font-semibold">{event.casualties.killed} martyrs</span>
                        </div>
                        <div className="flex items-center space-x-1 text-orange-600">
                          <Users className="w-4 h-4" />
                          <span className="font-semibold">{event.casualties.injured} injured</span>
                        </div>
                        {event.casualties.arrested && (
                          <div className="flex items-center space-x-1 text-slate-600">
                            <Users className="w-4 h-4" />
                            <span className="font-semibold">{event.casualties.arrested} arrested</span>
                          </div>
                        )}
                      </div>
                      
                      <p className="text-slate-700 mt-3 leading-relaxed">
                        {event.description}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                <AnimatePresence>
                  {expandedEvent === event.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-slate-200 bg-slate-50"
                    >
                      <div className="p-6 space-y-4">
                        {/* Context */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Context</h4>
                          <p className="text-slate-700 leading-relaxed">{event.context}</p>
                        </div>
                        
                        {/* Significance */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Historical Significance</h4>
                          <p className="text-slate-700 leading-relaxed">{event.significance}</p>
                        </div>
                        
                        {/* Notable Martyrs */}
                        {event.notableMartyrs && event.notableMartyrs.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-slate-800 mb-2">Notable Martyrs</h4>
                            <div className="bg-white rounded-lg p-4 border border-slate-200">
                              <ul className="space-y-2">
                                {event.notableMartyrs.map((martyr, idx) => (
                                  <li key={idx} className="flex items-center space-x-2">
                                    <Heart className="w-4 h-4 text-red-500 flex-shrink-0" />
                                    <span className="text-slate-700">{martyr}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        )}
                        
                        {/* Sources */}
                        <div>
                          <h4 className="font-semibold text-slate-800 mb-2">Sources</h4>
                          <ul className="space-y-1">
                            {event.sources.map((source, idx) => (
                              <li key={idx} className="flex items-center space-x-2 text-sm text-slate-600">
                                <ExternalLink className="w-3 h-3 flex-shrink-0" />
                                <span>{source}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
