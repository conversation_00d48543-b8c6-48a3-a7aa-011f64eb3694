# Martyr Website Migration Plan: Encore.dev to Appwrite Cloud

## Overview
Complete migration from Encore.dev backend + React frontend to React + Appwrite Cloud BaaS solution. This migration eliminates custom backend code and leverages Appwrite's native services for all backend functionality.

## Strategic Approach
- **Backend Strategy**: Complete deletion of `backend/` directory - no code preservation
- **Frontend Strategy**: Maintain React architecture while replacing all API integrations
- **Tools**: Mandatory use of Appwrite MCP tools for all backend setup
- **Research**: Web research required for latest Appwrite documentation and patterns

## Current Architecture Analysis
Based on comprehensive codebase analysis:

### Backend (To Be Deleted)
- **Framework**: Encore.dev 1.49.1+ with TypeScript
- **Services**: 3 services (auth, martyrs, upload)
- **Database**: PostgreSQL with migrations
- **Authentication**: JWT with HttpOnly cookies
- **File Upload**: Dual approach (direct + signed URLs)

### Frontend (To Be Preserved & Modified)
- **Framework**: React 19.1.1+ with TypeScript
- **State Management**: TanStack Query 5.85.3+
- **Routing**: React Router DOM 7.8.1+
- **Styling**: Tailwind CSS 4.1.12+ with custom design tokens
- **UI Components**: Radix UI primitives
- **Rich Text**: TipTap 3.3.0+
- **Maps**: OpenLayers 8.2.0+
- **Animations**: Framer Motion 12.23.12+

## Database Schema Documentation (From Current PostgreSQL)
*Note: This schema will be recreated as Appwrite collections*

### Martyrs Table Structure:
- `id` (integer, primary key)
- `name` (varchar, required)
- `slug` (varchar, unique, required)
- `bio` (text)
- `birth_date` (date)
- `birth_place` (varchar)
- `death_date` (date)
- `death_place` (varchar)
- `martyrdom_cause` (varchar)
- `martyrdom_context` (text)
- `sub_categories` (text array)
- `region` (varchar)
- `period` (varchar)
- `quotes` (text array)
- `family_info` (text)
- `latitude` (decimal)
- `longitude` (decimal)
- `created_at` (timestamp)
- `updated_at` (timestamp)

### Images Table Structure:
- `id` (integer, primary key)
- `martyr_id` (integer, foreign key)
- `url` (varchar, required)
- `caption` (text)
- `credit` (varchar)
- `is_profile_image` (boolean)
- `created_at` (timestamp)

### Timeline Events Table Structure:
- `id` (integer, primary key)
- `martyr_id` (integer, foreign key)
- `title` (varchar, required)
- `description` (text)
- `date` (date, required)
- `event_type` (varchar)
- `created_at` (timestamp)

## Migration Phases

### Phase 1: Pre-Migration Setup & Backend Deletion
**Objective**: Prepare for migration and remove Encore.dev backend
**Duration**: 1-2 hours
**Dependencies**: None

#### Task 1.1: Create Migration Documentation Structure
**Rules**:
- Create `migrate-changes.md` file for tracking all changes
- Document current state before any modifications
- Set up progress tracking system

**Instructions**:
1. Create `migrate-changes.md` file in project root
2. Document current project structure
3. List all files that will be modified during migration
4. Create template for tracking completed tasks

**Validation**: 
- `migrate-changes.md` file exists
- Current project structure documented
- Progress tracking template ready

**Files to Create**: `migrate-changes.md`

#### Task 1.2: Backend Directory Deletion
**Rules**:
- Complete deletion of `backend/` directory
- No preservation of any backend files
- Update project documentation

**Instructions**:
1. Delete entire `backend/` directory and all contents
2. Update `README.md` to reflect new architecture
3. Remove backend-related scripts from `package.json` (if any)
4. Document deletion in `migrate-changes.md`

**Dependencies**: Task 1.1 completed
**Validation**: 
- `backend/` directory no longer exists
- No backend references in project files
- Documentation updated

**Files to Delete**: `backend/` (entire directory)
**Files to Modify**: `README.md`, `package.json` (if needed)

#### Task 1.3: Research Latest Appwrite Documentation
**Rules**:
- Mandatory web research on current Appwrite features
- Focus on React SDK implementation patterns
- Document findings for developer reference

**Instructions**:
1. Research Appwrite React SDK latest version and features
2. Study Appwrite Database collections and relationships
3. Review Appwrite Authentication patterns for React
4. Investigate Appwrite Storage for file uploads
5. Document findings in `migrate-changes.md`

**Dependencies**: Task 1.2 completed
**Web Research Required**: 
- Appwrite official documentation
- React SDK implementation guides
- Best practices for Appwrite + React integration

**Validation**: 
- Research findings documented
- Latest SDK version identified
- Implementation patterns understood

---

### Phase 2: Appwrite Backend Setup
**Objective**: Create and configure complete Appwrite backend using MCP tools
**Duration**: 2-3 hours
**Dependencies**: Phase 1 completed

#### Task 2.1: Appwrite Project Creation
**Rules**:
- Use Appwrite MCP tools exclusively
- No manual Appwrite console configuration
- Document all MCP tool commands used

**Instructions**:
1. Use Appwrite MCP tool to create new project
2. Configure project settings (name: "Martyr Biography Website")
3. Set up project regions and preferences
4. Document project ID and configuration in `migrate-changes.md`

**MCP Tools Required**: `databases_create_appwrite`
**Dependencies**: Phase 1 completed
**Validation**: 
- Appwrite project created successfully
- Project ID documented
- MCP commands logged

#### Task 2.2: Database Collections Setup
**Rules**:
- Create collections using MCP tools only
- Replicate PostgreSQL schema as Appwrite collections
- Set up proper permissions and security

**Instructions**:
1. Create "martyrs" collection with all attributes
2. Create "images" collection with relationship to martyrs
3. Create "timeline_events" collection with relationship to martyrs
4. Configure collection permissions for admin access
5. Document all collection IDs and structures

**MCP Tools Required**: 
- `databases_create_collection_appwrite`
- `databases_create_string_attribute_appwrite`
- `databases_create_datetime_attribute_appwrite`
- `databases_create_integer_attribute_appwrite`
- `databases_create_float_attribute_appwrite`
- `databases_create_boolean_attribute_appwrite`
- `databases_create_relationship_attribute_appwrite`

**Dependencies**: Task 2.1 completed
**Validation**: 
- All collections created with correct attributes
- Relationships properly configured
- Permissions set correctly

#### Task 2.3: Authentication Service Configuration
**Rules**:
- Configure Appwrite Auth using MCP tools
- Set up admin user management
- Configure security settings

**Instructions**:
1. Configure authentication methods (email/password)
2. Set up admin user creation capabilities
3. Configure session management settings
4. Set up security rules and rate limiting
5. Document authentication configuration

**MCP Tools Required**: 
- `users_create_appwrite`
- Authentication configuration tools

**Dependencies**: Task 2.2 completed
**Validation**: 
- Authentication service configured
- Admin user creation possible
- Security settings applied

#### Task 2.4: Storage Buckets Creation
**Rules**:
- Create storage buckets using MCP tools
- Configure file upload permissions
- Set up image processing capabilities

**Instructions**:
1. Create "martyr-images" storage bucket
2. Configure file type restrictions (images only)
3. Set up file size limits and security rules
4. Configure image transformation settings
5. Document bucket configuration

**MCP Tools Required**: 
- `storage_create_bucket_appwrite`

**Dependencies**: Task 2.3 completed
**Validation**: 
- Storage bucket created successfully
- File restrictions configured
- Security rules applied

#### Task 2.5: Indexes and Performance Optimization
**Rules**:
- Create database indexes using MCP tools
- Optimize for search and filtering operations
- Configure performance settings

**Instructions**:
1. Create indexes for martyr search operations
2. Set up indexes for filtering by categories, regions, periods
3. Create indexes for geographic queries (lat/lng)
4. Configure full-text search indexes
5. Document all indexes created

**MCP Tools Required**: 
- `databases_create_index_appwrite`

**Dependencies**: Task 2.4 completed
**Validation**: 
- All necessary indexes created
- Search performance optimized
- Index configuration documented

---

### Phase 3: Frontend SDK Integration
**Objective**: Replace Encore.dev client with Appwrite SDK in frontend
**Duration**: 3-4 hours
**Dependencies**: Phase 2 completed

#### Task 3.1: Appwrite SDK Installation and Configuration
**Rules**:
- Install latest Appwrite React SDK
- Replace existing client configuration
- Maintain TypeScript support

**Instructions**:
1. Install Appwrite SDK: `npm install appwrite`
2. Remove Encore.dev client dependencies
3. Create new `frontend/lib/appwrite.ts` configuration file
4. Set up Appwrite client with project configuration
5. Update `migrate-changes.md` with SDK installation details

**Dependencies**: Phase 2 completed
**Files to Create**: `frontend/lib/appwrite.ts`
**Files to Modify**: `package.json`, `frontend/client.ts` (replace entirely)
**Validation**: 
- Appwrite SDK installed successfully
- Client configuration working
- TypeScript types available

#### Task 3.2: Authentication System Migration
**Rules**:
- Replace all Encore.dev auth calls with Appwrite Auth
- Maintain existing authentication flow UX
- Update admin login functionality

**Instructions**:
1. Update `frontend/pages/AdminLoginPage.tsx` to use Appwrite Auth
2. Replace JWT cookie authentication with Appwrite sessions
3. Update authentication guards and protected routes
4. Modify logout functionality
5. Test authentication flow thoroughly

**Dependencies**: Task 3.1 completed
**Files to Modify**: 
- `frontend/pages/AdminLoginPage.tsx`
- `frontend/components/ProtectedRoute.tsx` (if exists)
- Authentication-related utilities

**Web Research Required**: Appwrite React authentication patterns
**Validation**: 
- Admin login working with Appwrite
- Session management functional
- Protected routes working

#### Task 3.3: Database Operations Migration
**Rules**:
- Replace all Encore.dev database calls with Appwrite Database SDK
- Maintain existing data structures and relationships
- Update CRUD operations

**Instructions**:
1. Update martyr CRUD operations in `AdminMartyrFormPage.tsx`
2. Replace search functionality to use Appwrite queries
3. Update filtering and pagination logic
4. Migrate timeline events operations
5. Update all database-related API calls

**Dependencies**: Task 3.2 completed
**Files to Modify**: 
- `frontend/pages/AdminMartyrFormPage.tsx`
- `frontend/pages/AdminDashboardPage.tsx`
- `frontend/pages/SearchPage.tsx`
- `frontend/components/SearchFilters.tsx`

**Web Research Required**: Appwrite Database query syntax and patterns
**Validation**: 
- All CRUD operations working
- Search and filtering functional
- Data relationships maintained

#### Task 3.4: File Upload System Migration
**Rules**:
- Replace Encore.dev upload service with Appwrite Storage
- Maintain existing upload UX and functionality
- Update image management capabilities

**Instructions**:
1. Update `frontend/components/FileUpload.tsx` to use Appwrite Storage
2. Modify `frontend/components/ImageUploadForm.tsx` for Appwrite integration
3. Update `frontend/components/ImageManager.tsx` for Appwrite file operations
4. Replace file URL generation with Appwrite Storage URLs
5. Update image deletion and management operations

**Dependencies**: Task 3.3 completed
**Files to Modify**:
- `frontend/components/FileUpload.tsx`
- `frontend/components/ImageUploadForm.tsx`
- `frontend/components/ImageManager.tsx`

**Web Research Required**: Appwrite Storage SDK for React file uploads
**Validation**:
- File uploads working with Appwrite Storage
- Image management functional
- File URLs generating correctly

#### Task 3.5: Maps and Geographic Data Migration
**Rules**:
- Update geographic data queries for Appwrite
- Maintain existing map functionality
- Optimize for Appwrite query patterns

**Instructions**:
1. Update `frontend/components/maps/hooks/useMarkerData.ts` for Appwrite queries
2. Modify geographic data fetching in map components
3. Update coordinate-based filtering and search
4. Test map marker display and interactions
5. Verify geographic query performance

**Dependencies**: Task 3.4 completed
**Files to Modify**:
- `frontend/components/maps/hooks/useMarkerData.ts`
- `frontend/components/maps/MapContainer.tsx`

**Validation**:
- Map data loading from Appwrite
- Geographic queries working
- Map interactions functional

---

### Phase 4: Admin Interface Complete Migration
**Objective**: Fully migrate all admin interface functionality to Appwrite
**Duration**: 4-5 hours
**Dependencies**: Phase 3 completed

#### Task 4.1: Admin Dashboard Analytics Migration
**Rules**:
- Replace all analytics API calls with Appwrite Database queries
- Maintain existing dashboard functionality
- Optimize query performance

**Instructions**:
1. Update `frontend/pages/AdminDashboardPage.tsx` analytics queries
2. Replace statistics calculations with Appwrite aggregation queries
3. Update chart data fetching for dashboard visualizations
4. Modify content health monitoring to use Appwrite data
5. Test all dashboard metrics and displays

**Dependencies**: Phase 3 completed
**Files to Modify**:
- `frontend/pages/AdminDashboardPage.tsx`
- Dashboard-related components

**Web Research Required**: Appwrite Database aggregation and analytics queries
**Validation**:
- Dashboard analytics working
- Statistics displaying correctly
- Performance acceptable

#### Task 4.2: Advanced Search and Filtering Migration
**Rules**:
- Implement complex search using Appwrite Query syntax
- Maintain all existing filter capabilities
- Optimize search performance

**Instructions**:
1. Update search functionality in `frontend/pages/SearchPage.tsx`
2. Migrate filter logic in `frontend/components/SearchFilters.tsx`
3. Implement full-text search using Appwrite capabilities
4. Update category, region, and period filtering
5. Test search performance and accuracy

**Dependencies**: Task 4.1 completed
**Files to Modify**:
- `frontend/pages/SearchPage.tsx`
- `frontend/components/SearchFilters.tsx`

**Web Research Required**: Appwrite full-text search and complex query patterns
**Validation**:
- Search functionality working
- All filters operational
- Search results accurate

#### Task 4.3: Form Validation and Submission Migration
**Rules**:
- Update all form submissions to use Appwrite Database
- Maintain existing validation logic
- Ensure data integrity

**Instructions**:
1. Update form submission logic in admin components
2. Verify validation rules work with Appwrite data types
3. Update error handling for Appwrite-specific errors
4. Test all form validations and submissions
5. Update success/error messaging

**Dependencies**: Task 4.2 completed
**Files to Modify**:
- `frontend/lib/validation.ts`
- All admin form components

**Validation**:
- Form submissions working
- Validation rules functional
- Error handling appropriate

#### Task 4.4: Real-time Features Implementation (Optional Enhancement)
**Rules**:
- Implement Appwrite real-time subscriptions where beneficial
- Enhance user experience with live updates
- Maintain performance standards

**Instructions**:
1. Research Appwrite real-time capabilities
2. Implement real-time updates for admin dashboard
3. Add live notifications for content changes
4. Test real-time functionality
5. Document real-time features added

**Dependencies**: Task 4.3 completed
**Web Research Required**: Appwrite real-time subscriptions and React integration
**Validation**:
- Real-time features working (if implemented)
- Performance impact acceptable
- User experience enhanced

---

### Phase 5: Testing and Validation
**Objective**: Comprehensive testing of all migrated functionality
**Duration**: 2-3 hours
**Dependencies**: Phase 4 completed

#### Task 5.1: Authentication Flow Testing
**Rules**:
- Test all authentication scenarios thoroughly
- Verify security measures are working
- Validate session management

**Instructions**:
1. Test admin login with valid credentials
2. Test login with invalid credentials
3. Verify session persistence and expiration
4. Test logout functionality
5. Verify protected route access controls

**Dependencies**: Phase 4 completed
**Validation Criteria**:
- All authentication flows working correctly
- Security measures functional
- No authentication bypasses possible

#### Task 5.2: CRUD Operations Testing
**Rules**:
- Test all Create, Read, Update, Delete operations
- Verify data integrity and relationships
- Test error handling

**Instructions**:
1. Test martyr profile creation with all fields
2. Test martyr profile editing and updates
3. Test martyr profile deletion
4. Verify image uploads and management
5. Test timeline event operations

**Dependencies**: Task 5.1 completed
**Validation Criteria**:
- All CRUD operations working
- Data relationships maintained
- Error handling appropriate

#### Task 5.3: Search and Performance Testing
**Rules**:
- Test all search and filtering capabilities
- Verify performance meets requirements
- Test with various data volumes

**Instructions**:
1. Test search functionality with various queries
2. Test all filter combinations
3. Verify search result accuracy
4. Test pagination and sorting
5. Measure and document performance metrics

**Dependencies**: Task 5.2 completed
**Validation Criteria**:
- Search accuracy 100%
- Filter combinations working
- Performance acceptable (< 2s response times)

#### Task 5.4: File Upload and Storage Testing
**Rules**:
- Test all file upload scenarios
- Verify file security and access controls
- Test file management operations

**Instructions**:
1. Test single and multiple file uploads
2. Test file type and size restrictions
3. Verify file URL generation and access
4. Test file deletion and management
5. Test image display and transformations

**Dependencies**: Task 5.3 completed
**Validation Criteria**:
- File uploads working reliably
- Security restrictions enforced
- File management functional

#### Task 5.5: Cross-browser and Responsive Testing
**Rules**:
- Test on multiple browsers and devices
- Verify responsive design maintained
- Test mobile functionality

**Instructions**:
1. Test on Chrome, Firefox, Safari, Edge
2. Test on mobile devices (iOS/Android)
3. Verify responsive design at all breakpoints
4. Test touch interactions on mobile
5. Document any browser-specific issues

**Dependencies**: Task 5.4 completed
**Validation Criteria**:
- Functionality working across all browsers
- Responsive design maintained
- Mobile experience optimal

---

### Phase 6: Documentation and Cleanup
**Objective**: Complete project documentation and final cleanup
**Duration**: 1-2 hours
**Dependencies**: Phase 5 completed

#### Task 6.1: Update Project Documentation
**Rules**:
- Update all project documentation to reflect new architecture
- Remove all references to Encore.dev
- Document Appwrite configuration

**Instructions**:
1. Update `README.md` with new setup instructions
2. Document Appwrite configuration requirements
3. Update development workflow documentation
4. Create deployment instructions for new architecture
5. Update API documentation to reflect Appwrite usage

**Dependencies**: Phase 5 completed
**Files to Modify**:
- `README.md`
- Any other documentation files

**Validation**:
- Documentation accurate and complete
- Setup instructions working
- No outdated references

#### Task 6.2: Environment Configuration
**Rules**:
- Set up proper environment variables for Appwrite
- Configure production and development environments
- Secure API keys and sensitive configuration

**Instructions**:
1. Create `.env.example` with Appwrite configuration template
2. Update environment variable usage in code
3. Document environment setup requirements
4. Configure production environment variables
5. Test environment configuration

**Dependencies**: Task 6.1 completed
**Files to Create**: `.env.example`
**Files to Modify**: Environment configuration files

**Validation**:
- Environment configuration working
- Production setup documented
- Security best practices followed

#### Task 6.3: Final Migration Documentation
**Rules**:
- Complete all migration documentation
- Mark all tasks as completed
- Create migration summary

**Instructions**:
1. Update `migrate-changes.md` with final status
2. Mark all migration tasks as completed in `migration.md`
3. Create migration summary with lessons learned
4. Document any issues encountered and resolutions
5. Archive migration documentation

**Dependencies**: Task 6.2 completed
**Files to Modify**:
- `migrate-changes.md`
- `migration.md`

**Validation**:
- All documentation complete
- Migration fully documented
- Project ready for production

---

## Success Criteria

### Technical Success Criteria
- [ ] All backend functionality migrated to Appwrite
- [ ] No custom backend code remaining
- [ ] All admin interface functionality working
- [ ] Authentication and authorization working
- [ ] File uploads and storage working
- [ ] Search and filtering working
- [ ] Performance meets or exceeds current system
- [ ] All tests passing

### Documentation Success Criteria
- [ ] Complete migration documentation
- [ ] Updated project setup instructions
- [ ] Environment configuration documented
- [ ] Deployment instructions updated
- [ ] API usage documented

### User Experience Success Criteria
- [ ] No degradation in user experience
- [ ] All existing features functional
- [ ] Performance maintained or improved
- [ ] Mobile experience preserved
- [ ] Admin interface fully functional

## Risk Mitigation

### High-Risk Areas
1. **Authentication Migration**: Critical for admin access
2. **File Upload System**: Complex integration with storage
3. **Search Functionality**: Performance-critical feature
4. **Data Relationships**: Must maintain referential integrity

### Mitigation Strategies
1. **Incremental Testing**: Test each component after migration
2. **Rollback Plan**: Keep migration documentation for reversal if needed
3. **Performance Monitoring**: Monitor response times throughout migration
4. **User Acceptance Testing**: Verify all user workflows work correctly

## Post-Migration Enhancements

### Immediate Opportunities
1. **Real-time Features**: Leverage Appwrite's real-time capabilities
2. **Advanced Authentication**: Add social login options
3. **Enhanced Search**: Implement full-text search improvements
4. **File Processing**: Use Appwrite's image transformation features

### Future Considerations
1. **Multi-language Support**: Leverage Appwrite's localization features
2. **Advanced Analytics**: Implement detailed usage analytics
3. **API Expansion**: Create public API endpoints for data access
4. **Mobile App**: Use same Appwrite backend for mobile application

---

## Task Completion Rules

### For Each Task:
1. **Before Starting**:
   - Verify all dependencies completed
   - Research latest Appwrite documentation
   - Document current state in `migrate-changes.md`

2. **During Execution**:
   - Follow instructions exactly
   - Use only specified MCP tools for backend operations
   - Test functionality after each change
   - Document any issues encountered

3. **After Completion**:
   - Update `migrate-changes.md` with changes made
   - Mark task as completed in `migration.md`
   - Verify validation criteria met
   - Test integration with other components

4. **Documentation Requirements**:
   - Log all MCP tool commands used
   - Document any deviations from plan
   - Record performance metrics where applicable
   - Note any additional research conducted

### Migration Progress Tracking
- Use checkboxes in `migration.md` to track task completion
- Update `migrate-changes.md` after each task
- Maintain detailed log of all changes made
- Document any issues and their resolutions

---

*Migration plan complete. Ready for execution starting with Phase 1.*
