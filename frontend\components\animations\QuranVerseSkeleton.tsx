import React from 'react';

interface QuranVerseSkeleterProps {
  className?: string;
  verseClassName?: string;
  sourceClassName?: string;
  direction?: 'ltr' | 'rtl';
}

const QuranVerseSkeleton: React.FC<QuranVerseSkeleterProps> = ({
  className = '',
  verseClassName = '',
  sourceClassName = '',
  direction = 'rtl'
}) => {
  return (
    <div className={`${className} text-center animate-pulse`} dir={direction}>
      {/* Arabic verse skeleton */}
      <div className={`${verseClassName} mb-2 space-y-2`}>
        <div className="h-6 md:h-8 bg-gradient-to-r from-emerald-200 via-emerald-100 to-emerald-200 rounded-lg w-full"></div>
        <div className="h-6 md:h-8 bg-gradient-to-r from-emerald-200 via-emerald-100 to-emerald-200 rounded-lg w-4/5 mx-auto"></div>
        <div className="h-6 md:h-8 bg-gradient-to-r from-emerald-200 via-emerald-100 to-emerald-200 rounded-lg w-3/4 mx-auto"></div>
      </div>
      
      {/* Reference skeleton */}
      <div className={`${sourceClassName} text-sm opacity-60 mb-2`}>
        <div className="h-4 bg-gradient-to-r from-amber-200 via-amber-100 to-amber-200 rounded w-32 mx-auto"></div>
      </div>
      
      {/* English translation skeleton */}
      <div className="mt-2 space-y-2" dir="ltr">
        <div className="h-4 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded w-full max-w-lg mx-auto"></div>
        <div className="h-4 bg-gradient-to-r from-slate-200 via-slate-100 to-slate-200 rounded w-4/5 max-w-md mx-auto"></div>
      </div>
      
      {/* Controls skeleton */}
      <div className="flex justify-center items-center mt-6 space-x-4">
        <div className="w-10 h-10 bg-white/30 rounded-full animate-pulse"></div>
        <div className="w-10 h-10 bg-white/30 rounded-full animate-pulse"></div>
        <div className="w-10 h-10 bg-white/30 rounded-full animate-pulse"></div>
        <div className="w-10 h-10 bg-white/30 rounded-full animate-pulse"></div>
      </div>
    </div>
  );
};

export default QuranVerseSkeleton;