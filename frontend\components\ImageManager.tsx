import React, { useState } from 'react';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from './ui/alert-dialog';
import { useToast } from './ui/use-toast';
import backend from '~backend/client';

// Update the backend client to include credentials
const backendWithCredentials = backend.with({
  requestInit: {
    credentials: 'include'
  }
});

interface MartyrImage {
  id: number;
  martyrId: number;
  url: string;
  caption?: string;
  credit?: string;
  isProfileImage: boolean;
  createdAt: string;
}

interface ImageManagerProps {
  images: MartyrImage[];
  onImageDeleted?: (imageId: number) => void;
  onImageUpdated?: (image: MartyrImage) => void;
}

export const ImageManager: React.FC<ImageManagerProps> = ({
  images,
  onImageDeleted,
  onImageUpdated
}) => {
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const { toast } = useToast();

  const deleteImage = async (imageId: number) => {
    setDeletingId(imageId);
    
    try {
      await backendWithCredentials.martyrs.deleteImage({ id: imageId });
      onImageDeleted?.(imageId);
      
      toast({
        title: "Image Deleted",
        description: "The image has been successfully removed.",
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete image';
      toast({
        title: "Delete Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
    }
  };

  const setAsProfileImage = async (imageId: number) => {
    try {
      const targetImage = images.find(img => img.id === imageId);
      if (!targetImage) return;

      const updatedImage = await backendWithCredentials.martyrs.setProfileImage({ id: imageId });
      onImageUpdated?.(updatedImage);
      
      toast({
        title: "Profile Image Updated",
        description: "The profile image has been successfully changed.",
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile image';
      toast({
        title: "Update Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  if (images.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="text-muted-foreground text-center">
            <svg 
              className="mx-auto h-16 w-16 mb-4" 
              stroke="currentColor" 
              fill="none" 
              viewBox="0 0 48 48"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <h3 className="text-lg font-medium mb-2">No Images</h3>
            <p className="text-sm">Upload images to get started</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {images.map((image) => (
        <Card key={image.id} className="overflow-hidden">
          <div className="relative">
            <img
              src={image.url}
              alt={image.caption || 'Martyr image'}
              className="w-full h-48 object-cover"
            />
            
            {/* Profile Image Badge */}
            {image.isProfileImage && (
              <Badge 
                variant="secondary" 
                className="absolute top-2 left-2 bg-green-100 text-green-800"
              >
                Profile Image
              </Badge>
            )}

            {/* Action Buttons */}
            <div className="absolute top-2 right-2 flex gap-1">
              {!image.isProfileImage && (
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setAsProfileImage(image.id)}
                  className="text-xs"
                >
                  Set as Profile
                </Button>
              )}
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    size="sm"
                    variant="destructive"
                    disabled={deletingId === image.id}
                  >
                    {deletingId === image.id ? 'Deleting...' : 'Delete'}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Image</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this image? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => deleteImage(image.id)}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>

          <CardContent className="p-4">
            {/* Image Info */}
            <div className="space-y-2">
              {image.caption && (
                <p className="text-sm text-gray-700 line-clamp-2">
                  {image.caption}
                </p>
              )}
              
              {image.credit && (
                <p className="text-xs text-gray-500">
                  Credit: {image.credit}
                </p>
              )}
              
              <p className="text-xs text-gray-400">
                Uploaded: {new Date(image.createdAt).toLocaleDateString()}
              </p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};