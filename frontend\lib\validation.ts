// Input validation utilities for admin forms
// Ensures data integrity and security for culturally sensitive memorial content

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const ValidationUtils = {
  // Email validation for admin accounts
  validateEmail: (email: string): ValidationResult => {
    const errors: string[] = [];
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!email) {
      errors.push('Email is required');
    } else if (!emailRegex.test(email)) {
      errors.push('Please enter a valid email address');
    }
    
    return { isValid: errors.length === 0, errors };
  },

  // URL slug validation for martyr profiles
  validateSlug: (slug: string): ValidationResult => {
    const errors: string[] = [];
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    
    if (!slug) {
      errors.push('URL slug is required');
    } else if (slug.length < 3) {
      errors.push('URL slug must be at least 3 characters');
    } else if (slug.length > 100) {
      errors.push('URL slug must not exceed 100 characters');
    } else if (!slugRegex.test(slug)) {
      errors.push('URL slug must contain only lowercase letters, numbers, and hyphens');
    } else if (slug.startsWith('-') || slug.endsWith('-')) {
      errors.push('URL slug cannot start or end with a hyphen');
    }
    
    return { isValid: errors.length === 0, errors };
  },

  // Name validation for martyr names (culturally sensitive)
  validateName: (name: string): ValidationResult => {
    const errors: string[] = [];
    
    if (!name) {
      errors.push('Name is required');
    } else if (name.length < 2) {
      errors.push('Name must be at least 2 characters');
    } else if (name.length > 200) {
      errors.push('Name must not exceed 200 characters');
    }
    // Allow Arabic, English, and common diacritics
    else if (!/^[a-zA-Z\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\-''.]+$/.test(name)) {
      errors.push('Name contains invalid characters');
    }
    
    return { isValid: errors.length === 0, errors };
  },

  // Geographic coordinate validation
  validateLatitude: (lat: string | number): ValidationResult => {
    const errors: string[] = [];
    
    if (lat === '' || lat === null || lat === undefined) {
      return { isValid: true, errors }; // Optional field
    }
    
    const latitude = typeof lat === 'string' ? parseFloat(lat) : lat;
    
    if (isNaN(latitude)) {
      errors.push('Latitude must be a valid number');
    } else if (latitude < -90 || latitude > 90) {
      errors.push('Latitude must be between -90 and 90 degrees');
    }
    
    return { isValid: errors.length === 0, errors };
  },

  validateLongitude: (lng: string | number): ValidationResult => {
    const errors: string[] = [];
    
    if (lng === '' || lng === null || lng === undefined) {
      return { isValid: true, errors }; // Optional field
    }
    
    const longitude = typeof lng === 'string' ? parseFloat(lng) : lng;
    
    if (isNaN(longitude)) {
      errors.push('Longitude must be a valid number');
    } else if (longitude < -180 || longitude > 180) {
      errors.push('Longitude must be between -180 and 180 degrees');
    }
    
    return { isValid: errors.length === 0, errors };
  },

  // Biography validation
  validateBiography: (bio: string): ValidationResult => {
    const errors: string[] = [];
    
    if (!bio) {
      errors.push('Biography is required');
    } else if (bio.length < 50) {
      errors.push('Biography must be at least 50 characters to provide meaningful content');
    } else if (bio.length > 50000) {
      errors.push('Biography must not exceed 50,000 characters');
    }
    
    return { isValid: errors.length === 0, errors };
  },

  // Date validation
  validateDate: (date: string, fieldName: string): ValidationResult => {
    const errors: string[] = [];
    
    if (!date) {
      return { isValid: true, errors }; // Optional field
    }
    
    const dateObj = new Date(date);
    const now = new Date();
    
    if (isNaN(dateObj.getTime())) {
      errors.push(`${fieldName} must be a valid date`);
    } else if (dateObj > now) {
      errors.push(`${fieldName} cannot be in the future`);
    } else if (dateObj.getFullYear() < 1800) {
      errors.push(`${fieldName} must be after year 1800`);
    }
    
    return { isValid: errors.length === 0, errors };
  },

  // File validation for uploads
  validateFile: (file: File): ValidationResult => {
    const errors: string[] = [];
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    
    if (!file) {
      errors.push('Please select a file');
      return { isValid: false, errors };
    }
    
    if (file.size > maxSize) {
      errors.push(`File size must not exceed 10MB (current: ${(file.size / 1024 / 1024).toFixed(2)}MB)`);
    }
    
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      errors.push('File must be an image (JPEG, PNG, WebP, or GIF)');
    }
    
    if (file.name.length > 255) {
      errors.push('Filename is too long (max 255 characters)');
    }
    
    return { isValid: errors.length === 0, errors };
  },

  // Comprehensive form validation
  validateMartyrForm: (formData: any): { isValid: boolean; fieldErrors: Record<string, string[]> } => {
    const fieldErrors: Record<string, string[]> = {};
    
    // Validate required fields
    const nameResult = ValidationUtils.validateName(formData.name);
    if (!nameResult.isValid) fieldErrors.name = nameResult.errors;
    
    const slugResult = ValidationUtils.validateSlug(formData.slug);
    if (!slugResult.isValid) fieldErrors.slug = slugResult.errors;
    
    const bioResult = ValidationUtils.validateBiography(formData.bio);
    if (!bioResult.isValid) fieldErrors.bio = bioResult.errors;
    
    // Validate optional fields
    if (formData.birthDate) {
      const birthDateResult = ValidationUtils.validateDate(formData.birthDate, 'Birth date');
      if (!birthDateResult.isValid) fieldErrors.birthDate = birthDateResult.errors;
    }
    
    if (formData.deathDate) {
      const deathDateResult = ValidationUtils.validateDate(formData.deathDate, 'Death date');
      if (!deathDateResult.isValid) fieldErrors.deathDate = deathDateResult.errors;
    }
    
    // Validate date logic
    if (formData.birthDate && formData.deathDate) {
      const birthDate = new Date(formData.birthDate);
      const deathDate = new Date(formData.deathDate);
      if (birthDate >= deathDate) {
        fieldErrors.deathDate = fieldErrors.deathDate || [];
        fieldErrors.deathDate.push('Death date must be after birth date');
      }
    }
    
    const latResult = ValidationUtils.validateLatitude(formData.latitude);
    if (!latResult.isValid) fieldErrors.latitude = latResult.errors;
    
    const lngResult = ValidationUtils.validateLongitude(formData.longitude);
    if (!lngResult.isValid) fieldErrors.longitude = lngResult.errors;
    
    return {
      isValid: Object.keys(fieldErrors).length === 0,
      fieldErrors
    };
  }
};