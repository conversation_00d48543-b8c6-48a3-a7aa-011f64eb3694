import React, { useState } from 'react';
import { FileUpload } from './FileUpload';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Checkbox } from './ui/checkbox';
import { useToast } from './ui/use-toast';
import backend from '~backend/client';

// Update the backend client to include credentials
const backendWithCredentials = backend.with({
  requestInit: {
    credentials: 'include'
  }
});

interface ImageUploadFormProps {
  martyrId: number;
  onImageAdded?: (image: MartyrImage) => void;
}

interface MartyrImage {
  id: number;
  martyrId: number;
  url: string;
  caption?: string;
  credit?: string;
  isProfileImage: boolean;
  createdAt: string;
}

interface ImageMetadata {
  caption: string;
  credit: string;
  isProfileImage: boolean;
}

export const ImageUploadForm: React.FC<ImageUploadFormProps> = ({
  martyrId,
  onImageAdded
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<{ url: string; id: string }[]>([]);
  const [imageMetadata, setImageMetadata] = useState<Record<string, ImageMetadata>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleUploadSuccess = (fileUrl: string, fileId: string) => {
    setUploadedFiles(prev => [...prev, { url: fileUrl, id: fileId }]);
    setImageMetadata(prev => ({
      ...prev,
      [fileId]: {
        caption: '',
        credit: '',
        isProfileImage: false
      }
    }));
  };

  const updateMetadata = (fileId: string, updates: Partial<ImageMetadata>) => {
    setImageMetadata(prev => ({
      ...prev,
      [fileId]: { ...prev[fileId], ...updates }
    }));
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    setImageMetadata(prev => {
      const { [fileId]: removed, ...rest } = prev;
      return rest;
    });
  };

  const submitImages = async () => {
    if (uploadedFiles.length === 0) {
      toast({
        title: "No Images",
        description: "Please upload at least one image before submitting.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Submit each image with its metadata using the generated client
      for (const file of uploadedFiles) {
        const metadata = imageMetadata[file.id];
        
        const response = await backendWithCredentials.martyrs.addImage({
          martyrId,
          url: file.url,
          caption: metadata.caption || undefined,
          credit: metadata.credit || undefined,
          isProfileImage: metadata.isProfileImage,
        });

        onImageAdded?.(response);
      }

      toast({
        title: "Images Saved",
        description: `Successfully saved ${uploadedFiles.length} image(s).`,
      });

      // Reset form
      setUploadedFiles([]);
      setImageMetadata({});

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save images';
      toast({
        title: "Save Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Status Notice */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
          <p className="text-sm text-amber-800">
            <strong>Upload System Status:</strong> Backend upload endpoints are implemented but may need client regeneration. 
            Test with the Upload Test tab if you encounter 404 errors.
          </p>
        </div>
      </div>

      {/* File Upload Component */}
      <FileUpload
        martyrId={martyrId}
        onUploadSuccess={handleUploadSuccess}
        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
        maxSize={10 * 1024 * 1024} // 10MB
        multiple={true}
      />

      {/* Uploaded Files with Metadata Forms */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Image Details</h3>
          
          {uploadedFiles.map((file) => {
            const metadata = imageMetadata[file.id] || {};
            
            return (
              <Card key={file.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-base">Image Preview</CardTitle>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                    >
                      Remove
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Image Preview */}
                  <div className="flex justify-center">
                    <img
                      src={file.url}
                      alt="Preview"
                      className="max-w-xs max-h-48 object-contain rounded-lg border"
                    />
                  </div>

                  {/* Metadata Form */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`caption-${file.id}`}>Caption</Label>
                      <Textarea
                        id={`caption-${file.id}`}
                        placeholder="Enter image caption..."
                        value={metadata.caption || ''}
                        onChange={(e) => updateMetadata(file.id, { caption: e.target.value })}
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`credit-${file.id}`}>Photo Credit</Label>
                      <Input
                        id={`credit-${file.id}`}
                        placeholder="Photographer or source..."
                        value={metadata.credit || ''}
                        onChange={(e) => updateMetadata(file.id, { credit: e.target.value })}
                      />
                    </div>
                  </div>

                  {/* Profile Image Checkbox */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`profile-${file.id}`}
                      checked={metadata.isProfileImage || false}
                      onCheckedChange={(checked) => {
                        // Only allow one profile image
                        if (checked) {
                          // Uncheck all other profile images
                          setImageMetadata(prev => {
                            const updated = { ...prev };
                            Object.keys(updated).forEach(key => {
                              if (key !== file.id) {
                                updated[key] = { ...updated[key], isProfileImage: false };
                              }
                            });
                            return updated;
                          });
                        }
                        updateMetadata(file.id, { isProfileImage: checked as boolean });
                      }}
                    />
                    <Label htmlFor={`profile-${file.id}`} className="text-sm font-medium">
                      Set as profile image
                    </Label>
                  </div>
                </CardContent>
              </Card>
            );
          })}

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              onClick={submitImages}
              disabled={isSubmitting}
              size="lg"
            >
              {isSubmitting ? 'Saving...' : `Save ${uploadedFiles.length} Image(s)`}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};