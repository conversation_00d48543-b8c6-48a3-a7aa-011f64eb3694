import { api, APIError } from "encore.dev/api";
import { getAuthData } from "~encore/auth";
import { martyrsDB } from "./db";
import { logImageUpload, logImageDelete } from "../auth/auditLog";

interface AddImageRequest {
  martyrId: number;
  url: string;
  caption?: string;
  credit?: string;
  isProfileImage?: boolean;
}

interface MartyrImage {
  id: number;
  martyrId: number;
  url: string;
  caption?: string;
  credit?: string;
  isProfileImage: boolean;
  createdAt: string;
}

interface DeleteImageRequest {
  id: number;
}

interface DeleteImageResponse {
  success: boolean;
  message: string;
}

// Adds an image to a martyr profile.
export const addImage = api<AddImageRequest, MartyrImage>(
  { auth: true, expose: true, method: "POST", path: "/admin/martyrs/images" },
  async (req) => {
    const auth = getAuthData();
    if (!auth || auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate required fields
    if (!req.martyrId || !req.url) {
      throw APIError.invalidArgument("Missing required fields: martyrId and url");
    }

    const timestamp = new Date().toISOString();

    try {
      // If setting as profile image, unset any existing profile image for this martyr
      if (req.isProfileImage) {
        await martyrsDB.exec`
          UPDATE martyr_images SET is_profile_image = false WHERE martyr_id = ${req.martyrId}
        `;
      }

      const result = await martyrsDB.queryRow<{
        id: number;
        martyr_id: number;
        url: string;
        caption?: string;
        credit?: string;
        is_profile_image: boolean;
        created_at: string;
      }>`
        INSERT INTO martyr_images (
          martyr_id, url, caption, credit, is_profile_image, created_at
        ) VALUES (
          ${req.martyrId}, ${req.url}, ${req.caption}, ${req.credit}, 
          ${req.isProfileImage || false}, ${timestamp}
        )
        RETURNING *
      `;

      if (!result) {
        throw APIError.internal("Failed to add image");
      }

      // Log the image upload
      await logImageUpload(result.id, result.martyr_id);

      return {
        id: result.id,
        martyrId: result.martyr_id,
        url: result.url,
        caption: result.caption,
        credit: result.credit,
        isProfileImage: result.is_profile_image,
        createdAt: result.created_at
      };
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      console.error("Database error in addImage:", error);
      throw APIError.internal("Database error: " + (error as Error).message);
    }
  }
);

// Deletes an image from a martyr profile.
export const deleteImage = api<DeleteImageRequest, DeleteImageResponse>(
  { auth: true, expose: true, method: "DELETE", path: "/admin/martyrs/images/:id" },
  async ({ id }) => {
    const auth = getAuthData();
    if (!auth || auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate ID
    if (!id || id <= 0) {
      throw APIError.invalidArgument("Invalid image ID");
    }

    try {
      // First get the image details for logging
      const image = await martyrsDB.queryRow<{ id: number; martyr_id: number }>`
        SELECT id, martyr_id FROM martyr_images WHERE id = ${id}
      `;

      if (!image) {
        throw APIError.notFound("Image not found");
      }

      // Log the image deletion before actually deleting it
      await logImageDelete(image.id, image.martyr_id);

      const result = await martyrsDB.exec`
        DELETE FROM martyr_images WHERE id = ${id}
      `;

      if (result.rowsAffected === 0) {
        throw APIError.notFound("Image not found");
      }

      return {
        success: true,
        message: "Image deleted successfully"
      };
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      console.error("Database error in deleteImage:", error);
      throw APIError.internal("Database error: " + (error as Error).message);
    }
  }
);

// Get signed upload URL for martyr image
interface GetImageUploadURLRequest {
  martyrId: number;
  filename: string;
  contentType: string;
  fileSize: number;
}

interface SignedUploadURL {
  uploadUrl: string;
  fileUrl: string;
  fields: Record<string, string>;
}

export const getImageUploadURL = api<GetImageUploadURLRequest, SignedUploadURL>(
  { auth: true, expose: true, method: "POST", path: "/admin/martyrs/images/upload-url" },
  async (req) => {
    const auth = getAuthData();
    if (!auth || auth.role !== "admin") {
      throw APIError.permissionDenied("Admin access required");
    }

    // Validate required fields
    if (!req.martyrId || !req.filename || !req.contentType || !req.fileSize) {
      throw APIError.invalidArgument("Missing required fields");
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(req.contentType.toLowerCase())) {
      throw APIError.invalidArgument("Only image files are allowed");
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (req.fileSize > maxSize) {
      throw APIError.invalidArgument("File size must be less than 10MB");
    }

    // Import storage service
    const { storage } = await import("../upload/storage");
    
    try {
      const result = await storage.generateSignedUploadURL(
        req.filename,
        req.contentType,
        req.fileSize
      );
      
      return result;
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      console.error("Storage error in getImageUploadURL:", error);
      throw APIError.internal("Failed to generate upload URL");
    }
  }
);