/**
 * CSS Optimization Utilities
 * Helpers for critical CSS extraction and performance optimization
 */

// Critical CSS classes that should be inlined
export const criticalCSSClasses = [
  // Layout
  'container', 'grid', 'grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3',
  'flex', 'flex-col', 'items-center', 'justify-center', 'justify-between',
  'relative', 'absolute', 'fixed', 'sticky',
  
  // Spacing
  'p-4', 'p-6', 'p-8', 'px-4', 'px-6', 'px-8', 'py-4', 'py-6', 'py-8',
  'gap-4', 'gap-6', 'gap-8', 'mb-4', 'mb-6', 'mb-8', 'mx-auto',
  
  // Typography
  'text-center', 'text-left', 'text-right', 'font-bold', 'font-semibold', 'font-medium',
  'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl', 'text-4xl',
  'arabic-text',
  
  // Colors
  'text-white', 'text-slate-600', 'text-slate-700', 'text-slate-800',
  'text-emerald-600', 'text-emerald-700', 'bg-white', 'bg-emerald-600', 'bg-slate-50',
  
  // Interactive
  'btn-primary', 'btn-outline', 'card', 'hero-section', 'nav-header',
  'loading-skeleton', 'focus-ring',
  
  // Responsive
  'sm:block', 'sm:hidden', 'sm:text-lg', 'sm:text-xl', 'sm:px-6', 'sm:py-16',
  'md:block', 'md:hidden', 'md:text-xl', 'md:text-2xl', 'md:text-4xl', 'md:px-8', 'md:py-20',
  'lg:block', 'lg:hidden', 'lg:text-2xl', 'lg:text-3xl', 'lg:text-5xl', 'lg:px-8', 'lg:py-24',
  
  // Utility
  'w-full', 'h-full', 'min-h-screen', 'max-w-7xl', 'max-w-4xl',
  'rounded', 'rounded-lg', 'rounded-xl', 'rounded-2xl',
  'shadow', 'shadow-lg', 'shadow-xl', 'overflow-hidden', 'overflow-x-auto',
  'z-10', 'z-20', 'z-50', 'transition-all', 'transition-colors', 'transition-transform'
] as const;

// Above-the-fold components that need critical CSS
export const criticalComponents = [
  'HomePage', 'MartyrCard', 'QuranVerseRotator', 'TypewriterText', 'Header', 'Navigation'
] as const;

// Font loading optimization
export const fontLoadingStrategy = {
  // Preload critical fonts
  preloadFonts: [
    {
      href: '/fonts/inter-variable.woff2',
      type: 'font/woff2',
      crossorigin: 'anonymous'
    },
    {
      href: '/fonts/amiri-regular.woff2',
      type: 'font/woff2',
      crossorigin: 'anonymous'
    }
  ],
  
  // Font display strategy
  fontDisplay: 'swap',
  
  // Font fallbacks
  fontStacks: {
    primary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif",
    arabic: "'Amiri', 'Noto Naskh Arabic', 'Traditional Arabic', serif",
    fallback: "system-ui, -apple-system, sans-serif"
  }
};

// Performance budgets
export const performanceBudgets = {
  // Critical CSS should be under 14KB (gzipped)
  criticalCSS: 14 * 1024,
  
  // Total CSS should be under 50KB (gzipped)
  totalCSS: 50 * 1024,
  
  // JavaScript bundles
  criticalJS: 30 * 1024,
  totalJS: 200 * 1024,
  
  // Images
  heroImage: 100 * 1024,
  cardImage: 50 * 1024
};

// CSS optimization utilities
export const cssOptimization = {
  // Remove unused CSS classes
  purgeCSS: {
    content: [
      './pages/**/*.{ts,tsx}',
      './components/**/*.{ts,tsx}',
      './lib/**/*.{ts,tsx}'
    ],
    safelist: criticalCSSClasses,
    blocklist: [
      // Remove development-only classes
      'debug-*',
      'dev-*'
    ]
  },
  
  // CSS minification options
  minification: {
    removeComments: true,
    removeWhitespace: true,
    mergeDuplicates: true,
    optimizeShorthands: true,
    removeUnusedRules: true
  },
  
  // Critical CSS extraction
  extraction: {
    // Viewport size for critical CSS
    width: 1200,
    height: 900,
    
    // Above-the-fold threshold
    foldHeight: 600,
    
    // Include critical animations
    includeKeyframes: ['loading-shimmer', 'fadeIn', 'slideIn'],
    
    // Exclude non-critical
    exclude: [
      '@media print',
      '.hidden',
      '.print\\:*'
    ]
  }
};

// Resource loading optimization
export const resourceOptimization = {
  // Preload critical resources
  preload: [
    { href: '/styles/critical.css', as: 'style' },
    { href: '/api/martyrs/featured', as: 'fetch', crossorigin: 'anonymous' }
  ],
  
  // Prefetch likely resources
  prefetch: [
    { href: '/styles/components.css', as: 'style' },
    { href: '/api/categories', as: 'fetch', crossorigin: 'anonymous' }
  ],
  
  // DNS prefetch for external resources
  dnsPrefetch: [
    '//fonts.googleapis.com',
    '//fonts.gstatic.com'
  ],
  
  // Preconnect for critical external resources
  preconnect: [
    { href: 'https://fonts.googleapis.com', crossorigin: 'anonymous' },
    { href: 'https://fonts.gstatic.com', crossorigin: 'anonymous' }
  ]
};

// Runtime CSS optimization
export const runtimeOptimization = {
  // Lazy load non-critical CSS
  lazyLoadCSS: (href: string, media = 'all') => {
    if (typeof window !== 'undefined') {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      link.media = 'print';
      link.onload = () => {
        link.media = media;
      };
      document.head.appendChild(link);
    }
  },
  
  // Critical CSS injection
  injectCriticalCSS: (css: string) => {
    if (typeof window !== 'undefined') {
      const style = document.createElement('style');
      style.textContent = css;
      document.head.appendChild(style);
    }
  },
  
  // Remove unused CSS at runtime (for SPA)
  removeUnusedCSS: (selectors: string[]) => {
    if (typeof window !== 'undefined') {
      const sheets = Array.from(document.styleSheets);
      sheets.forEach(sheet => {
        try {
          const rules = Array.from(sheet.cssRules || sheet.rules);
          rules.forEach((rule, index) => {
            if (rule instanceof CSSStyleRule) {
              const selector = rule.selectorText;
              if (selectors.some(unused => selector.includes(unused))) {
                sheet.deleteRule(index);
              }
            }
          });
        } catch (e) {
          // Cross-origin or other access issues
          console.warn('Cannot access stylesheet rules:', e);
        }
      });
    }
  }
};

// Build-time optimization helpers
export const buildOptimization = {
  // Generate critical CSS HTML
  generateCriticalHTML: (criticalCSS: string) => `
    <style>
      ${criticalCSS}
    </style>
    <noscript>
      <link rel="stylesheet" href="/styles/main.css">
    </noscript>
    <script>
      // Load non-critical CSS asynchronously
      (function() {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = '/styles/main.css';
        link.media = 'print';
        link.onload = function() { this.media = 'all'; };
        document.head.appendChild(link);
      })();
    </script>
  `,
  
  // CSS splitting strategy
  splitStrategy: {
    critical: ['critical.css'],
    components: ['components.css'],
    utilities: ['utilities.css'],
    themes: ['themes.css'],
    print: ['print.css']
  },
  
  // Asset optimization
  assets: {
    // CSS compression
    css: {
      level: 2, // csso optimization level
      restructure: true,
      comments: false
    },
    
    // Font optimization
    fonts: {
      preload: true,
      display: 'swap',
      subset: ['latin', 'arabic']
    }
  }
};

export default {
  criticalCSSClasses,
  criticalComponents,
  fontLoadingStrategy,
  performanceBudgets,
  cssOptimization,
  resourceOptimization,
  runtimeOptimization,
  buildOptimization
};