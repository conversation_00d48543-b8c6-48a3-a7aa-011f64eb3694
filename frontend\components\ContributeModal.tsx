import React from 'react';
import { Link } from 'react-router-dom';
import { X, Heart, BookOpen, Users, ArrowRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import animationPatterns from '../lib/animationPatterns';
import { focusRing } from '../lib/designTokens';

interface ContributeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ContributeModal({ isOpen, onClose }: ContributeModalProps) {
  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Focus management
  const modalRef = React.useRef<HTMLDivElement>(null);
  const closeButtonRef = React.useRef<HTMLButtonElement>(null);

  React.useEffect(() => {
    if (isOpen && closeButtonRef.current) {
      closeButtonRef.current.focus();
    }
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            aria-hidden="true"
          />

          {/* Modal */}
          <motion.div
            ref={modalRef}
            className="relative bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={animationPatterns.springs.gentle}
            role="dialog"
            aria-modal="true"
            aria-labelledby="contribute-modal-title"
            aria-describedby="contribute-modal-description"
          >
            {/* Background decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 via-teal-50 to-emerald-100 rounded-3xl opacity-60"></div>
            <div className="absolute inset-0 opacity-20">
              <div className="absolute top-0 left-0 w-full h-full" style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2310b981' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm30 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: '60px 60px'
              }}></div>
            </div>

            <div className="relative p-8">
              {/* Close button */}
              <button
                ref={closeButtonRef}
                onClick={onClose}
                className={`absolute top-4 right-4 w-10 h-10 bg-white/80 hover:bg-white rounded-full flex items-center justify-center transition-all duration-200 ${focusRing.emerald} group`}
                aria-label="Close contribute modal"
              >
                <X className="w-5 h-5 text-slate-600 group-hover:text-slate-800 transition-colors duration-200" />
              </button>

              {/* Header */}
              <motion.div
                className="text-center mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <motion.div
                  className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-600 to-teal-700 rounded-2xl mb-6 shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={animationPatterns.springs.bouncy}
                >
                  <Heart className="w-8 h-8 text-white" fill="currentColor" />
                </motion.div>
                
                <h2 id="contribute-modal-title" className="text-3xl md:text-4xl font-bold text-emerald-800 mb-4">
                  Contribute to the Archive
                </h2>
                
                <p id="contribute-modal-description" className="text-lg text-emerald-700 leading-relaxed max-w-lg mx-auto">
                  Help us preserve the memory of Nigerian martyrs from the Islamic Movement of Nigeria (IMN). Share stories, provide information, or support our sacred mission
                  of documenting these eternal legacies.
                </p>
              </motion.div>

              {/* Contribution options */}
              <motion.div 
                className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"
                variants={animationPatterns.stagger.container}
                initial="initial"
                animate="animate"
              >
                <motion.div 
                  className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 border border-emerald-200/60 shadow-lg text-left"
                  variants={animationPatterns.fade.fadeInUp}
                  whileHover={{ y: -4, scale: 1.02 }}
                  transition={animationPatterns.springs.gentle}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mb-4">
                    <BookOpen className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-emerald-800 mb-3">Share Stories</h3>
                  <p className="text-emerald-700 leading-relaxed">
                    Submit verified information about martyrs not yet in our archive. Help us preserve their stories for future generations.
                  </p>
                </motion.div>
                
                <motion.div 
                  className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 border border-emerald-200/60 shadow-lg text-left"
                  variants={animationPatterns.fade.fadeInUp}
                  whileHover={{ y: -4, scale: 1.02 }}
                  transition={animationPatterns.springs.gentle}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl flex items-center justify-center mb-4">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-emerald-800 mb-3">Join Our Mission</h3>
                  <p className="text-emerald-700 leading-relaxed">
                    Become part of our community dedicated to preserving the memory and legacy of Nigerian martyrs from the Islamic Movement of Nigeria (IMN).
                  </p>
                </motion.div>
              </motion.div>
              
              {/* CTA Button */}
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={animationPatterns.springs.snappy}
                >
                  <Link to="/contact" onClick={onClose}>
                    <Button 
                      size="lg" 
                      className={`bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-10 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 ${focusRing.white} group`}
                    >
                      <Heart className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="currentColor" />
                      Get Involved
                      <motion.div
                        className="ml-2"
                        animate={{ x: [0, 4, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                      >
                        <ArrowRight className="w-5 h-5" />
                      </motion.div>
                    </Button>
                  </Link>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
