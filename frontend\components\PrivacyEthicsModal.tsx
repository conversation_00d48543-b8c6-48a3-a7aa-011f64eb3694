import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Shield, Heart, Eye, Lock, Mail, FileText, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { IslamicCalligraphyBorder } from '@/components/IslamicPatterns';

interface PrivacyEthicsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PrivacyEthicsModal({ isOpen, onClose }: PrivacyEthicsModalProps) {
  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Focus management
  useEffect(() => {
    if (isOpen) {
      const focusableElements = document.querySelectorAll(
        '[data-modal-content] button, [data-modal-content] [href], [data-modal-content] input, [data-modal-content] select, [data-modal-content] textarea, [data-modal-content] [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      const handleTabKey = (e: KeyboardEvent) => {
        if (e.key === 'Tab') {
          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              lastElement?.focus();
              e.preventDefault();
            }
          } else {
            if (document.activeElement === lastElement) {
              firstElement?.focus();
              e.preventDefault();
            }
          }
        }
      };

      document.addEventListener('keydown', handleTabKey);
      firstElement?.focus();

      return () => {
        document.removeEventListener('keydown', handleTabKey);
      };
    }
  }, [isOpen]);

  const privacySections = [
    {
      icon: Shield,
      title: "Data Protection",
      content: "We implement robust security measures to protect all information about Nigerian IMN martyrs and their families. Personal data is encrypted and stored securely with limited access only to authorized personnel."
    },
    {
      icon: Eye,
      title: "Information Collection",
      content: "We collect information from public sources, family testimonies, and community records. All data collection follows ethical guidelines and respects the dignity of martyrs and their families."
    },
    {
      icon: Users,
      title: "Family Privacy",
      content: "We prioritize the privacy and wishes of martyrs' families. Sensitive personal information is protected, and families can request modifications or removal of information at any time."
    },
    {
      icon: Lock,
      title: "User Privacy",
      content: "We use minimal cookies for essential website functionality. We do not track users for advertising purposes or share personal information with third parties without explicit consent."
    }
  ];

  const ethicalGuidelines = [
    "Respectful representation of all martyrs and their stories",
    "Accurate historical documentation without sensationalism",
    "Protection of family privacy and sensitive information",
    "Cultural sensitivity to Islamic traditions and values",
    "Transparent research methodology and source attribution",
    "Non-commercial, educational purpose only"
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Modal Content */}
          <motion.div
            className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            data-modal-content
          >
            <Card className="bg-gradient-to-br from-white via-emerald-50/30 to-teal-50/20 border-2 border-emerald-200/60 shadow-2xl">
              {/* Header */}
              <CardHeader className="relative pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                      <Shield className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl font-bold text-emerald-800">
                        Privacy & Ethics
                      </CardTitle>
                      <p className="text-emerald-600 text-sm">Nigerian IMN Martyrs Archive</p>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-full p-2"
                    aria-label="Close privacy and ethics modal"
                  >
                    <X className="w-5 h-5" />
                  </Button>
                </div>
                
                <IslamicCalligraphyBorder className="max-w-xs mt-4" color="#10b981" />
                
                <p className="text-slate-600 mt-4">
                  Our commitment to protecting privacy and maintaining ethical standards in documenting 
                  the sacred memory of Nigerian IMN martyrs.
                </p>
              </CardHeader>

              {/* Content */}
              <CardContent className="overflow-y-auto max-h-[60vh] space-y-8">
                {/* Privacy Policy */}
                <section>
                  <h3 className="text-xl font-bold text-slate-800 mb-6 flex items-center">
                    <Lock className="w-5 h-5 mr-2 text-emerald-600" />
                    Privacy Policy
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {privacySections.map((section, index) => (
                      <motion.div
                        key={section.title}
                        className="p-4 bg-white/60 rounded-xl border border-emerald-200/50"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center flex-shrink-0">
                            <section.icon className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-slate-800 mb-2">{section.title}</h4>
                            <p className="text-sm text-slate-600 leading-relaxed">{section.content}</p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </section>

                {/* Ethical Guidelines */}
                <section>
                  <h3 className="text-xl font-bold text-slate-800 mb-6 flex items-center">
                    <Heart className="w-5 h-5 mr-2 text-emerald-600" />
                    Ethical Guidelines
                  </h3>
                  
                  <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-6 border border-emerald-200/60">
                    <p className="text-slate-700 mb-4">
                      We adhere to the highest ethical standards in our documentation and preservation efforts:
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {ethicalGuidelines.map((guideline, index) => (
                        <motion.div
                          key={index}
                          className="flex items-start space-x-2"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-sm text-slate-700">{guideline}</span>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </section>

                {/* GDPR Compliance */}
                <section>
                  <h3 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                    <FileText className="w-5 h-5 mr-2 text-emerald-600" />
                    GDPR Compliance & Your Rights
                  </h3>
                  
                  <div className="bg-white/60 rounded-xl p-6 border border-emerald-200/50">
                    <p className="text-slate-700 mb-4">
                      Under GDPR and international privacy laws, you have the right to:
                    </p>
                    
                    <div className="space-y-2 text-sm text-slate-600">
                      <p>• Access your personal data and understand how it's processed</p>
                      <p>• Request correction of inaccurate or incomplete information</p>
                      <p>• Request deletion of personal data (right to be forgotten)</p>
                      <p>• Object to processing of your personal data</p>
                      <p>• Data portability and the right to receive your data</p>
                    </div>
                  </div>
                </section>

                {/* Contact Information */}
                <section className="border-t border-emerald-200 pt-6">
                  <h3 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                    <Mail className="w-5 h-5 mr-2 text-emerald-600" />
                    Contact for Privacy Concerns
                  </h3>
                  
                  <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-6 border border-emerald-200/60">
                    <p className="text-slate-700 mb-4">
                      For any privacy concerns, data requests, or ethical questions regarding our documentation:
                    </p>
                    
                    <div className="space-y-2 text-sm">
                      <p className="text-slate-700">
                        <strong>Email:</strong> <EMAIL>
                      </p>
                      <p className="text-slate-700">
                        <strong>Response Time:</strong> Within 72 hours
                      </p>
                      <p className="text-slate-700">
                        <strong>Languages:</strong> English, Arabic, Hausa
                      </p>
                    </div>
                    
                    <div className="text-center mt-6 pt-4 border-t border-emerald-200">
                      <p className="text-emerald-700 font-medium text-sm">
                        "We honor the trust placed in us to preserve these sacred memories responsibly."
                      </p>
                      <p className="text-xs text-slate-600 mt-2" dir="rtl">
                        نحن ملتزمون بحماية خصوصيتكم وكرامة الشهداء
                      </p>
                    </div>
                  </div>
                </section>
              </CardContent>

              {/* Footer */}
              <div className="p-6 border-t border-emerald-200/50 bg-gradient-to-r from-emerald-50/50 to-teal-50/50">
                <div className="flex justify-between items-center">
                  <p className="text-xs text-slate-500">
                    Last updated: December 2024
                  </p>
                  <Button
                    onClick={onClose}
                    className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
                  >
                    Close
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
