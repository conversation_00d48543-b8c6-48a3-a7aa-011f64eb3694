import { APIError } from "encore.dev/api";

// Magic numbers for file type detection
const MAGIC_NUMBERS = {
  JPEG: [0xFF, 0xD8, 0xFF],
  PNG: [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A],
  GIF: [0x47, 0x49, 0x46, 0x38],
  WEBP: [0x52, 0x49, 0x46, 0x46] // RIFF header, need additional check for WEBP
};

/**
 * Validates file content by checking magic numbers and file structure
 * This provides security beyond MIME type checking which can be spoofed
 */
export class FileContentValidator {
  /**
   * Validates that the file content matches the declared content type
   * @param fileData The file data as Uint8Array
   * @param contentType The declared content type
   * @returns True if valid, throws APIError if invalid
   */
  static validateFileContent(fileData: Uint8Array, contentType: string): boolean {
    // Check file size first
    if (fileData.length === 0) {
      throw APIError.invalidArgument("File is empty");
    }

    // Validate based on content type
    switch (contentType.toLowerCase()) {
      case 'image/jpeg':
      case 'image/jpg':
        return this.validateJPEG(fileData);
      case 'image/png':
        return this.validatePNG(fileData);
      case 'image/gif':
        return this.validateGIF(fileData);
      case 'image/webp':
        return this.validateWEBP(fileData);
      default:
        throw APIError.invalidArgument(`Unsupported file type: ${contentType}`);
    }
  }

  /**
   * Validates JPEG file by checking magic numbers
   */
  private static validateJPEG(fileData: Uint8Array): boolean {
    // Check JPEG magic number at the beginning
    if (!this.checkMagicNumber(fileData, MAGIC_NUMBERS.JPEG)) {
      throw APIError.invalidArgument("Invalid JPEG file - missing JPEG signature");
    }

    // Check for JPEG end of image marker (0xFFD9)
    const endIndex = this.findJPEGEnd(fileData);
    if (endIndex === -1) {
      throw APIError.invalidArgument("Invalid JPEG file - missing end of image marker");
    }

    // Additional validation: check for valid JPEG structure
    if (!this.validateJPEGStructure(fileData)) {
      throw APIError.invalidArgument("Invalid JPEG file structure");
    }

    return true;
  }

  /**
   * Validates PNG file by checking magic numbers
   */
  private static validatePNG(fileData: Uint8Array): boolean {
    // Check PNG magic number
    if (!this.checkMagicNumber(fileData, MAGIC_NUMBERS.PNG)) {
      throw APIError.invalidArgument("Invalid PNG file - missing PNG signature");
    }

    // Check PNG header chunk (IHDR)
    if (fileData.length < 12 || 
        fileData[12] !== 0x49 || // 'I'
        fileData[13] !== 0x48 || // 'H'
        fileData[14] !== 0x44 || // 'D'
        fileData[15] !== 0x52) { // 'R'
      throw APIError.invalidArgument("Invalid PNG file - missing IHDR chunk");
    }

    return true;
  }

  /**
   * Validates GIF file by checking magic numbers
   */
  private static validateGIF(fileData: Uint8Array): boolean {
    // Check GIF magic number
    if (!this.checkMagicNumber(fileData, MAGIC_NUMBERS.GIF)) {
      throw APIError.invalidArgument("Invalid GIF file - missing GIF signature");
    }

    // Check version (87a or 89a)
    if (!((fileData[4] === 0x37 && fileData[5] === 0x61) || // '7a'
          (fileData[4] === 0x39 && fileData[5] === 0x61))) { // '9a'
      throw APIError.invalidArgument("Invalid GIF file - unsupported version");
    }

    return true;
  }

  /**
   * Validates WEBP file by checking RIFF header and WEBP signature
   */
  private static validateWEBP(fileData: Uint8Array): boolean {
    // Check RIFF header
    if (!this.checkMagicNumber(fileData, MAGIC_NUMBERS.WEBP)) {
      throw APIError.invalidArgument("Invalid WEBP file - missing RIFF header");
    }

    // Check file size in RIFF header
    if (fileData.length < 12) {
      throw APIError.invalidArgument("Invalid WEBP file - file too small");
    }

    // Check for WEBP signature
    if (fileData[8] !== 0x57 || // 'W'
        fileData[9] !== 0x45 || // 'E'
        fileData[10] !== 0x42 || // 'B'
        fileData[11] !== 0x50) { // 'P'
      throw APIError.invalidArgument("Invalid WEBP file - missing WEBP signature");
    }

    return true;
  }

  /**
   * Checks if the file data starts with the expected magic number
   */
  private static checkMagicNumber(fileData: Uint8Array, magicNumber: number[]): boolean {
    if (fileData.length < magicNumber.length) {
      return false;
    }

    for (let i = 0; i < magicNumber.length; i++) {
      if (fileData[i] !== magicNumber[i]) {
        return false;
      }
    }

    return true;
  }

  /**
   * Finds the JPEG end of image marker (0xFFD9)
   */
  private static findJPEGEnd(fileData: Uint8Array): number {
    // Search from the end of the file backwards for the EOI marker
    for (let i = fileData.length - 2; i >= 0; i--) {
      if (fileData[i] === 0xFF && fileData[i + 1] === 0xD9) {
        return i + 1; // Return index of second byte of EOI marker
      }
    }
    return -1; // Not found
  }

  /**
   * Validates basic JPEG structure by checking for SOF markers
   */
  private static validateJPEGStructure(fileData: Uint8Array): boolean {
    // Look for SOF (Start of Frame) markers which indicate valid JPEG structure
    // SOF0: 0xFFC0, SOF1: 0xFFC1, SOF2: 0xFFC2, etc.
    for (let i = 2; i < fileData.length - 1; i++) {
      if (fileData[i] === 0xFF) {
        const marker = fileData[i + 1];
        // Check if it's a SOF marker (0xC0 to 0xC3, 0xC5 to 0xC7, 0xC9 to 0xCB, 0xCD to 0xCF)
        if ((marker >= 0xC0 && marker <= 0xC3) ||
            (marker >= 0xC5 && marker <= 0xC7) ||
            (marker >= 0xC9 && marker <= 0xCB) ||
            (marker >= 0xCD && marker <= 0xCF)) {
          return true;
        }
        // Skip variable length markers
        if ((marker >= 0xC0 && marker <= 0xFE) && i + 3 < fileData.length) {
          const length = (fileData[i + 2] << 8) | fileData[i + 3];
          i += length + 1; // Skip marker and length bytes
        }
      }
    }
    return false;
  }

  /**
   * Additional security checks to prevent malicious files
   */
  static performSecurityChecks(fileData: Uint8Array, filename: string): boolean {
    // Check for potentially dangerous patterns
    const dangerousPatterns = [
      // PHP tags
      [0x3C, 0x3F, 0x70, 0x68, 0x70], // "<?php"
      [0x3C, 0x3F, 0x3D], // "<?="
      // JavaScript
      [0x3C, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74], // "<script"
      // HTML
      [0x3C, 0x68, 0x74, 0x6D, 0x6C], // "<html"
      [0x3C, 0x62, 0x6F, 0x64, 0x79], // "<body"
    ];

    for (const pattern of dangerousPatterns) {
      if (this.containsPattern(fileData, pattern)) {
        throw APIError.invalidArgument("File contains potentially malicious content");
      }
    }

    // Check filename for dangerous extensions
    const dangerousExtensions = ['.php', '.php3', '.php4', '.php5', '.phtml', '.js', '.html', '.htm'];
    const lowerFilename = filename.toLowerCase();
    
    for (const ext of dangerousExtensions) {
      if (lowerFilename.endsWith(ext)) {
        throw APIError.invalidArgument(`File extension ${ext} is not allowed`);
      }
    }

    return true;
  }

  /**
   * Checks if file data contains a specific byte pattern
   */
  private static containsPattern(fileData: Uint8Array, pattern: number[]): boolean {
    if (fileData.length < pattern.length) {
      return false;
    }

    for (let i = 0; i <= fileData.length - pattern.length; i++) {
      let match = true;
      for (let j = 0; j < pattern.length; j++) {
        if (fileData[i + j] !== pattern[j]) {
          match = false;
          break;
        }
      }
      if (match) {
        return true;
      }
    }
    return false;
  }
}