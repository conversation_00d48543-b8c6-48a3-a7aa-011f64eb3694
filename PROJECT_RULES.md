# Martyr Website Project Rules & AI Assistant Instructions

## 1. Project Overview & Mission

### Core Purpose
The **Martyr Website** is a cultural and religious memorial platform dedicated to documenting and honoring martyrs from Nigeria, specifically focusing on the Islamic Movement of Nigeria (IMN) martyrs. This is not just a database - it's a sacred digital archive that requires respectful, accurate, and culturally sensitive handling.

### Cultural & Religious Sensitivity Rules
- **MANDATORY**: Always approach this project with the utmost respect and cultural sensitivity
- **REQUIRED**: Understand that martyrs (<PERSON><PERSON><PERSON>) hold sacred significance in Islamic tradition
- **CRITICAL**: Never suggest content or features that could be seen as disrespectful to the memory of the deceased
- **ESSENTIAL**: Maintain historical accuracy and verify information when making content suggestions

## 2. Technology Stack & Architecture

### Backend: Encore.dev Framework
- **Framework**: Encore.dev 1.49.1+ (Modern TypeScript backend platform)
- **Architecture**: Service-Oriented Architecture (SOA) with 3 core services:
  - `auth`: Admin authentication and JWT token management
  - `martyrs`: Core CRUD operations, search, and data management
  - `upload`: File/image upload and cloud storage management
- **Database**: PostgreSQL with SQL migrations
- **API Generation**: Automatic type-safe API generation with Encore.dev
- **Authentication**: JWT-based admin authentication with role-based access control

### Frontend: Modern React Stack
- **Framework**: React 19.1.1+ with TypeScript
- **Build Tool**: Vite 6.3.5+
- **Styling**: Tailwind CSS 4.1.12+ with custom design tokens
- **UI Components**: Radix UI primitives with custom components
- **Routing**: React Router DOM 7.8.1+
- **State Management**: TanStack Query (React Query) 5.85.3+
- **Animations**: Framer Motion 12.23.12+
- **Maps**: OpenLayers 8.2.0+ for interactive geographic visualization
- **Rich Text**: TipTap 3.3.0+ for content editing

### Development Environment
- **Package Manager**: npm 9.8.1 (strict requirement)
- **Node.js**: Compatible with npm 9.8.1
- **Workspace**: npm workspaces with `backend/` and `frontend/` packages
- **Local Development**: Encore dev server on port 4000, Vite on port 5173

## 3. Mandatory Research & Verification Requirements

### CRITICAL: Always Use Exa Tools for Implementation
Before implementing any new feature, fix, or content modification, you **MUST**:

1. **Research Current Best Practices**:
   ```
   Use mcp_exa_web_search_exa to research:
   - Latest security practices for the technologies used
   - Current accessibility standards (WCAG 2.1+)
   - Performance optimization techniques
   - Modern UI/UX patterns for memorial websites
   ```

2. **Verify Technical Implementation**:
   ```
   Use mcp_exa_web_search_exa to verify:
   - Encore.dev latest documentation and best practices
   - React/TypeScript current patterns and recommendations
   - Database schema best practices for the data types involved
   - Security considerations for admin authentication
   ```

3. **Cultural and Religious Research**:
   ```
   For any content-related work, use mcp_exa_web_search_exa to research:
   - Islamic traditions regarding martyrdom and memorial practices
   - Cultural sensitivities for the regions/communities involved
   - Historical accuracy requirements for the period/events
   - Appropriate language and terminology usage
   ```

4. **Accessibility and Compliance**:
   ```
   Use mcp_exa_web_search_exa to ensure:
   - WCAG 2.1 AA compliance for new features
   - Screen reader compatibility for memorial content
   - Multi-language support considerations (Arabic, English)
   - Performance standards for low-bandwidth regions
   ```

### Deep Research Requirements
For complex features or significant changes, use `mcp_exa_deep_researcher_start` and `mcp_exa_deep_researcher_check` to conduct comprehensive research on:
- Security implications of new backend endpoints
- Performance impact of frontend changes
- Cultural appropriateness of new features
- Historical accuracy of content modifications

## 4. File Structure & Code Organization

### Backend Structure (`backend/`)
```
backend/
├── auth/                    # Authentication service
│   ├── auth.ts             # Auth handler and middleware
│   ├── login.ts            # Login endpoint
│   └── encore.service.ts   # Service registration
├── martyrs/                # Core martyrs management service
│   ├── migrations/         # SQL schema migrations
│   ├── admin_*.ts         # Admin-only endpoints (CRUD operations)
│   ├── get_*.ts          # Public read endpoints
│   ├── search.ts         # Search functionality
│   ├── db.ts             # Database connection
│   └── types.ts          # TypeScript interfaces
├── upload/                 # File upload service
│   ├── upload.ts         # Upload endpoints
│   ├── storage.ts        # Storage abstraction layer
│   └── types.ts          # Upload-related types
└── encore.gen/            # Auto-generated API clients (DO NOT EDIT)
```

### Frontend Structure (`frontend/`)
```
frontend/
├── components/
│   ├── ui/               # Reusable UI components (button, card, etc.)
│   ├── animations/       # Animation components (Quran rotator, typewriter)
│   ├── Header.tsx        # Main navigation with search
│   ├── Footer.tsx        # Site footer
│   ├── MartyrCard.tsx    # Martyr display card
│   ├── ImageManager.tsx  # Admin image management component
│   ├── ImageUploadForm.tsx # Admin image upload with metadata
│   ├── FileUpload.tsx    # Generic file upload component
│   ├── RichTextEditor.tsx # TipTap rich text editor
│   └── [Feature]*.tsx   # Feature-specific components
├── pages/                # Route components
│   ├── HomePage.tsx      # Landing page with featured martyrs
│   ├── SearchPage.tsx    # Search interface
│   ├── MartyrProfilePage.tsx  # Individual martyr details
│   ├── CategoryPage.tsx  # Category listing page
│   ├── TimelinePage.tsx  # Global timeline page
│   ├── AboutPage.tsx     # About page
│   ├── ContactPage.tsx   # Contact page
│   └── Admin Routes/     # Admin-specific pages (protected)
│       ├── AdminLoginPage.tsx        # Admin authentication
│       ├── AdminDashboardPage.tsx    # Main admin dashboard with analytics
│       ├── AdminMartyrFormPage.tsx   # Create/edit martyr forms
│       └── [AdminFeature]Page.tsx    # Other admin functionality
├── hooks/                # Custom React hooks
│   └── useScrollAnimation.ts # Scroll-based animations
├── lib/                  # Utility libraries
│   ├── utils.ts          # General utilities
│   ├── quranVerses.ts    # Quran verse content
│   ├── designTokens.ts   # Design system tokens
│   ├── animationPatterns.ts # Animation utilities
│   ├── seoMeta.ts        # SEO metadata helpers
│   ├── structuredData.ts # JSON-LD schema generation
│   └── [Feature]Utils.ts # Feature-specific utilities
└── styles/               # CSS files
    ├── index.css         # Global styles
    ├── animations.css    # Animation definitions
    ├── critical.css      # Above-the-fold CSS
    └── prose.css         # Rich text content styling
```

## 5. Database Schema & Data Rules

### Core Tables
1. **martyrs**: Main martyr records with biographical data
2. **martyr_images**: Associated images with profile image designation
3. **timeline_events**: Historical timeline events for martyrs
4. **file_uploads**: File upload metadata and tracking

### Data Integrity Rules
- **REQUIRED**: All martyr names must be respectfully formatted
- **MANDATORY**: Dates must be historically accurate and verified
- **CRITICAL**: Geographic data (latitude/longitude) must be precise
- **ESSENTIAL**: Sub-categories must follow the established taxonomy
- **IMPORTANT**: All user-generated content must be sanitized and verified

### Field Requirements
```sql
-- Core required fields for martyrs
name: TEXT NOT NULL          -- Full respectful name
slug: TEXT UNIQUE NOT NULL   -- URL-safe identifier
bio: TEXT NOT NULL           -- Biographical information
sub_categories: TEXT[]       -- Classification tags
created_at: TIMESTAMP        -- Record creation
updated_at: TIMESTAMP        -- Last modification
```

## 6. Authentication & Security Requirements

### Admin Authentication System
- **JWT Token Authentication**: Simple token-based authentication for admin routes using Bearer tokens
- **Role-Based Access Control (RBAC)**: Only `admin` role can access CRUD operations
- **Session Management**: Tokens stored in localStorage with automatic cleanup on logout
- **Secret Management**: Use Encore secret management (`AdminSecret`, `JWTSecret`) or `DEV_ADMIN_SECRET`, `DEV_JWT_SECRET` fallbacks
- **Protected Routes**: All admin endpoints (`/admin/*`) require authentication
- **Demo Credentials**: Email: `<EMAIL>`, Password: `Admin@2025!`

### Admin Dashboard Features
- **Comprehensive Dashboard**: Analytics, martyrs management, content health monitoring
- **Data Visualization**: Charts for views over time, martyrs by category (using Recharts)
- **Content Health Scoring**: Automated scoring based on biography length and profile images
- **Search & Pagination**: Real-time search with debouncing and paginated results
- **Bulk Operations**: Mass operations on martyr records

### Admin CRUD Operations
1. **Martyr Management**:
   - Create/Update/Delete martyr profiles
   - Rich text editing for biographies and context
   - Geographic data (latitude/longitude) support
   - Category and quote management
   - Timeline event management

2. **Image Management**:
   - Multiple image upload with drag-and-drop
   - Profile image designation
   - Image metadata (caption, credit)
   - Signed URL uploads for large files
   - Cloud storage integration

3. **Analytics & Monitoring**:
   - View statistics and trends
   - Content health monitoring
   - User activity tracking
   - Performance metrics

### Security Implementation
- **JWT Validation**: All admin endpoints validate JWT tokens with role checking
- **Input Validation**: Comprehensive validation using TypeScript interfaces
- **File Upload Security**: 
  - Type restrictions (JPEG, PNG, WebP, GIF only)
  - Size limits (10MB maximum)
  - Signed URL uploads for secure file handling
  - Metadata sanitization
- **SQL Injection Prevention**: Parameterized queries using Encore's SQL templates
- **XSS Prevention**: DOMPurify for content sanitization
- **CSRF Protection**: Bearer token authentication prevents CSRF attacks
- **Route Protection**: Automatic redirection for unauthenticated access attempts

## 7. UI/UX Design Principles

### Visual Design Guidelines
- **Color Palette**: Emerald/teal gradient themes with Islamic geometric patterns
- **Typography**: Respectful, readable fonts with Arabic text support
- **Cultural Elements**: Islamic patterns and motifs used tastefully
- **Accessibility**: WCAG 2.1 AA compliance required

### Component Design Rules
- **Consistency**: Follow established design system in `lib/designTokens.ts`
- **Responsiveness**: Mobile-first design approach
- **Performance**: Optimize for low-bandwidth regions
- **Animations**: Smooth, respectful animations that enhance rather than distract

### Islamic Cultural Elements
- **Quran Verses**: Use appropriate verses with proper citation
- **Arabic Text**: Proper RTL support and beautiful Arabic typography
- **Color Symbolism**: Use colors that respect Islamic cultural significance
- **Pattern Usage**: Incorporate traditional Islamic geometric patterns tastefully

## 8. Content & Language Requirements

### Content Guidelines
- **Accuracy**: All historical information must be verified
- **Respectful Language**: Use dignified, respectful terminology
- **Cultural Sensitivity**: Understand regional and cultural contexts
- **Arabic Support**: Proper handling of Arabic names and text
- **Translation**: Consider multi-language support requirements

### Terminology Standards
- Use "Martyr" (Shahid/Shaheed) respectfully
- Proper capitalization of religious terms
- Accurate historical period references
- Respectful family and personal information handling

## 9. Performance & Optimization Rules

### Frontend Performance
- **Lazy Loading**: Implement for images and non-critical components
- **Code Splitting**: Route-based code splitting with React.lazy
- **Image Optimization**: Proper image sizing and compression
- **Caching**: Implement appropriate caching strategies
- **Bundle Size**: Monitor and optimize bundle sizes

### Backend Performance
- **Database Indexing**: Proper indexes on frequently queried fields
- **Query Optimization**: Efficient SQL queries with pagination
- **Caching**: Implement Redis or similar for frequently accessed data
- **Rate Limiting**: Implement to prevent abuse

## 10. Testing & Quality Assurance

### Testing Requirements
- **Unit Tests**: Required for all business logic functions
- **Integration Tests**: Required for API endpoints
- **E2E Tests**: Required for critical user journeys
- **Accessibility Testing**: Required for all user-facing features
- **Performance Testing**: Required for all new features

### Quality Gates
- **Type Safety**: Full TypeScript coverage with strict mode
- **Linting**: ESLint and Prettier compliance
- **Security Scanning**: Regular security audits
- **Performance Budgets**: Defined performance metrics

## 11. Deployment & Environment Management

### Environment Configuration
- **Development**: Local Encore dev server with file storage simulation
- **Staging**: Encore Cloud staging environment
- **Production**: Encore Cloud production with real cloud storage

### Deployment Rules
- **Git Workflow**: Feature branches with PR reviews required
- **CI/CD**: Automated testing and deployment pipelines
- **Secret Management**: Proper secret handling across environments
- **Database Migrations**: Careful migration planning and rollback strategies

### Secret Management

The application requires the following secrets for proper operation:

1. **AdminSecret**: Used for admin authentication
2. **JWTSecret**: Used for JWT token signing

#### Setting Secrets in Development

For local development, you can set these secrets in two ways:

1. **Using Encore CLI (Recommended)**:
   ```bash
   # From the backend directory
   encore secret set AdminSecret "your-admin-secret"
   encore secret set JWTSecret "your-jwt-secret"
   ```

2. **Using Environment Variables**:
   Set the following environment variables:
   ```bash
   export DEV_ADMIN_SECRET="your-admin-secret"
   export DEV_JWT_SECRET="your-jwt-secret"
   export DEV_ADMIN_PASSWORD="your-admin-password"  # Optional
   ```

   Or use the `.env` file in the backend directory:
   ```bash
   # In backend/.env
   DEV_ADMIN_SECRET=your-admin-secret
   DEV_JWT_SECRET=your-jwt-secret
   DEV_ADMIN_PASSWORD=your-admin-password
   ```

#### Secret Generation

Use the provided script to generate secure secrets:
```bash
./scripts/setup-dev-secret.sh
```

This script generates cryptographically secure random secrets and shows you how to set them.

#### Security Best Practices

- Never commit real secrets to version control
- Use different secrets for development, staging, and production
- Rotate secrets regularly in production
- Use the Encore CLI method for production deployments

## 12. API Design & Integration Standards

### API Endpoint Patterns
```typescript
// Public endpoints (no auth required)
GET /martyrs/:slug              # Get martyr profile
GET /search                     # Search martyrs
GET /categories/:category       # Get category martyrs
GET /timeline                   # Get timeline events
GET /featured                   # Get featured martyrs
GET /filters                    # Get search filters
GET /martyrs/on-this-day        # Get martyrs for current date
GET /martyrs/map-data           # Get martyrs with location data
GET /martyrs/:slug/related      # Get related martyrs

// Admin endpoints (auth required)
# Martyr Management
POST /admin/martyrs             # Create martyr
GET /admin/martyrs              # List all martyrs (admin)
PUT /admin/martyrs/:id          # Update martyr
DELETE /admin/martyrs/:id       # Delete martyr

# Image Management
POST /admin/martyrs/images/upload-url  # Get signed upload URL
POST /admin/martyrs/images      # Add image to martyr
DELETE /admin/martyrs/images/:id # Delete image
POST /admin/martyrs/images/:id/profile # Set as profile image

# Timeline Management
POST /admin/martyrs/timeline    # Add timeline event
DELETE /admin/martyrs/timeline/:id # Delete timeline event

# File Upload System
POST /admin/upload/signed-url   # Generate signed upload URL
POST /admin/upload/direct       # Direct file upload (small files)
GET /admin/upload/files         # List uploaded files
DELETE /admin/upload/file       # Delete uploaded file

# Analytics & Monitoring
GET /admin/charts/views         # Get views over time data

# Authentication
POST /admin/login               # Admin login endpoint
```

### Response Format Standards
```typescript
// Success responses
{ 
  data: T,                      # Actual data
  success: true,                # Success flag
  message?: string              # Optional message
}

// Error responses
{
  error: string,                # Error message
  code: ErrorCode,              # Structured error code
  details?: any                 # Additional error details
}
```

## 13. Accessibility & Internationalization

### Accessibility Requirements
- **WCAG 2.1 AA**: Full compliance required
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Keyboard Navigation**: Full keyboard accessibility