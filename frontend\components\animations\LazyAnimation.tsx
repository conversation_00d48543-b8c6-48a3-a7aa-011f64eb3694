import React, { useState, useEffect, useRef, ReactNode } from 'react';

interface LazyAnimationProps {
  children: ReactNode;
  fallback?: ReactNode;
  rootMargin?: string;
  threshold?: number;
  className?: string;
  triggerOnce?: boolean;
  delay?: number;
}

const LazyAnimation: React.FC<LazyAnimationProps> = ({
  children,
  fallback = null,
  rootMargin = '50px',
  threshold = 0.1,
  className = '',
  triggerOnce = true,
  delay = 0
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          if (delay > 0) {
            setTimeout(() => {
              setIsVisible(true);
              if (triggerOnce) {
                observer.disconnect();
              }
            }, delay);
          } else {
            setIsVisible(true);
            if (triggerOnce) {
              observer.disconnect();
            }
          }
        } else if (!triggerOnce) {
          setIsVisible(false);
        }
      },
      {
        rootMargin,
        threshold
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [rootMargin, threshold, triggerOnce, delay]);

  useEffect(() => {
    if (isVisible && !isLoaded) {
      // Small delay to ensure smooth loading
      const loadTimer = setTimeout(() => {
        setIsLoaded(true);
      }, 50);
      
      return () => clearTimeout(loadTimer);
    }
  }, [isVisible, isLoaded]);

  return (
    <div ref={elementRef} className={className}>
      {isLoaded ? children : fallback}
    </div>
  );
};

export default LazyAnimation;