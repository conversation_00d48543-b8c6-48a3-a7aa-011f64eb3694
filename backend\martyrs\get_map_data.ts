
import { api } from "encore.dev/api";
import { martyrsDB } from "./db";

interface MapDataResponse {
  martyrs: {
    name: string;
    slug: string;
    latitude: number;
    longitude: number;
  }[];
}

// Gets all martyrs with location data for the map.
export const getMapData = api({ expose: true, method: 'GET', path: '/martyrs/map-data' }, async (): Promise<MapDataResponse> => {
  const martyrs = await martyrsDB.rawQueryAll<{
    name: string;
    slug: string;
    latitude: number;
    longitude: number;
  }>(`
    SELECT name, slug, latitude, longitude
    FROM martyrs
    WHERE latitude IS NOT NULL AND longitude IS NOT NULL
  `);

  return { martyrs };
});
